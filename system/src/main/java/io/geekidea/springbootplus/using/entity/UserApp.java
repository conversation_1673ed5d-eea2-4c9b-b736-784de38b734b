package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserApp对象")
@TableName(autoResultMap = true)
public class UserApp extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("PAD关联的学生id 训练相关都传这个id ")
    private Long hdStudentId;

    @ApiModelProperty("PAD关联的球队id 查询指定训练传这个id ")
    @TableField(exist = false)
    private Long hdTeamId;

    @ApiModelProperty("PAD关联的学校id")
    @TableField(exist = false)
    private Long schoolId;

    @ApiModelProperty("PAD关联的学校名")
    @TableField(exist = false)
    private String schoolName;

    @ApiModelProperty("PAD关联的学号")
    @TableField(exist = false)
    private String card;

    @ApiModelProperty("赛事等级1-20")
    private Integer grade;

    @ApiModelProperty("篮球赛事等级1-20")
    private Integer basketballGrade;

    @ApiModelProperty("{grade:17,status:0：审核中 1：审核通过 2：不通过} 足球赛事等级审核状态")
    @TableField(exist = false)
    private JSONObject gradeAudit;

    @ApiModelProperty("{grade:17,status:0：审核中 1：审核通过 2：不通过} 篮球赛事等级审核状态")
    @TableField(exist = false)
    private JSONObject basketballGradeAudit;

    @ApiModelProperty("PAD关联的班级名")
    @TableField(exist = false)
    private String teamName;

    @ApiModelProperty("昵称")
    private String name;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("密码")
    private String passwd;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("身高（厘米）")
    private Double height;

    @ApiModelProperty("体重（kg）")
    private Double weight;

    @ApiModelProperty("出生年月")
    private Date birthday;

    @ApiModelProperty("球鞋尺寸")
    private String size;

    @ApiModelProperty("友盟地址")
    private String deviceTokens;

    @ApiModelProperty("惯用脚")
    private String habit;

    @ApiModelProperty("性别")
    private Integer sex;

    @ApiModelProperty("头像")
    private String headImg;

    @ApiModelProperty("证书")
    private String certificate;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("简介")
    private String introduction;

    @ApiModelProperty("篮球球衣号")
    private Integer shirt;

    @ApiModelProperty("足球球衣号")
    private Integer footballShirt;

    @ApiModelProperty("   账号的版本集合 没有对应版本返回空数组 ['1.1','1.2']\n" +
            "    TeamTag校园数宁体育课\n" +
            "    1.1 标准版\n" +
            "    1.2 高级版\n" +
            "    1.3 旗舰版\n" +
            "    TeamTag智能篮球\n" +
            "    2.1 标准版\n" +
            "    2.2 专业版\n" +
            "    2.3 俱乐部版\n" +
            "    2.4 赛事版\n" +
            "    TeamTag智能足球\n" +
            "    3.1 少儿版\n" +
            "    3.2 青少年版\n" +
            "    3.3 成人版\n" +
            "    3.4 专业版\n" +
            "    3.5 俱乐部版\n" +
            "    3.6 赛事版")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray appVersion;

    @ApiModelProperty("导航文字 APP可自定义JSON数据存入此字段")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray navigationText = new JSONArray();

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Boolean deleted;

    @ApiModelProperty("App学员绑定的设备")
    @TableField(exist = false)
    private HardwareApp hardwareApp;

    @ApiModelProperty("Pad学员绑定的设备1")
    @TableField(exist = false)
    private Hardware hardware;

    @TableField(exist = false)
    @ApiModelProperty("登录成功的token")
    private String token;

    @ApiModelProperty("手机注册验证码")
    @TableField(exist = false)
    private String phoneCode;

    @ApiModelProperty("邮箱注册验证码")
    @TableField(exist = false)
    private String emailCode;

    @ApiModelProperty("用户是否有已同步了数据的体育监测")
    @TableField(exist = false)
    private Boolean haveMonitorDate = false;

    @ApiModelProperty("监测TeamId")
    @TableField(exist = false)
    private Long monitorTeamId = -1l;

    @ApiModelProperty("监测学校id")
    @TableField(exist = false)
    private Long monitorSchollId = -1l;


    @ApiModelProperty("\"football\":足球数量,\"basketball\":篮球数量,\"rest\":其他课数量")
    @TableField(exist = false)
    private JSONObject courseCount;

    @ApiModelProperty("登录or注册类型 1手机号登录 2邮箱登录")
    private Integer loginType=1;

    @ApiModelProperty("校足办id")
    private Long schoolFootballId;

}
