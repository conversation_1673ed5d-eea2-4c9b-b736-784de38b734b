package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.mapper.FieldDataMapper;
import io.geekidea.springbootplus.using.mapper.PersonDataMapper;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.param.FieldDataPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.vo.StuPersonDataAo;
import org.checkerframework.checker.units.qual.A;
import org.checkerframework.checker.units.qual.C;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Slf4j
@Service
public class FieldDataServiceImpl extends BaseServiceImpl<FieldDataMapper, FieldData> implements FieldDataService {

    @Autowired
    private FieldDataMapper fieldDataMapper;
    @Autowired
    private FieldGroupService fieldGroupService;
    @Autowired
    private FieldNotesService fieldNotesService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private PersonDataService personDataService;
    @Autowired
    private PersonDataMapper personDataMapper;
    @Autowired
    private DrillStudentService drillStudentService;
    @Autowired
    private GameStudentService gameStudentService;
    @Autowired
    private HardwareService hardwareService;
    @Autowired
    private HardwareAppService hardwareAppService;
    @Autowired
    private DrillService drillService;
    @Autowired
    private GameService gameService;
    @Autowired
    private UserAppService userAppService;
    @Autowired
    private TeamUserAppService teamUserAppService;



    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveFieldData(FieldData fieldData) throws Exception {
        fieldData.setCreateTime(new Date());
        fieldData.setUpdateTime(new Date());
        return super.save(fieldData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateFieldData(FieldData fieldData) throws Exception {
        FieldNotes fieldNotes = fieldNotesService.getById(fieldData.getFieldId());
        return super.updateById(fieldData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteFieldData(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public boolean deleteByApp(Long fieldId,Long groupId, Long stuId) {
        return update(new LambdaUpdateWrapper<FieldData>().eq(FieldData::getFieldId,fieldId).eq(FieldData::getGroupId,groupId).eq(FieldData::getStuId,stuId).set(FieldData::getAppDel,1));
    }

    @Override
    public Paging<FieldData> getFieldDataPageList(FieldDataPageParam fieldDataPageParam) throws Exception {
        Page<FieldData> page = new PageInfo<>(fieldDataPageParam, OrderItem.desc(getLambdaColumn(FieldData::getCreateTime)));
        LambdaQueryWrapper<FieldData> wrapper = new LambdaQueryWrapper<>();
        IPage<FieldData> iPage = fieldDataMapper.selectPage(page, wrapper);
        return new Paging<FieldData>(iPage);
    }

    @Override
    public List<FieldData> getStuByGroupIdAndFieldId(Long groupId, Long fieldId) {
        List<FieldData> fieldDataList = fieldDataMapper.selectList(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId, fieldId).eq(FieldData::getGroupId, groupId));
        if (fieldDataList.size() > 0) {
            for (FieldData fieldData : fieldDataList) {
                StudentInfo studentInfo = studentInfoService.getStudentInfo(fieldData.getStuId());
                fieldData.setStuName(studentInfo.getName());
            }
        }
        return fieldDataList;
    }

//    @Override
//    public FieldData getByStuId(Long fieldId, Long stuId) {
//        FieldData fieldData = getBaseMapper().selectOne(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId,fieldId).eq(FieldData::getStuId,stuId));
//        return fieldData;
//    }

//    @Override
//    public FieldData getDataList(Long fieldId) {
//        List<FieldData> fieldDataList = getBaseMapper().selectList(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId,fieldId));
//        if(fieldDataList.size()>0){
//
//            for(int i = 0; i < fieldDataList.size();i++){
//                JSONObject jsonObject = new JSONObject();
//                Object onePointer = jsonObject.get("onePointer");
//                Object twoPointer = jsonObject.get("twoPointer");
//                Object threePointer = jsonObject.get("threePointer");
//                Object treeBasket = jsonObject.get("treeBasket");
//                Object assist = jsonObject.get("assist");
//                Object backboard = jsonObject.get("backboard");
//                Object snatch = jsonObject.get("snatch");
//                Object blockShot = jsonObject.get("blockShot");
//                Object walk = jsonObject.get("walk");
//                Object pullPeople = jsonObject.get("pullPeople");
//
//            }
//        }
//        return null;
//    }

    @Override
    public List<FieldData> getStuField(FieldDataPageParam fieldDataPageParam, Long stuId, Long drillId) {
        List<FieldNotes> fieldNotesList = new ArrayList<>();
        if (drillId != null) {
            fieldNotesList = fieldNotesService.getBaseMapper().selectList(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, "DRILL").eq(FieldNotes::getMatchId, drillId));
        }
        Page<FieldData> page = new PageInfo<>(fieldDataPageParam, OrderItem.desc(getLambdaColumn(FieldData::getCreateTime)));
        List<FieldData> fieldDataList = new ArrayList<>();
        if (fieldNotesList.size() > 0) {
            for (FieldNotes fieldNotes : fieldNotesList) {
                FieldData fieldData = fieldDataMapper.getFieldByMatchType(page, stuId, fieldNotes.getId(), "DRILL");
                if (fieldData != null) {
                    fieldData.setType(fieldNotes.getType());
                    fieldDataList.add(fieldData);
                }
            }
        } else if (fieldDataList.size() < 1 && drillId == null) {
            fieldDataList = fieldDataMapper.getStuField(page, stuId).getRecords();
        }
        if (fieldDataList.size() > 0) {
            for (FieldData fieldData : fieldDataList) {
                List<FieldGroup> fieldGroupList = fieldGroupService.getBaseMapper().selectList(new LambdaQueryWrapper<FieldGroup>().eq(FieldGroup::getFieldId, fieldData.getFieldId()));
                fieldData.setFieldGroupList(fieldGroupList);
                StudentInfo studentInfo = studentInfoService.getById(fieldData.getStuId());
                fieldData.setStuName(studentInfo.getName());
            }
        }
        return fieldDataList;
    }

    @Override
    public List<FieldData> getStuField(FieldDataPageParam fieldDataPageParam) {
        List<FieldNotes> fieldNotesList = new ArrayList<>();
        if (fieldDataPageParam.getMatchId() != null) {
            fieldNotesList = fieldNotesService.getBaseMapper().selectList(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, fieldDataPageParam.getMatchType()).eq(FieldNotes::getMatchId, fieldDataPageParam.getMatchId()));
        }
        Page<FieldData> page = new PageInfo<>(fieldDataPageParam, OrderItem.desc(getLambdaColumn(FieldData::getCreateTime)));
        List<FieldData> fieldDataList = new ArrayList<>();
        if (fieldNotesList.size() > 0) {
            for (FieldNotes fieldNotes : fieldNotesList) {
                FieldData fieldData = fieldDataMapper.getFieldByMatchType(page, fieldDataPageParam.getStudentId(), fieldNotes.getId(), fieldDataPageParam.getMatchType());
                if (fieldData != null) {
                    fieldData.setType(fieldNotes.getType());
                    fieldDataList.add(fieldData);
                }
            }
        } else if (fieldDataList.size() < 1 && fieldDataPageParam.getMatchId() == null) {
            fieldDataList = fieldDataMapper.getStuFieldByMatchType(page, fieldDataPageParam.getStudentId(), fieldDataPageParam.getMatchType()).getRecords();
        }
        if (fieldDataList.size() > 0) {
            for (FieldData fieldData : fieldDataList) {
                List<FieldGroup> fieldGroupList = fieldGroupService.getBaseMapper().selectList(new LambdaQueryWrapper<FieldGroup>().eq(FieldGroup::getFieldId, fieldData.getFieldId()));
                fieldData.setFieldGroupList(fieldGroupList);
                if (fieldDataPageParam.getMatchType().equals("DRILL")) {
                    StudentInfo studentInfo = studentInfoService.getById(fieldData.getStuId());
                    fieldData.setStuName(studentInfo.getName());
                } else {
                    Game game = gameService.getById(fieldDataPageParam.getMatchId());
                    UserApp userApp = userAppService.getById(fieldDataPageParam.getStudentId());
                    if (game.getApp()) {
                        fieldData.setStuName(userApp.getName());
                    } else {
                        StudentInfo studentInfo = studentInfoService.getById(userApp.getHdStudentId());
                        if (studentInfo != null) {
                            fieldData.setStuName(studentInfo.getName());
                        }
                    }
                }
            }
        }
        return fieldDataList;
    }

    @Override
    public FieldData findListByIds(Long stuId, Long fieldId, Long teamId) {
        FieldData fieldData = getBaseMapper().selectOne(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId, fieldId).eq(FieldData::getStuId, stuId).eq(FieldData::getGroupId, teamId));
        FieldData fieldData1 = fieldDataMapper.getByStuId(stuId, fieldId);
        JSONObject jsonObject = JSONObject.parseObject(fieldData1.getUpMap());
        fieldData.setDataMap(jsonObject);
        fieldData.setHideMap(JSONObject.parseObject(fieldData1.getOnMap()));
        return fieldData;
    }

    @Override
    public FieldData findListByIdsGame(Long stuId, Long fieldId, Long groupId) {
        FieldData fieldData = getBaseMapper().selectOne(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId, fieldId).eq(FieldData::getStuId, stuId).eq(FieldData::getGroupId, groupId));
        FieldData fieldData1 = fieldDataMapper.getByStuId(stuId, fieldId);
        JSONObject jsonObject = JSONObject.parseObject(fieldData1.getUpMap());
        fieldData.setDataMap(jsonObject);
        fieldData.setHideMap(JSONObject.parseObject(fieldData1.getOnMap()));
        return fieldData;
    }

    @Override
    public Object getPersonal(Long stuId, Long drillId) {
        Drill drill = drillService.getById(drillId);
        JSONObject jsonObject = new JSONObject();
        if (drill.getCourseId() == 3) {
            List<FieldNotes> fieldNotesList = fieldNotesService.list(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, "DRILL").eq(FieldNotes::getMatchId, drillId).orderByDesc(FieldNotes::getCreateTime));
            if (fieldNotesList.size() < 1) {
                return null;
            }
            FieldNotes fieldNotes = fieldNotesList.get(0);
            FieldData fieldData = fieldDataMapper.getByStuId(stuId, fieldNotes.getId());
            fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
            fieldData.setHideMap(JSONObject.parseObject(fieldData.getOnMap()));
            if (fieldData.getDataMap() != null) {
                //计算得分
                String goal = fieldData.getDataMap().getOrDefault("goal",0).toString();
                jsonObject.put("goal", Integer.valueOf(goal));
                //计算助攻
                String assisting = fieldData.getDataMap().getOrDefault("assisting",0).toString();
                jsonObject.put("assisting", Integer.valueOf(assisting));
                //计算抢断数
                String preemption = fieldData.getDataMap().getOrDefault("preemption",0).toString();
                jsonObject.put("preemption", Integer.valueOf(preemption));
                //计算精彩射门
                String excitingShot = fieldData.getDataMap().getOrDefault("excitingShot",0).toString();
                jsonObject.put("excitingShot", Integer.valueOf(excitingShot));
                jsonObject.put("hideMap", fieldData.getHideMap());
            } else {
                jsonObject.put("goal", 0);
                jsonObject.put("assisting", 0);
                jsonObject.put("preemption", 0);
                jsonObject.put("excitingShot", 0);
                jsonObject.put("hideMap", null);
            }
        } else {
            StudentInfo studentInfo = studentInfoService.getById(stuId);
            //跑步排序
            List<StuPersonDataAo> runList = personDataService.runteamList(studentInfo.getTeamId(), drillId, "DRILL");
            if (runList.size() > 0) {
                for (int i = 0; i < runList.size(); i++) {
                    if (stuId.equals(runList.get(i).getStuId())) {
                        jsonObject.put("distance", runList.get(i).getDistance());
                        jsonObject.put("runTop", i + 1);
                    }
                }
            }

            //最大高度排序
            List<StuPersonDataAo> heightList = personDataService.heightteamList(studentInfo.getTeamId(), drillId, "DRILL");
            if (heightList.size() > 0) {
                for (int i = 0; i < heightList.size(); i++) {
                    if (stuId.equals(heightList.get(i).getStuId())) {
                        jsonObject.put("height", heightList.get(i).getMaxHeight());
                        jsonObject.put("heightTop", i + 1);
                    }
                }
            }

            //得分排名
            Map<Long, Integer> scoreMap = new HashMap<>();
            Map<Long, Integer> assistMap = new HashMap<>();
            Map<Long, Integer> backboardMap = new HashMap<>();
            List<FieldNotes> fieldNotesList = fieldNotesService.list(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, "DRILL").eq(FieldNotes::getMatchId, drillId).orderByDesc(FieldNotes::getCreateTime));
            FieldNotes fieldNotes = new FieldNotes();
            if (fieldNotesList.size() > 0) {
                fieldNotes = fieldNotesList.get(0);
            }
            if (fieldNotes == null) {
                jsonObject.put("score", -1);
                jsonObject.put("scoreTop", -1);
                jsonObject.put("assist", -1);
                jsonObject.put("assistTop", -1);
                jsonObject.put("dataMap", null);
                jsonObject.put("hideMap", null);
                fieldNotes = new FieldNotes();
            }
            List<FieldData> fieldDataList = getBaseMapper().getList(fieldNotes.getId());
            for (int i = 0; i < fieldDataList.size(); i++) {
                if (fieldDataList.get(i).getUpMap() != null) {
                    fieldDataList.get(i).setDataMap(JSONObject.parseObject(fieldDataList.get(i).getUpMap()));
                    String onePointer = fieldDataList.get(i).getDataMap().getString("onePointer");
                    String twoPointer = fieldDataList.get(i).getDataMap().getString("twoPointer");
                    String threePointer = fieldDataList.get(i).getDataMap().getString("threePointer");
                    scoreMap.put(fieldDataList.get(i).getStuId(), Integer.valueOf(1 * Integer.valueOf(onePointer) + 2 * Integer.valueOf(twoPointer) + 3 * Integer.valueOf(threePointer)));
                    String assist = fieldDataList.get(i).getDataMap().getString("assist");
                    assistMap.put(fieldDataList.get(i).getStuId(), Integer.valueOf(assist));
                    String frontback = fieldDataList.get(i).getDataMap().getString("frontback");
                    if(frontback==null){
                        frontback = "0";
                    }
                    String afterback = fieldDataList.get(i).getDataMap().getString("afterback");
                    if(afterback==null){
                        afterback = "0";
                    }
                    backboardMap.put(fieldDataList.get(i).getStuId(), Integer.valueOf(frontback)+Integer.valueOf(afterback));
                }
            }

            List<Map.Entry<Long, Integer>> entryList = new ArrayList<>(scoreMap.entrySet());
            Collections.sort(entryList, new Comparator<Map.Entry<Long, Integer>>() {
                @Override
                public int compare(Map.Entry<Long, Integer> o1, Map.Entry<Long, Integer> o2) {
                    return o2.getValue() - o1.getValue();
                }
            });
            for (int i = 0; i < entryList.size(); i++) {
                if (stuId.equals(entryList.get(i).getKey())) {
                    jsonObject.put("score", entryList.get(i).getValue());
                    jsonObject.put("scoreTop", i + 1);
                }
            }
            //助攻排名
            List<Map.Entry<Long, Integer>> assList = new ArrayList<>(assistMap.entrySet());
            Collections.sort(assList, new Comparator<Map.Entry<Long, Integer>>() {
                @Override
                public int compare(Map.Entry<Long, Integer> o1, Map.Entry<Long, Integer> o2) {
                    return o2.getValue() - o1.getValue();
                }
            });
            for (int i = 0; i < assList.size(); i++) {
                if (stuId.equals(assList.get(i).getKey())) {
                    jsonObject.put("assist", assList.get(i).getValue());
                    jsonObject.put("assistTop", i + 1);
                }
            }
            //篮板数据
            List<Map.Entry<Long, Integer>> backList = new ArrayList<>(backboardMap.entrySet());
            Collections.sort(backList, new Comparator<Map.Entry<Long, Integer>>() {
                @Override
                public int compare(Map.Entry<Long, Integer> o1, Map.Entry<Long, Integer> o2) {
                    return o2.getValue() - o1.getValue();
                }
            });
            for (int i = 0; i < backList.size(); i++) {
                if (stuId.equals(backList.get(i).getKey())) {
                    jsonObject.put("backboard", backList.get(i).getValue());
                    jsonObject.put("backboardTop", i + 1);
                }
            }
            //场记数据
            FieldData fieldData = getBaseMapper().getByStuId(stuId, fieldNotes.getId());
            if (fieldData != null) {
                fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
                fieldData.setHideMap(JSONObject.parseObject(fieldData.getOnMap()));
                jsonObject.put("dataMap", fieldData.getDataMap());
                jsonObject.put("hideMap", fieldData.getHideMap());
            } else {
                fieldData = new FieldData();
            }
            if (fieldData.getUpMap() == null) {
                jsonObject.put("score", -1);
                jsonObject.put("scoreTop", -1);
                jsonObject.put("assist", -1);
                jsonObject.put("assistTop", -1);
            }
        }
        return jsonObject;
    }

    @Override
    public Object getPersonalGame(Long stuId, Long gameId) {
        Game game = gameService.getById(gameId);
        UserApp userApp = userAppService.getById(stuId);
        JSONObject jsonObject = new JSONObject();
        if (game.getCourseId() == 3) {
            List<FieldNotes> fieldNotesList = fieldNotesService.list(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, "GAME").eq(FieldNotes::getMatchId, game.getId()).orderByDesc(FieldNotes::getCreateTime));
            if (fieldNotesList.size() < 1) {
                return null;
            }
            FieldNotes fieldNotes = fieldNotesList.get(0);
            FieldData fieldData = fieldDataMapper.getByStuId(stuId, fieldNotes.getId());
            if (fieldData == null) {
                return null;
            }
            fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
            fieldData.setHideMap(JSONObject.parseObject(fieldData.getOnMap()));
            if (fieldData.getDataMap() != null) {
                //计算得分
                String goal = fieldData.getDataMap().getString("goal");
                jsonObject.put("goal", Integer.valueOf(goal));
                //计算助攻
                String assisting = fieldData.getDataMap().getString("assisting");
                jsonObject.put("assisting", Integer.valueOf(assisting));
                //计算抢断数
                String preemption = fieldData.getDataMap().getString("preemption");
                jsonObject.put("preemption", Integer.valueOf(preemption));
                //计算精彩射门
                String excitingShot = fieldData.getDataMap().getString("excitingShot");
                jsonObject.put("excitingShot", Integer.valueOf(excitingShot));
                jsonObject.put("hideMap", fieldData.getHideMap());
            } else {
                jsonObject.put("goal", 0);
                jsonObject.put("assisting", 0);
                jsonObject.put("preemption", 0);
                jsonObject.put("excitingShot", 0);
                jsonObject.put("hideMap", null);
            }
        } else {
            Long teamId = 0l;
            if (game.getApp()) {
                teamId = teamUserAppService.getByUserIdAndGameId(stuId, game);
            } else {
                teamId = userApp.getHdTeamId();
            }
            //跑步排序
            List<StuPersonDataAo> runList = personDataService.runteamList(teamId, gameId, "GAME");
            if (runList.size() > 0) {
                for (int i = 0; i < runList.size(); i++) {
                    if (stuId.equals(runList.get(i).getStuId())) {
                        jsonObject.put("distance", runList.get(i).getDistance());
                        jsonObject.put("runTop", i + 1);
                    }
                }
            }

            //最大高度排序
            List<StuPersonDataAo> heightList = personDataService.heightteamList(teamId, gameId, "GAME");
            if (heightList.size() > 0) {
                for (int i = 0; i < heightList.size(); i++) {
                    if (stuId.equals(heightList.get(i).getStuId())) {
                        jsonObject.put("height", heightList.get(i).getMaxHeight());
                        jsonObject.put("heightTop", i + 1);
                    }
                }
            }

            //得分排名
            Map<Long, Integer> scoreMap = new HashMap<>();
            Map<Long, Integer> assistMap = new HashMap<>();
            Map<Long, Integer> backboardMap = new HashMap<>();
            List<FieldNotes> fieldNotesList = fieldNotesService.list(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, "GAME").eq(FieldNotes::getMatchId, gameId).orderByDesc(FieldNotes::getCreateTime));
            FieldNotes fieldNotes = new FieldNotes();
            if (fieldNotesList.size() > 0) {
                fieldNotes = fieldNotesList.get(0);
            }
            if (fieldNotes == null) {
                jsonObject.put("score", -1);
                jsonObject.put("scoreTop", -1);
                jsonObject.put("assist", -1);
                jsonObject.put("assistTop", -1);
                jsonObject.put("dataMap", null);
                jsonObject.put("hideMap", null);
                fieldNotes = new FieldNotes();
            }
            List<FieldData> fieldDataList = getBaseMapper().getList(fieldNotes.getId());
            for (int i = 0; i < fieldDataList.size(); i++) {
                if (fieldDataList.get(i).getUpMap() != null) {
                    fieldDataList.get(i).setDataMap(JSONObject.parseObject(fieldDataList.get(i).getUpMap()));
                    String onePointer = fieldDataList.get(i).getDataMap().getString("onePointer");
                    String twoPointer = fieldDataList.get(i).getDataMap().getString("twoPointer");
                    String threePointer = fieldDataList.get(i).getDataMap().getString("threePointer");
                    scoreMap.put(fieldDataList.get(i).getStuId(), Integer.valueOf(1 * Integer.valueOf(onePointer) + 2 * Integer.valueOf(twoPointer) + 3 * Integer.valueOf(threePointer)));
                    String assist = fieldDataList.get(i).getDataMap().getString("assist");
                    assistMap.put(fieldDataList.get(i).getStuId(), Integer.valueOf(assist));
                    String frontback = fieldDataList.get(i).getDataMap().getString("frontback");
                    if(frontback==null){
                        frontback = "0";
                    }
                    String afterback = fieldDataList.get(i).getDataMap().getString("afterback");
                    if(afterback==null){
                        afterback = "0";
                    }
                    backboardMap.put(fieldDataList.get(i).getStuId(), Integer.valueOf(frontback)+Integer.valueOf(afterback));
                }
            }

            List<Map.Entry<Long, Integer>> entryList = new ArrayList<>(scoreMap.entrySet());
            Collections.sort(entryList, new Comparator<Map.Entry<Long, Integer>>() {
                @Override
                public int compare(Map.Entry<Long, Integer> o1, Map.Entry<Long, Integer> o2) {
                    return o2.getValue() - o1.getValue();
                }
            });
            for (int i = 0; i < entryList.size(); i++) {
                if (stuId.equals(entryList.get(i).getKey())) {
                    jsonObject.put("score", entryList.get(i).getValue());
                    jsonObject.put("scoreTop", i + 1);
                }
            }
            //助攻排名
            List<Map.Entry<Long, Integer>> assList = new ArrayList<>(assistMap.entrySet());
            Collections.sort(assList, new Comparator<Map.Entry<Long, Integer>>() {
                @Override
                public int compare(Map.Entry<Long, Integer> o1, Map.Entry<Long, Integer> o2) {
                    return o2.getValue() - o1.getValue();
                }
            });
            for (int i = 0; i < assList.size(); i++) {
                if (stuId.equals(assList.get(i).getKey())) {
                    jsonObject.put("assist", assList.get(i).getValue());
                    jsonObject.put("assistTop", i + 1);
                }
            }
            //篮板数据
            List<Map.Entry<Long, Integer>> backList = new ArrayList<>(backboardMap.entrySet());
            Collections.sort(backList, new Comparator<Map.Entry<Long, Integer>>() {
                @Override
                public int compare(Map.Entry<Long, Integer> o1, Map.Entry<Long, Integer> o2) {
                    return o2.getValue() - o1.getValue();
                }
            });
            for (int i = 0; i < backList.size(); i++) {
                if (stuId.equals(backList.get(i).getKey())) {
                    jsonObject.put("backboard", backList.get(i).getValue());
                    jsonObject.put("backboardTop", i + 1);
                }
            }
            //场记数据
            FieldData fieldData = getBaseMapper().getByStuId(stuId, fieldNotes.getId());
            if (fieldData != null) {
                fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
                fieldData.setHideMap(JSONObject.parseObject(fieldData.getOnMap()));
                jsonObject.put("dataMap", fieldData.getDataMap());
                jsonObject.put("hideMap", fieldData.getHideMap());
            } else {
                fieldData = new FieldData();
            }
            if (fieldData.getUpMap() == null) {
                jsonObject.put("score", -1);
                jsonObject.put("scoreTop", -1);
                jsonObject.put("assist", -1);
                jsonObject.put("assistTop", -1);
            }
        }
        return jsonObject;
    }

    @Override
    public Object getTeam(Long teamId, Long drillId) {
        //根据训练id查场记id
        List<FieldNotes> fieldNotesList = fieldNotesService.list(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, "DRILL").eq(FieldNotes::getMatchId, drillId).orderByDesc(FieldNotes::getCreateTime));
        FieldNotes fieldNotes = new FieldNotes();
        List<DrillStudent> drillStudentList = drillStudentService.list(new LambdaQueryWrapper<DrillStudent>().eq(DrillStudent::getTeamId, teamId).eq(DrillStudent::getDeleted, 0).eq(DrillStudent::getDrillId, drillId).eq(DrillStudent::getStatus, 1));
        if (fieldNotesList.size() > 0) {
            fieldNotes = fieldNotesList.get(0);
        }

        if (fieldNotes == null) {
            fieldNotes = new FieldNotes();
            fieldNotes.setId(0L);
        }
        JSONObject jsonObject = new JSONObject();
        if (fieldNotes.getType() != null && fieldNotes.getType() == 1) {
            //足球场记
            Integer sumGoal = 0;
            Integer sumAssist = 0;
            Integer sumPreemption = 0;
            Integer sumExcitingShot = 0;
            List<StuPersonDataAo> goalList = new ArrayList<>();
            if (drillStudentList.size() > 0) {
                for (DrillStudent drillStudent : drillStudentList) {
                    StudentInfo studentInfo = studentInfoService.getById(drillStudent.getStudentId());
                    FieldData fieldData = getBaseMapper().getByStuId(studentInfo.getId(), fieldNotes.getId());
                    StuPersonDataAo stuPersonDataAo = new StuPersonDataAo();
                    if (fieldData != null) {
                        fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
                        fieldData.setHideMap(JSONObject.parseObject(fieldData.getOnMap()));
                        if (fieldData.getDataMap() != null) {
                            //计算得分
                            String goal = fieldData.getDataMap().getString("goal");
                            sumGoal = sumGoal + Integer.valueOf(goal);
                            //计算助攻
                            String assisting = fieldData.getDataMap().getString("assisting");
                            sumAssist = sumAssist + Integer.valueOf(assisting);
                            //计算抢断数
                            String preemption = fieldData.getDataMap().getString("preemption");
                            sumPreemption = sumPreemption + Integer.valueOf(preemption);
                            //计算精彩射门
                            String excitingShot = fieldData.getDataMap().getString("excitingShot");
                            if (excitingShot != null) {
                                sumExcitingShot = sumExcitingShot + Integer.valueOf(excitingShot);
                            }
                        }
                    }
                    stuPersonDataAo.setStuId(studentInfo.getId());
                    stuPersonDataAo.setStuName(studentInfo.getName());
                    stuPersonDataAo.setImage(studentInfo.getHeadImg());
                    stuPersonDataAo.setScore(sumGoal);
                    goalList.add(stuPersonDataAo);
                }
            }
            goalList = goalList.stream().sorted(Comparator.comparing(StuPersonDataAo::getScore).reversed()).collect(Collectors.toList());
            jsonObject.put("sumGoal", sumGoal);
            jsonObject.put("sumAssist", sumAssist);
            jsonObject.put("sumPreemption", sumPreemption);
            jsonObject.put("sumExcitingShot", sumExcitingShot);
            //运动健将
            StuPersonDataAo stuPersonDataAo = goalList.get(0);
            FieldData motionFieldData = getBaseMapper().getByStuId(stuPersonDataAo.getStuId(), fieldNotes.getId());
            if (motionFieldData != null && motionFieldData.getUpMap() != null) {
                motionFieldData.setDataMap(JSONObject.parseObject(motionFieldData.getUpMap()));
                motionFieldData.setHideMap(JSONObject.parseObject(motionFieldData.getOnMap()));
                //进球
                String goal = motionFieldData.getDataMap().getString("goal");
                jsonObject.put("motionGoal", Integer.valueOf(goal));
                //助攻
                String assisting = motionFieldData.getDataMap().getString("assisting");
                jsonObject.put("motionAssist", Integer.valueOf(assisting));
                //抢断
                String preemption = motionFieldData.getDataMap().getString("preemption");
                jsonObject.put("motionPreemption", Integer.valueOf(preemption));
                //精彩射门
                String excitingShot = motionFieldData.getDataMap().getString("excitingShot");
                if (excitingShot != null) {
                    jsonObject.put("motionExcitingShot", Integer.valueOf(excitingShot));
                } else {
                    jsonObject.put("motionExcitingShot", 0);
                }
                jsonObject.put("hideMap", motionFieldData.getHideMap());
            } else {
                jsonObject.put("motionGoal", 0);
                jsonObject.put("motionAssist", 0);
                jsonObject.put("motionPreemption", 0);
                jsonObject.put("hideMap", null);
            }
            jsonObject.put("motionName", stuPersonDataAo.getStuName());
            PersonData da = personDataMapper.getYundong(stuPersonDataAo.getStuId(), drillId, "DRILL");
            if (da != null) {
                jsonObject.put("motionDistance", da.getRunDistance());
            } else {
                jsonObject.put("motionDistance", 0);
            }
            goalField(jsonObject,fieldNotes.getId());

        } else {
            //跑步排序
            List<StuPersonDataAo> runList = personDataService.runteamList(teamId, drillId, "DRILL");
            //最大高度排序
            List<StuPersonDataAo> heightList = personDataService.heightteamList(teamId, drillId, "DRILL");
            //助攻和得分
            List<StuPersonDataAo> assistList = new ArrayList<>();
            List<StuPersonDataAo> scoreList = new ArrayList<>();
            if (drillStudentList.size() > 0) {
                for (DrillStudent drillStudent : drillStudentList) {
                    StudentInfo studentInfo = studentInfoService.getById(drillStudent.getStudentId());
                    List<Hardware> hardware = hardwareService.list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getTeamId, teamId).eq(Hardware::getStudentId, studentInfo.getId()));
                    if (hardware.size() > 0) {
                        Integer asssum = 0;
                        Integer scosum = 0;
                        StuPersonDataAo asstuPersonDataAo = new StuPersonDataAo();
                        StuPersonDataAo scstuPersonDataAo = new StuPersonDataAo();
                        FieldData fieldData = getBaseMapper().getByStuId(studentInfo.getId(), fieldNotes.getId());
                        if (fieldData != null) {
                            fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
                            if (fieldData.getDataMap() != null) {
                                //助攻次数
                                String assist = fieldData.getDataMap().getString("assist");
                                asssum = asssum + Integer.valueOf(assist);
                                //得分
                                String onePointer = fieldData.getDataMap().getString("onePointer");
                                String twoPointer = fieldData.getDataMap().getString("twoPointer");
                                String threePointer = fieldData.getDataMap().getString("threePointer");
                                scosum = scosum + 1 * Integer.valueOf(onePointer) + 2 * Integer.valueOf(twoPointer) + 3 * Integer.valueOf(threePointer);
                            }
                        }

                        //助攻
                        asstuPersonDataAo.setStuId(studentInfo.getId());
                        asstuPersonDataAo.setStuName(studentInfo.getName());
                        asstuPersonDataAo.setImage(studentInfo.getHeadImg());
                        asstuPersonDataAo.setAssist(asssum);
                        assistList.add(asstuPersonDataAo);
                        //得分
                        scstuPersonDataAo.setStuId(studentInfo.getId());
                        scstuPersonDataAo.setStuName(studentInfo.getName());
                        scstuPersonDataAo.setImage(studentInfo.getHeadImg());
                        scstuPersonDataAo.setScore(scosum);
                        scoreList.add(scstuPersonDataAo);
                    }
                }
            }

            //助攻排序
            assistList = assistList.stream().sorted(Comparator.comparing(StuPersonDataAo::getAssist).reversed()).collect(Collectors.toList());
            //得分排序
            scoreList = scoreList.stream().sorted(Comparator.comparing(StuPersonDataAo::getScore).reversed()).collect(Collectors.toList());

            //4条数据
            runList = runList.stream().limit(4).collect(Collectors.toList());
            heightList = heightList.stream().limit(4).collect(Collectors.toList());
            assistList = assistList.stream().limit(4).collect(Collectors.toList());
            scoreList = scoreList.stream().limit(4).collect(Collectors.toList());

            jsonObject.put("runList", runList);
            jsonObject.put("heightList", heightList);
            jsonObject.put("assistList", assistList);
            jsonObject.put("scoreList", scoreList);
            //场记统计
            Integer onePointer = 0;
            Integer twoPointer = 0;
            Integer threePointer = 0;
            Integer treeBasket = 0;
            Integer assist = 0;
            Integer backboard = 0;
            Integer snatch = 0;
            Integer blockShot = 0;
            Integer walk = 0;
            Integer pullPeople = 0;
            Integer walkBall = 0;
            Integer threeViolation = 0;
            Integer illegalDribble = 0;
            Integer intentionalBall = 0;
            Integer hookFoul = 0;
            Integer dribbler = 0;
            Integer outBall = 0;
            Integer illegalHands = 0;
            Integer headFoul = 0;
            Integer flyingElbow = 0;
            Integer illegalAttack = 0;
            Integer illegalDefense = 0;
            Integer pushPepole = 0;
            Integer penaltyExit = 0;
            Integer technicalfoul = 0;
            Integer violationfoul = 0;
            Integer comeback = 0;
            Integer amazing = 0;
            Integer nicepass = 0;
            Integer frontback = 0;
            Integer afterback = 0;
            Integer nullone = 0;
            Integer nulltwo = 0;
            Integer nullthree = 0;
            Integer dunk = 0;
            Integer helpball = 0;
            List<FieldData> fieldDataList = getBaseMapper().getList(fieldNotes.getId());
            if (fieldDataList.size() > 0) {
                for (FieldData fieldData : fieldDataList) {
                    fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
                    if (fieldData.getDataMap() != null) {
                        String onePointer1 = fieldData.getDataMap().getString("onePointer");
                        if (onePointer1 != null) {
                            onePointer = onePointer + Integer.valueOf(onePointer1);
                        }
                        String twoPointer1 = fieldData.getDataMap().getString("twoPointer");
                        if (twoPointer1 != null) {
                            twoPointer = twoPointer + Integer.valueOf(twoPointer1);
                        }
                        String threePointer1 = fieldData.getDataMap().getString("threePointer");
                        if (threePointer1 != null) {
                            threePointer = threePointer + Integer.valueOf(threePointer1);
                        }
                        String treeBasket1 = fieldData.getDataMap().getString("treeBasket");
                        if (treeBasket1 != null) {
                            treeBasket = treeBasket + Integer.valueOf(treeBasket1);
                        }
                        String assist1 = fieldData.getDataMap().getString("assist");
                        if (assist1 != null) {
                            assist = assist + Integer.valueOf(assist1);
                        }
                        String snatch1 = fieldData.getDataMap().getString("snatch");
                        if (snatch1 != null) {
                            snatch = snatch + Integer.valueOf(snatch1);
                        }
                        String blockShot1 = fieldData.getDataMap().getString("blockShot");
                        if (blockShot1 != null) {
                            blockShot = blockShot + Integer.valueOf(blockShot1);
                        }
                        String walk1 = fieldData.getDataMap().getString("walk");
                        if (walk1 != null) {
                            walk = walk + Integer.valueOf(walk1);
                        }
                        String pullPeople1 = fieldData.getDataMap().getString("pullPeople");
                        if (pullPeople1 != null) {
                            pullPeople = pullPeople + Integer.valueOf(pullPeople1);
                        }
                        String walkBall1 = fieldData.getDataMap().getString("walkBall");
                        if (walkBall1 != null) {
                            walkBall = walkBall + Integer.valueOf(walkBall1);
                        }
                        String threeViolation1 = fieldData.getDataMap().getString("threeViolation");
                        if (threeViolation1 != null) {
                            threeViolation = threeViolation + Integer.valueOf(threeViolation1);
                        }
                        String illegalDribble1 = fieldData.getDataMap().getString("illegalDribble");
                        if (illegalDribble1 != null) {
                            illegalDribble = illegalDribble + Integer.valueOf(illegalDribble1);
                        }
                        String intentionalBall1 = fieldData.getDataMap().getString("intentionalBall");
                        if (intentionalBall1 != null) {
                            intentionalBall = intentionalBall + Integer.valueOf(intentionalBall1);
                        }
                        String hookFoul1 = fieldData.getDataMap().getString("hookFoul");
                        if (hookFoul1 != null) {
                            hookFoul = hookFoul + Integer.valueOf(hookFoul1);
                        }
                        String dribbler1 = fieldData.getDataMap().getString("dribbler");
                        if (dribbler1 != null) {
                            dribbler = dribbler + Integer.valueOf(dribbler1);
                        }
                        String outBall1 = fieldData.getDataMap().getString("outBall");
                        if (outBall1 != null) {
                            outBall = outBall + Integer.valueOf(outBall1);
                        }
                        String illegalHands1 = fieldData.getDataMap().getString("illegalHands");
                        if (illegalHands1 != null) {
                            illegalHands = illegalHands + Integer.valueOf(illegalHands1);
                        }
                        String headFoul1 = fieldData.getDataMap().getString("headFoul");
                        if (headFoul1 != null) {
                            headFoul = headFoul + Integer.valueOf(headFoul1);
                        }
                        String flyingElbow1 = fieldData.getDataMap().getString("flyingElbow");
                        if (flyingElbow1 != null) {
                            flyingElbow = flyingElbow + Integer.valueOf(flyingElbow1);
                        }
                        String illegalAttack1 = fieldData.getDataMap().getString("illegalAttack");
                        if (illegalAttack1 != null) {
                            illegalAttack = illegalAttack + Integer.valueOf(illegalAttack1);
                        }
                        String illegalDefense1 = fieldData.getDataMap().getString("illegalDefense");
                        if (illegalDefense1 != null) {
                            illegalDefense = illegalDefense + Integer.valueOf(illegalDefense1);
                        }
                        String pushPepole1 = fieldData.getDataMap().getString("pushPepole");
                        if (pushPepole1 != null) {
                            pushPepole = pushPepole + Integer.valueOf(pushPepole1);
                        }
                        String penaltyExit1 = fieldData.getDataMap().getString("penaltyExit");
                        if (penaltyExit1 != null) {
                            penaltyExit = penaltyExit + Integer.valueOf(penaltyExit1);
                        }
                        String technicalfoul1 = fieldData.getDataMap().getString("technicalfoul");
                        if (technicalfoul1 != null) {
                            technicalfoul = technicalfoul + Integer.valueOf(technicalfoul1);
                        }
                        String violationfoul1 = fieldData.getDataMap().getString("violationfoul");
                        if (violationfoul1 != null) {
                            violationfoul = violationfoul + Integer.valueOf(violationfoul1);
                        }
                        String comeback1 = fieldData.getDataMap().getString("comeback");
                        if (comeback1 != null) {
                            comeback = comeback + Integer.valueOf(comeback1);
                        }
                        String amazing1 = fieldData.getDataMap().getString("amazing");
                        if (amazing1 != null) {
                            amazing = amazing + Integer.valueOf(amazing1);
                        }
                        String nicepass1 = fieldData.getDataMap().getString("nicepass");
                        if (nicepass1 != null) {
                            nicepass = nicepass + Integer.valueOf(nicepass1);
                        }
                        String frontback1 = fieldData.getDataMap().getString("frontback");
                        if(frontback1 != null){
                            frontback = frontback + Integer.valueOf(frontback1);
                        }
                        String afterback1 = fieldData.getDataMap().getString("afterback");
                        if(afterback1 != null){
                            afterback = afterback + Integer.valueOf(afterback1);
                        }
                        String backboard1 = fieldData.getDataMap().getString("backboard");
                        if (backboard1 != null) {
                            backboard = backboard + frontback + afterback;
                        }
                        String nullone1 = fieldData.getDataMap().getString("nullone");
                        if(nullone1 != null){
                            nullone = nullone + Integer.valueOf(nullone1);
                        }
                        String nulltwo1 = fieldData.getDataMap().getString("nulltwo");
                        if(nulltwo1 != null){
                            nulltwo = nulltwo + Integer.valueOf(nulltwo1);
                        }
                        String nullthree1 = fieldData.getDataMap().getString("nullthree");
                        if(nullthree1 != null){
                            nullthree = nullthree + Integer.valueOf(nullthree1);
                        }
                        String dunk1 = fieldData.getDataMap().getString("dunk");
                        if(dunk1 != null){
                            dunk = dunk + Integer.valueOf(dunk1);
                        }
                        String helpball1 = fieldData.getDataMap().getString("helpball");
                        if(helpball1 != null){
                            helpball = helpball + Integer.valueOf(helpball1);
                        }

                    }
                }
                jsonObject.put("hideMap", JSONObject.parseObject(fieldDataList.get(0).getOnMap()));
            }

            JSONObject dataMap = new JSONObject();
            dataMap.put("onePointer", onePointer);
            dataMap.put("twoPointer", twoPointer);
            dataMap.put("threePointer", threePointer);
            dataMap.put("treeBasket", treeBasket);
            dataMap.put("assist", assist);
            dataMap.put("backboard", backboard);
            dataMap.put("snatch", snatch);
            dataMap.put("blockShot", blockShot);
            dataMap.put("walk", walk);
            dataMap.put("pullPeople", pullPeople);
            dataMap.put("walkBall", walkBall);
            dataMap.put("threeViolation", threeViolation);
            dataMap.put("illegalDribble", illegalDribble);
            dataMap.put("intentionalBall", intentionalBall);
            dataMap.put("hookFoul", hookFoul);
            dataMap.put("dribbler", dribbler);
            dataMap.put("outBall", outBall);
            dataMap.put("illegalHands", illegalHands);
            dataMap.put("headFoul", headFoul);
            dataMap.put("flyingElbow", flyingElbow);
            dataMap.put("illegalAttack", illegalAttack);
            dataMap.put("illegalDefense", illegalDefense);
            dataMap.put("pushPepole", pushPepole);
            dataMap.put("penaltyExit", penaltyExit);
            dataMap.put("technicalfoul", technicalfoul);
            dataMap.put("violationfoul", violationfoul);
            dataMap.put("comeback", comeback);
            dataMap.put("amazing", amazing);
            dataMap.put("nicepass", nicepass);
            dataMap.put("frontback",frontback);
            dataMap.put("afterback",afterback);
            dataMap.put("nullone",nullone);
            dataMap.put("nulltwo",nulltwo);
            dataMap.put("nullthree",nullthree);
            dataMap.put("dunk",dunk);
            dataMap.put("helpball",helpball);
            FieldData fieldData = new FieldData();
            fieldData.setDataMap(dataMap);
            jsonObject.put("dataMap", fieldData.getDataMap());
            jsonObject.put("sumAssist", assist);
            jsonObject.put("sumBackboard", frontback+afterback);
            Integer sunScore = onePointer * 1 + twoPointer * 2 + threePointer * 3;
            jsonObject.put("sumScore", sunScore);
            //运动健将
            StuPersonDataAo stuPersonDataAo1 = scoreList.get(0);
            FieldData motionFieldData = getBaseMapper().getByStuId(stuPersonDataAo1.getStuId(), fieldNotes.getId());
            if (motionFieldData != null && motionFieldData.getUpMap() != null) {
                motionFieldData.setDataMap(JSONObject.parseObject(motionFieldData.getUpMap()));
                //助攻次数
                String motionAssist = motionFieldData.getDataMap().getString("assist");
                jsonObject.put("motionAssist", Integer.valueOf(motionAssist));
                //篮板
//                String motionBoard = motionFieldData.getDataMap().getString("backboard");
                String frontback1 = motionFieldData.getDataMap().getString("frontback");
                Integer frontback2 = 0;
                if(frontback1 != null){
                    frontback2 = Integer.valueOf(frontback1);
                }
                String afterback1 = motionFieldData.getDataMap().getString("afterback");
                Integer afterback2 = 0;
                if(afterback1 != null){
                    afterback2 = Integer.valueOf(afterback1);
                }
                jsonObject.put("motionBoard", frontback2+afterback2);
                //抢断
                String motionPreemption = motionFieldData.getDataMap().getString("snatch");
                jsonObject.put("motionPreemption", Integer.valueOf(motionPreemption));
            } else {
                jsonObject.put("motionAssist", 0);
                jsonObject.put("motionBoard", 0);
                jsonObject.put("motionPreemption", 0);
            }
            jsonObject.put("motionName", stuPersonDataAo1.getStuName());
            jsonObject.put("motionScore", stuPersonDataAo1.getScore());
            PersonData da = personDataMapper.getYundong(stuPersonDataAo1.getStuId(), drillId, "DRILL");
            if (da != null) {
                jsonObject.put("motionDistance", da.getRunDistance());
            } else {
                jsonObject.put("motionDistance", 0);
            }
        }
        //总和(步数+跑动距离+热量)
        StuPersonDataAo stuPersonDataAo = personDataService.sumCalculation(drillId, "DRILL");
        if(stuPersonDataAo!=null) {
            jsonObject.put("sumSteps", stuPersonDataAo.getSumSteps());
            jsonObject.put("sumRun", stuPersonDataAo.getSumRun());
            jsonObject.put("sumCalorie", stuPersonDataAo.getSumCalorie());
        }
        return jsonObject;
    }

    @Override
    public Object getTeamGame(Long teamId, Long gameId) {
        Game game = gameService.getById(gameId);
        //根据训练id查场记id
        List<FieldNotes> fieldNotesList = fieldNotesService.list(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, "GAME").eq(FieldNotes::getMatchId, gameId).orderByDesc(FieldNotes::getCreateTime));
        FieldNotes fieldNotes = new FieldNotes();
        List<GameStudent> gameStudents = gameStudentService.list(new LambdaQueryWrapper<GameStudent>().eq(GameStudent::getTeamId, teamId).eq(GameStudent::getDeleted, 0).eq(GameStudent::getGameId, gameId).eq(GameStudent::getStatus, 1));
        if (fieldNotesList.size() > 0) {
            fieldNotes = fieldNotesList.get(0);
        }
        if (gameStudents.size() == 0) {
            return null;
        }
        if (fieldNotes == null) {
            fieldNotes = new FieldNotes();
            fieldNotes.setId(0L);
        }
        JSONObject jsonObject = new JSONObject();
        if (fieldNotes.getType() != null && fieldNotes.getType() == 1) {
            //足球场记
            Integer sumGoal = 0;
            Integer sumAssist = 0;
            Integer sumPreemption = 0;
            Integer sumExcitingShot = 0;
            List<StuPersonDataAo> goalList = new ArrayList<>();
            if (gameStudents.size() > 0) {
                for (GameStudent gameStudent : gameStudents) {
                    UserApp userApp = userAppService.getById(gameStudent.getStudentId());
                    Long stuId = 0l;
                    if (game.getApp()) {
                        stuId = userApp.getId();
                    } else {
                        stuId = userApp.getHdStudentId();
                    }
                    FieldData fieldData = getBaseMapper().getByStuId(stuId, fieldNotes.getId());
                    StuPersonDataAo stuPersonDataAo = new StuPersonDataAo();
                    if (fieldData != null) {
                        fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
                        fieldData.setHideMap(JSONObject.parseObject(fieldData.getOnMap()));
                        if (fieldData.getDataMap() != null) {
                            //计算得分
                            String goal = fieldData.getDataMap().getString("goal");
                            sumGoal = sumGoal + Integer.valueOf(goal);
                            //计算助攻
                            String assisting = fieldData.getDataMap().getString("assisting");
                            sumAssist = sumAssist + Integer.valueOf(assisting);
                            //计算抢断数
                            String preemption = fieldData.getDataMap().getString("preemption");
                            sumPreemption = sumPreemption + Integer.valueOf(preemption);
                            //计算精彩射门
                            String excitingShot = fieldData.getDataMap().getString("excitingShot");
                            if (excitingShot != null) {
                                sumExcitingShot = sumExcitingShot + Integer.valueOf(excitingShot);
                            }
                        }
                    }
                    stuPersonDataAo.setStuId(userApp.getId());
                    stuPersonDataAo.setStuName(userApp.getName());
                    stuPersonDataAo.setImage(userApp.getHeadImg());
                    stuPersonDataAo.setScore(sumGoal);
                    goalList.add(stuPersonDataAo);
                }
            }
            goalList = goalList.stream().sorted(Comparator.comparing(StuPersonDataAo::getScore).reversed()).collect(Collectors.toList());
            jsonObject.put("sumGoal", sumGoal);
            jsonObject.put("sumAssist", sumAssist);
            jsonObject.put("sumPreemption", sumPreemption);
            jsonObject.put("sumExcitingShot", sumExcitingShot);
            //运动健将
            StuPersonDataAo stuPersonDataAo = goalList.get(0);
            FieldData motionFieldData = getBaseMapper().getByStuId(stuPersonDataAo.getStuId(), fieldNotes.getId());
            if (motionFieldData != null && motionFieldData.getUpMap() != null) {
                motionFieldData.setDataMap(JSONObject.parseObject(motionFieldData.getUpMap()));
                motionFieldData.setHideMap(JSONObject.parseObject(motionFieldData.getOnMap()));
                //进球
                String goal = motionFieldData.getDataMap().getString("goal");
                jsonObject.put("motionGoal", Integer.valueOf(goal));
                //助攻
                String assisting = motionFieldData.getDataMap().getString("assisting");
                jsonObject.put("motionAssist", Integer.valueOf(assisting));
                //抢断
                String preemption = motionFieldData.getDataMap().getString("preemption");
                jsonObject.put("motionPreemption", Integer.valueOf(preemption));
                //精彩射门
                String excitingShot = motionFieldData.getDataMap().getString("excitingShot");
                if (excitingShot != null) {
                    jsonObject.put("motionExcitingShot", Integer.valueOf(excitingShot));
                } else {
                    jsonObject.put("motionExcitingShot", 0);
                }
                jsonObject.put("hideMap", motionFieldData.getHideMap());
            } else {
                jsonObject.put("motionGoal", 0);
                jsonObject.put("motionAssist", 0);
                jsonObject.put("motionPreemption", 0);
                jsonObject.put("hideMap", null);
            }
            jsonObject.put("motionName", stuPersonDataAo.getStuName());
            PersonData da = personDataMapper.getYundong(stuPersonDataAo.getStuId(), gameId, "GAME");
            if (da != null) {
                jsonObject.put("motionDistance", da.getRunDistance());
            } else {
                jsonObject.put("motionDistance", 0);
            }
            goalField(jsonObject,fieldNotes.getId());

        } else {
            //跑步排序
            List<StuPersonDataAo> runList = personDataService.runteamList(teamId, gameId, "GAME");
            //最大高度排序
            List<StuPersonDataAo> heightList = personDataService.heightteamList(teamId, gameId, "GAME");
            //助攻和得分
            List<StuPersonDataAo> assistList = new ArrayList<>();
            List<StuPersonDataAo> scoreList = new ArrayList<>();
            if (gameStudents.size() > 0) {
                for (GameStudent gameStudent : gameStudents) {
                    UserApp userApp = userAppService.getById(gameStudent.getStudentId());
                    int hardwareSize = 0;
                    if (game.getApp()) {
                        List<HardwareApp> hardware = hardwareAppService.list(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getStudentId, userApp.getId()));
                        hardwareSize = hardware.size();
                    } else {
                        List<Hardware> hardware = hardwareService.list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getTeamId, teamId).eq(Hardware::getStudentId, userApp.getId()));
                        hardwareSize = hardware.size();
                    }
                    if (hardwareSize > 0) {
                        Integer asssum = 0;
                        Integer scosum = 0;
                        StuPersonDataAo asstuPersonDataAo = new StuPersonDataAo();
                        StuPersonDataAo scstuPersonDataAo = new StuPersonDataAo();
                        FieldData fieldData = getBaseMapper().getByStuId(userApp.getId(), fieldNotes.getId());
                        if (fieldData != null) {
                            fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
                            if (fieldData.getDataMap() != null) {
                                //助攻次数
                                String assist = fieldData.getDataMap().getString("assist");
                                asssum = asssum + Integer.valueOf(assist);
                                //得分
                                String onePointer = fieldData.getDataMap().getString("onePointer");
                                String twoPointer = fieldData.getDataMap().getString("twoPointer");
                                String threePointer = fieldData.getDataMap().getString("threePointer");
                                scosum = scosum + 1 * Integer.valueOf(onePointer) + 2 * Integer.valueOf(twoPointer) + 3 * Integer.valueOf(threePointer);
                            }
                        }

                        //助攻
                        asstuPersonDataAo.setStuId(userApp.getId());
                        asstuPersonDataAo.setStuName(userApp.getName());
                        asstuPersonDataAo.setImage(userApp.getHeadImg());
                        asstuPersonDataAo.setAssist(asssum);
                        assistList.add(asstuPersonDataAo);
                        //得分
                        scstuPersonDataAo.setStuId(userApp.getId());
                        scstuPersonDataAo.setStuName(userApp.getName());
                        scstuPersonDataAo.setImage(userApp.getHeadImg());
                        scstuPersonDataAo.setScore(scosum);
                        scoreList.add(scstuPersonDataAo);
                    }
                }
            }

            //助攻排序
            assistList = assistList.stream().sorted(Comparator.comparing(StuPersonDataAo::getAssist).reversed()).collect(Collectors.toList());
            //得分排序
            scoreList = scoreList.stream().sorted(Comparator.comparing(StuPersonDataAo::getScore).reversed()).collect(Collectors.toList());

            //4条数据
            runList = runList.stream().limit(4).collect(Collectors.toList());
            heightList = heightList.stream().limit(4).collect(Collectors.toList());
            assistList = assistList.stream().limit(4).collect(Collectors.toList());
            scoreList = scoreList.stream().limit(4).collect(Collectors.toList());

            jsonObject.put("runList", runList);
            jsonObject.put("heightList", heightList);
            jsonObject.put("assistList", assistList);
            jsonObject.put("scoreList", scoreList);
            //场记统计
            Integer onePointer = 0;
            Integer twoPointer = 0;
            Integer threePointer = 0;
            Integer treeBasket = 0;
            Integer assist = 0;
            Integer backboard = 0;
            Integer snatch = 0;
            Integer blockShot = 0;
            Integer walk = 0;
            Integer pullPeople = 0;
            Integer walkBall = 0;
            Integer threeViolation = 0;
            Integer illegalDribble = 0;
            Integer intentionalBall = 0;
            Integer hookFoul = 0;
            Integer dribbler = 0;
            Integer outBall = 0;
            Integer illegalHands = 0;
            Integer headFoul = 0;
            Integer flyingElbow = 0;
            Integer illegalAttack = 0;
            Integer illegalDefense = 0;
            Integer pushPepole = 0;
            Integer penaltyExit = 0;
            Integer technicalfoul = 0;
            Integer violationfoul = 0;
            Integer comeback = 0;
            Integer amazing = 0;
            Integer nicepass = 0;
            Integer frontback = 0;
            Integer afterback = 0;
            Integer nullone = 0;
            Integer nulltwo = 0;
            Integer nullthree = 0;
            Integer dunk = 0;
            Integer helpball = 0;
            List<FieldData> fieldDataList = getBaseMapper().getList(fieldNotes.getId());
            if (fieldDataList.size() > 0) {
                for (FieldData fieldData : fieldDataList) {
                    fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
                    if (fieldData.getDataMap() != null) {
                        String onePointer1 = fieldData.getDataMap().getString("onePointer");
                        onePointer = onePointer + Integer.valueOf(onePointer1);
                        String twoPointer1 = fieldData.getDataMap().getString("twoPointer");
                        twoPointer = twoPointer + Integer.valueOf(twoPointer1);
                        String threePointer1 = fieldData.getDataMap().getString("threePointer");
                        threePointer = threePointer + Integer.valueOf(threePointer1);
                        String treeBasket1 = fieldData.getDataMap().getString("treeBasket");
                        treeBasket = treeBasket + Integer.valueOf(treeBasket1);
                        String assist1 = fieldData.getDataMap().getString("assist");
                        assist = assist + Integer.valueOf(assist1);
                        String snatch1 = fieldData.getDataMap().getString("snatch");
                        snatch = snatch + Integer.valueOf(snatch1);
                        String blockShot1 = fieldData.getDataMap().getString("blockShot");
                        blockShot = blockShot + Integer.valueOf(blockShot1);
                        String walk1 = fieldData.getDataMap().getString("walk");
                        walk = walk + Integer.valueOf(walk1);
                        String pullPeople1 = fieldData.getDataMap().getString("pullPeople");
                        pullPeople = pullPeople + Integer.valueOf(pullPeople1);
                        String walkBall1 = fieldData.getDataMap().getString("walkBall");
                        walkBall = walkBall + Integer.valueOf(walkBall1);
                        String threeViolation1 = fieldData.getDataMap().getString("threeViolation");
                        threeViolation = threeViolation + Integer.valueOf(threeViolation1);
                        String illegalDribble1 = fieldData.getDataMap().getString("illegalDribble");
                        illegalDribble = illegalDribble + Integer.valueOf(illegalDribble1);
                        String intentionalBall1 = fieldData.getDataMap().getString("intentionalBall");
                        intentionalBall = intentionalBall + Integer.valueOf(intentionalBall1);
                        String hookFoul1 = fieldData.getDataMap().getString("hookFoul");
                        hookFoul = hookFoul + Integer.valueOf(hookFoul1);
                        String dribbler1 = fieldData.getDataMap().getString("dribbler");
                        dribbler = dribbler + Integer.valueOf(dribbler1);
                        String outBall1 = fieldData.getDataMap().getString("outBall");
                        outBall = outBall + Integer.valueOf(outBall1);
                        String illegalHands1 = fieldData.getDataMap().getString("illegalHands");
                        illegalHands = illegalHands + Integer.valueOf(illegalHands1);
                        String headFoul1 = fieldData.getDataMap().getString("headFoul");
                        headFoul = headFoul + Integer.valueOf(headFoul1);
                        String flyingElbow1 = fieldData.getDataMap().getString("flyingElbow");
                        flyingElbow = flyingElbow + Integer.valueOf(flyingElbow1);
                        String illegalAttack1 = fieldData.getDataMap().getString("illegalAttack");
                        illegalAttack = illegalAttack + Integer.valueOf(illegalAttack1);
                        String illegalDefense1 = fieldData.getDataMap().getString("illegalDefense");
                        illegalDefense = illegalDefense + Integer.valueOf(illegalDefense1);
                        String pushPepole1 = fieldData.getDataMap().getString("pushPepole");
                        pushPepole = pushPepole + Integer.valueOf(pushPepole1);
                        String penaltyExit1 = fieldData.getDataMap().getString("penaltyExit");
                        penaltyExit = penaltyExit + Integer.valueOf(penaltyExit1);
                        String technicalfoul1 = fieldData.getDataMap().getString("technicalfoul");
                        if (technicalfoul1 != null) {
                            technicalfoul = technicalfoul + Integer.valueOf(technicalfoul1);
                        }
                        String violationfoul1 = fieldData.getDataMap().getString("violationfoul");
                        if (violationfoul1 != null) {
                            violationfoul = violationfoul + Integer.valueOf(violationfoul1);
                        }
                        String comeback1 = fieldData.getDataMap().getString("comeback");
                        if (comeback1 != null) {
                            comeback = comeback + Integer.valueOf(comeback1);
                        }
                        String amazing1 = fieldData.getDataMap().getString("amazing");
                        if (amazing1 != null) {
                            amazing = amazing + Integer.valueOf(amazing1);
                        }
                        String nicepass1 = fieldData.getDataMap().getString("nicepass");
                        if (nicepass1 != null) {
                            nicepass = nicepass + Integer.valueOf(nicepass1);
                        }
                        String frontback1 = fieldData.getDataMap().getString("frontback");
                        if(frontback1 != null){
                            frontback = frontback + Integer.valueOf(frontback1);
                        }
                        String afterback1 = fieldData.getDataMap().getString("afterback");
                        if(afterback1 != null){
                            afterback = afterback + Integer.valueOf(afterback1);
                        }
                        String backboard1 = fieldData.getDataMap().getString("backboard");
                        if(backboard1 != null){
                            backboard = backboard + frontback + afterback;
                        }
                        String nullone1 = fieldData.getDataMap().getString("nullone");
                        if(nullone1 != null){
                            nullone = nullone + Integer.valueOf(nullone1);
                        }
                        String nulltwo1 = fieldData.getDataMap().getString("nulltwo");
                        if(nulltwo1 != null){
                            nulltwo = nulltwo + Integer.valueOf(nulltwo1);
                        }
                        String nullthree1 = fieldData.getDataMap().getString("nullthree");
                        if(nullthree1 != null){
                            nullthree = nullthree + Integer.valueOf(nullthree1);
                        }
                        String dunk1 = fieldData.getDataMap().getString("dunk");
                        if(dunk1 != null){
                            dunk = dunk + Integer.valueOf(dunk1);
                        }
                        String helpball1 = fieldData.getDataMap().getString("helpball");
                        if(helpball1 != null){
                            helpball = helpball + Integer.valueOf(helpball1);
                        }

                    }
                }
                jsonObject.put("hideMap", JSONObject.parseObject(fieldDataList.get(0).getOnMap()));
            }

            JSONObject dataMap = new JSONObject();
            dataMap.put("onePointer", onePointer);
            dataMap.put("twoPointer", twoPointer);
            dataMap.put("threePointer", threePointer);
            dataMap.put("treeBasket", treeBasket);
            dataMap.put("assist", assist);
            dataMap.put("backboard", backboard);
            dataMap.put("snatch", snatch);
            dataMap.put("blockShot", blockShot);
            dataMap.put("walk", walk);
            dataMap.put("pullPeople", pullPeople);
            dataMap.put("walkBall", walkBall);
            dataMap.put("threeViolation", threeViolation);
            dataMap.put("illegalDribble", illegalDribble);
            dataMap.put("intentionalBall", intentionalBall);
            dataMap.put("hookFoul", hookFoul);
            dataMap.put("dribbler", dribbler);
            dataMap.put("outBall", outBall);
            dataMap.put("illegalHands", illegalHands);
            dataMap.put("headFoul", headFoul);
            dataMap.put("flyingElbow", flyingElbow);
            dataMap.put("illegalAttack", illegalAttack);
            dataMap.put("illegalDefense", illegalDefense);
            dataMap.put("pushPepole", pushPepole);
            dataMap.put("penaltyExit", penaltyExit);
            dataMap.put("technicalfoul", technicalfoul);
            dataMap.put("violationfoul", violationfoul);
            dataMap.put("comeback", comeback);
            dataMap.put("amazing", amazing);
            dataMap.put("nicepass", nicepass);
            dataMap.put("frontback",frontback);
            dataMap.put("afterback",afterback);
            dataMap.put("nullone",nullone);
            dataMap.put("nulltwo",nulltwo);
            dataMap.put("nullthree",nullthree);
            dataMap.put("dunk",dunk);
            dataMap.put("helpball",helpball);
            FieldData fieldData = new FieldData();
            fieldData.setDataMap(dataMap);
            jsonObject.put("dataMap", fieldData.getDataMap());
            jsonObject.put("sumAssist", assist);
            jsonObject.put("sumBackboard", frontback+afterback);
            Integer sunScore = onePointer * 1 + twoPointer * 2 + threePointer * 3;
            jsonObject.put("sumScore", sunScore);
            //运动健将
            StuPersonDataAo stuPersonDataAo1 = scoreList.get(0);
            FieldData motionFieldData = getBaseMapper().getByStuId(stuPersonDataAo1.getStuId(), fieldNotes.getId());
            if (motionFieldData != null && motionFieldData.getUpMap() != null) {
                motionFieldData.setDataMap(JSONObject.parseObject(motionFieldData.getUpMap()));
                //助攻次数
                String motionAssist = motionFieldData.getDataMap().getString("assist");
                jsonObject.put("motionAssist", Integer.valueOf(motionAssist));
                //篮板
//                String motionBoard = motionFieldData.getDataMap().getString("backboard");
//                jsonObject.put("motionBoard", Integer.valueOf(motionBoard));
                String frontback1 = motionFieldData.getDataMap().getString("frontback");
                Integer frontback2 = 0;
                if(frontback1 != null){
                    frontback2 = Integer.valueOf(frontback1);
                }
                String afterback1 = motionFieldData.getDataMap().getString("afterback");
                Integer afterback2 = 0;
                if(afterback1 != null){
                    afterback2 = Integer.valueOf(afterback1);
                }
                jsonObject.put("motionBoard", frontback2+afterback2);
                //抢断
                String motionPreemption = motionFieldData.getDataMap().getString("snatch");
                jsonObject.put("motionPreemption", Integer.valueOf(motionPreemption));
            } else {
                jsonObject.put("motionAssist", 0);
                jsonObject.put("motionBoard", 0);
                jsonObject.put("motionPreemption", 0);
            }
            jsonObject.put("motionName", stuPersonDataAo1.getStuName());
            jsonObject.put("motionScore", stuPersonDataAo1.getScore());
            PersonData da = personDataMapper.getYundong(stuPersonDataAo1.getStuId(), gameId, "GAME");
            if (da != null) {
                jsonObject.put("motionDistance", da.getRunDistance());
            } else {
                jsonObject.put("motionDistance", 0);
            }
        }
        //总和(步数+跑动距离+热量)
        StuPersonDataAo stuPersonDataAo = personDataService.sumCalculation(gameId, "GAME");
        jsonObject.put("sumSteps", stuPersonDataAo.getSumSteps());
        jsonObject.put("sumRun", stuPersonDataAo.getSumRun());
        jsonObject.put("sumCalorie", stuPersonDataAo.getSumCalorie());
        return jsonObject;
    }

    @Override
    public FieldDataPageParam createFieldDataPageParam(Long userId, String matchType, Long matchId, Long pageIndex, Long pageSize) {
        FieldDataPageParam fieldDataPageParam = new FieldDataPageParam();
        fieldDataPageParam.setMatchType(matchType);
        fieldDataPageParam.setStudentId(userId);
        fieldDataPageParam.setMatchId(matchId);
        fieldDataPageParam.setPageIndex(pageIndex);
        fieldDataPageParam.setPageSize(pageSize);
        return fieldDataPageParam;
    }

    @Override
    public Object goalField(JSONObject jsonObject, Long fieldId) {
        //统计场记
        Integer goal = 0;
        Integer orthogonality = 0;
        Integer assisting = 0;
        Integer surpass = 0;
        Integer pointsphere = 0;
        Integer cornerkick = 0;
        Integer freekick = 0;
        Integer threatball = 0;
        Integer heading = 0;
        Integer raise = 0;
        Integer preemption = 0;
        Integer redcard = 0;
        Integer shooting = 0;
        Integer rules = 0;
        Integer offside = 0;
        Integer puking = 0;
        Integer yellowcard = 0;
        Integer owngoal = 0;
        Integer waveshooting = 0;
        Integer kickair = 0;
        Integer stoperror = 0;
        Integer defensiveerror = 0;
        Integer passingerror = 0;
        Integer wonderful = 0;
        Integer excitingShot = 0;
        Integer excitingSyeals = 0;
        Integer longpass = 0;
        Integer headgoal = 0;
        Integer headclear = 0;
        Integer clearance = 0;
        Integer rescue = 0;
        Integer pointMaking = 0;
        Integer fatalerror = 0;
        Integer wonderfulstop = 0;
        Integer shoot = 0;
        Integer threatenshoot = 0;
        Integer scoring = 0;
        Integer seriouserror = 0;
        List<FieldData> fieldDataList = getBaseMapper().getList(fieldId);
        if (fieldDataList.size() > 0) {
            for (FieldData fieldData : fieldDataList) {
                fieldData.setDataMap(JSONObject.parseObject(fieldData.getUpMap()));
                if (fieldData.getDataMap() != null) {
                    String goal1 = fieldData.getDataMap().getString("goal");
                    if (goal1 != null) {
                        goal = goal + Integer.valueOf(goal1);
                    }
                    String orthogonality1 = fieldData.getDataMap().getString("orthogonality");
                    if (orthogonality1 != null) {
                        orthogonality = orthogonality + Integer.valueOf(orthogonality1);
                    }
                    String assisting1 = fieldData.getDataMap().getString("assisting");
                    if (assisting1 != null) {
                        assisting = assisting + Integer.valueOf(assisting1);
                    }
                    String surpass1 = fieldData.getDataMap().getString("surpass");
                    if (surpass1 != null) {
                        surpass = surpass + Integer.valueOf(surpass1);
                    }
                    String pointsphere1 = fieldData.getDataMap().getString("pointsphere");
                    if (pointsphere1 != null) {
                        pointsphere = pointsphere + Integer.valueOf(pointsphere1);
                    }
                    String cornerkick1 = fieldData.getDataMap().getString("cornerkick");
                    if (cornerkick1 != null) {
                        cornerkick = cornerkick + Integer.valueOf(cornerkick1);
                    }
                    String freekick1 = fieldData.getDataMap().getString("freekick");
                    if (freekick1 != null) {
                        freekick = freekick + Integer.valueOf(freekick1);
                    }
                    String threatball1 = fieldData.getDataMap().getString("threatball");
                    if (threatball1 != null) {
                        threatball = threatball + Integer.valueOf(threatball1);
                    }
                    String heading1 = fieldData.getDataMap().getString("heading");
                    if (heading1 != null) {
                        heading = heading + Integer.valueOf(heading1);
                    }
                    String raise1 = fieldData.getDataMap().getString("raise");
                    if (raise1 != null) {
                        raise = raise + Integer.valueOf(raise1);
                    }
                    String preemption1 = fieldData.getDataMap().getString("preemption");
                    if (preemption1 != null) {
                        preemption = preemption + Integer.valueOf(preemption1);
                    }
                    String redcard1 = fieldData.getDataMap().getString("redcard");
                    if (redcard1 != null) {
                        redcard = redcard + Integer.valueOf(redcard1);
                    }
                    String shooting1 = fieldData.getDataMap().getString("shooting");
                    if (shooting1 != null) {
                        shooting = shooting + Integer.valueOf(shooting1);
                    }
                    String rules1 = fieldData.getDataMap().getString("rules");
                    if (rules1 != null) {
                        rules = rules + Integer.valueOf(rules1);
                    }
                    String offside1 = fieldData.getDataMap().getString("offside");
                    if (offside1 != null) {
                        offside = offside + Integer.valueOf(offside1);
                    }
                    String puking1 = fieldData.getDataMap().getString("puking");
                    if (puking1 != null) {
                        puking = puking + Integer.valueOf(puking1);
                    }
                    String yellowcard1 = fieldData.getDataMap().getString("yellowcard");
                    if (yellowcard1 != null) {
                        yellowcard = yellowcard + Integer.valueOf(yellowcard1);
                    }
                    String owngoal1 = fieldData.getDataMap().getString("owngoal");
                    if (owngoal1 != null) {
                        owngoal = owngoal + Integer.valueOf(owngoal1);
                    }
                    String waveshooting1 = fieldData.getDataMap().getString("waveshooting");
                    if (waveshooting1 != null) {
                        waveshooting = waveshooting + Integer.valueOf(waveshooting1);
                    }
                    String kickair1 = fieldData.getDataMap().getString("kickair");
                    if (kickair1 != null) {
                        kickair = kickair + Integer.valueOf(kickair1);
                    }
                    String stoperror1 = fieldData.getDataMap().getString("stoperror");
                    if (stoperror1 != null) {
                        stoperror = stoperror + Integer.valueOf(stoperror1);
                    }
                    String defensiveerror1 = fieldData.getDataMap().getString("defensiveerror");
                    if (defensiveerror1 != null) {
                        defensiveerror = defensiveerror + Integer.valueOf(defensiveerror1);
                    }
                    String passingerror1 = fieldData.getDataMap().getString("passingerror");
                    if (passingerror1 != null) {
                        passingerror = passingerror + Integer.valueOf(passingerror1);
                    }
                    String wonderful1 = fieldData.getDataMap().getString("wonderful");
                    if (wonderful1 != null) {
                        wonderful = wonderful + Integer.valueOf(wonderful1);
                    }
                    String excitingShot1 = fieldData.getDataMap().getString("excitingShot");
                    if (excitingShot1 != null) {
                        excitingShot = excitingShot + Integer.valueOf(excitingShot1);
                    }
                    String excitingSyeals1 = fieldData.getDataMap().getString("excitingSyeals");
                    if (excitingSyeals1 != null) {
                        excitingSyeals = excitingSyeals + Integer.valueOf(excitingSyeals1);
                    }
                    String longpass1 = fieldData.getDataMap().getString("longpass");
                    if (longpass1 != null) {
                        longpass = longpass + Integer.valueOf(longpass1);
                    }
                    String headgoal1 = fieldData.getDataMap().getString("headgoal");
                    if (headgoal1 != null) {
                        headgoal = headgoal + Integer.valueOf(headgoal1);
                    }
                    String headclear1 = fieldData.getDataMap().getString("headclear");
                    if (headclear1 != null) {
                        headclear = headclear + Integer.valueOf(headclear1);
                    }
                    String clearance1 = fieldData.getDataMap().getString("clearance");
                    if (clearance1 != null) {
                        clearance = clearance + Integer.valueOf(clearance1);
                    }
                    String rescue1 = fieldData.getDataMap().getString("rescue");
                    if (rescue1 != null) {
                        rescue = rescue + Integer.valueOf(rescue1);
                    }
                    String pointMaking1 = fieldData.getDataMap().getString("pointMaking");
                    if (pointMaking1 != null) {
                        pointMaking = pointMaking + Integer.valueOf(pointMaking1);
                    }
                    String fatalerror1 = fieldData.getDataMap().getString("fatalerror");
                    if (fatalerror1 != null) {
                        fatalerror = fatalerror + Integer.valueOf(fatalerror1);
                    }
                    String wonderfulstop1 = fieldData.getDataMap().getString("wonderfulstop");
                    if (wonderfulstop1 != null) {
                        wonderfulstop = wonderfulstop + Integer.valueOf(wonderfulstop1);
                    }
                    String shoot1 = fieldData.getDataMap().getString("shoot");
                    if (shoot1 != null) {
                        shoot = shoot + Integer.valueOf(shoot1);
                    }
                    String threatenshoot1 = fieldData.getDataMap().getString("threatenshoot");
                    if (threatenshoot1 != null) {
                        threatenshoot = threatenshoot + Integer.valueOf(threatenshoot1);
                    }
                    String scoring1 = fieldData.getDataMap().getString("scoring");
                    if (scoring1 != null) {
                        scoring = scoring + Integer.valueOf(scoring1);
                    }
                    String seriouserror1 = fieldData.getDataMap().getString("seriouserror");
                    if (seriouserror1 != null) {
                        seriouserror = seriouserror + Integer.valueOf(seriouserror1);
                    }
                }
            }
            jsonObject.put("hideMap", JSONObject.parseObject(fieldDataList.get(0).getOnMap()));
        }
        JSONObject dataMap = new JSONObject();
        dataMap.put("goal", goal);
        dataMap.put("orthogonality", orthogonality);
        dataMap.put("assisting", assisting);
        dataMap.put("surpass", surpass);
        dataMap.put("pointsphere", pointsphere);
        dataMap.put("cornerkick", cornerkick);
        dataMap.put("freekick", freekick);
        dataMap.put("threatball", threatball);
        dataMap.put("heading", heading);
        dataMap.put("raise", raise);
        dataMap.put("preemption", preemption);
        dataMap.put("redcard", redcard);
        dataMap.put("shooting", shooting);
        dataMap.put("rules", rules);
        dataMap.put("offside", offside);
        dataMap.put("puking", puking);
        dataMap.put("yellowcard", yellowcard);
        dataMap.put("owngoal", owngoal);
        dataMap.put("waveshooting", waveshooting);
        dataMap.put("kickair", kickair);
        dataMap.put("stoperror", stoperror);
        dataMap.put("defensiveerror", defensiveerror);
        dataMap.put("passingerror", passingerror);
        dataMap.put("wonderful", wonderful);
        dataMap.put("excitingShot", excitingShot);
        dataMap.put("excitingSyeals", excitingSyeals);
        dataMap.put("longpass", longpass);
        dataMap.put("headgoal", headgoal);
        dataMap.put("headclear", headclear);
        dataMap.put("clearance", clearance);
        dataMap.put("rescue", rescue);
        dataMap.put("pointMaking", pointMaking);
        dataMap.put("fatalerror", fatalerror);
        dataMap.put("wonderfulstop", wonderfulstop);
        dataMap.put("shoot", shoot);
        dataMap.put("threatenshoot", threatenshoot);
        dataMap.put("scoring", scoring);
        dataMap.put("seriouserror", seriouserror);
        FieldData fieldData = new FieldData();
        fieldData.setDataMap(dataMap);
        jsonObject.put("dataMap", fieldData.getDataMap());
        return jsonObject;
    }

    @Override
    public List<FieldData> getGroupIdAndStuId(Long fnId) {
        return getBaseMapper().getGroupIdAndStuId(fnId);
    }
}
