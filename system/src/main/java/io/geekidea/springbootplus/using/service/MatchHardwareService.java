package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.MatchHardware;
import io.geekidea.springbootplus.using.param.MatchHardwarePageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface MatchHardwareService extends BaseService<MatchHardware> {
    List<MatchHardware> getMatchHardwareByMatchId(Long matchId,Long teamId,String matchType);

    List<MatchHardware> getMatchHardwareByMatchId(Long matchId,String matchType);

}
