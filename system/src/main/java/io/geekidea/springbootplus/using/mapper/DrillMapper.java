package io.geekidea.springbootplus.using.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.geekidea.springbootplus.using.entity.Drill;
import io.geekidea.springbootplus.using.entity.DrillStudent;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.param.DrillPageParam;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Repository
public interface DrillMapper extends BaseMapper<Drill> {
    Drill getLastNoUploadAndNoCancel(@Param("teamIds")List<Long> teamIds);

    Drill getLastNoUploadAndNoCancelStu(@Param("stuId")Long stuId);

    IPage<Drill> getGrowUpData(Page<Drill> page,@Param("teamId")Long teamId, @Param("studentId") Long studentId, @Param("befor") Date befor, @Param("after") Date after);

    List<StudentInfo> attendance(@Param("teamId") Long teamId);

    Integer getNormal(@Param("stuId") Long stuId,@Param("date") String date);

    Integer getDefect(@Param("stuId") Long stuId,@Param("date") String date);

    Integer unUploadCount(@Param("teamId") Long teamId);
}
