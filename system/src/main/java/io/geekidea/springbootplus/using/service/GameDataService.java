package io.geekidea.springbootplus.using.service;


import io.geekidea.springbootplus.using.entity.PersonData;

import java.util.List;

public interface GameDataService {
    /**
     * @desc: 上传数据
     * @author: DH
     * @date: 2022/4/29 11:09
     */
    boolean uploadData(String strJSONArray, Long matchId, String matchType);

    /**
     * @desc: 查看数据
     * @author: DH
     * @date: 2022/4/29 14:56
     */
    Object getPlayerData(Long matchId, Long studentId, Long teamId, String matchType);

    /**
     * @desc: 查看数据(提供鸵鸟)
     * @author: DH
     * @date: 2022/4/29 14:56
     */
    Object getPlayerDataTuoNiao(Long matchId, Long studentId,Long teamId);

    /**
     * @desc: 体能排行
     * @author: DH
     * @date: 2022/5/6 10:07
     */
    Object getPhysicalAnalysis(Long matchId, Long teamId, String matchType);

    /**
     * @desc: 体能排行
     * @author: DH
     * @date: 2022/5/6 10:07
     */
    Object moveDistanceRanking(Long matchId, String rankingKey, List<PersonData> personDatas, Long teamId, String matchType);
}
