package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.entity.AppMessage;
import io.geekidea.springbootplus.using.mapper.AppMessageMapper;
import io.geekidea.springbootplus.using.service.AppMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * APP消息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Service
public class AppMessageServiceImpl extends BaseServiceImpl<AppMessageMapper, AppMessage> implements AppMessageService {

    @Override
    public Long sendMessage(Long receiverUserId, Long senderUserId, String messageType, 
                           String title, String content, JSONObject extraData) {
        AppMessage message = new AppMessage();
        message.setReceiverUserId(receiverUserId);
        message.setSenderUserId(senderUserId);
        message.setMessageType(messageType);
        message.setTitle(title);
        message.setContent(content);
        message.setExtraData(extraData);
        message.setIsRead(false);
        
        save(message);
        return message.getId();
    }

    @Override
    public Paging<AppMessage> getUserMessages(Long userId, Integer page, Integer size) {
        Page<AppMessage> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<AppMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppMessage::getReceiverUserId, userId)
               .orderByDesc(AppMessage::getCreateTime);
        
        IPage<AppMessage> iPage = page(pageParam, wrapper);
        return new Paging<>(iPage);
    }

    @Override
    public Map<String, Long> getMessageCount(Long userId) {
        Map<String, Long> result = new HashMap<>();
        
        // 总消息数
        LambdaQueryWrapper<AppMessage> totalWrapper = new LambdaQueryWrapper<>();
        totalWrapper.eq(AppMessage::getReceiverUserId, userId);
        long totalCount = count(totalWrapper);
        
        // 未读消息数
        LambdaQueryWrapper<AppMessage> unreadWrapper = new LambdaQueryWrapper<>();
        unreadWrapper.eq(AppMessage::getReceiverUserId, userId)
                    .eq(AppMessage::getIsRead, false);
        long unreadCount = count(unreadWrapper);
        
        result.put("totalCount", totalCount);
        result.put("unreadCount", unreadCount);
        
        return result;
    }

    @Override
    public boolean markAsRead(Long messageId, Long userId) {
        LambdaUpdateWrapper<AppMessage> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AppMessage::getId, messageId)
               .eq(AppMessage::getReceiverUserId, userId)
               .eq(AppMessage::getIsRead, false)
               .set(AppMessage::getIsRead, true)
               .set(AppMessage::getReadTime, new Date());
        
        return update(wrapper);
    }

    @Override
    public boolean markAllAsRead(Long userId) {
        LambdaUpdateWrapper<AppMessage> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AppMessage::getReceiverUserId, userId)
               .eq(AppMessage::getIsRead, false)
               .set(AppMessage::getIsRead, true)
               .set(AppMessage::getReadTime, new Date());
        
        return update(wrapper);
    }

    @Override
    public void sendTeamMessage(Long receiverUserId, String messageType, Long teamId, String teamName, String teamLogo, String extraInfo) {
        JSONObject extraData = new JSONObject();
        extraData.put("teamId", teamId);
        extraData.put("teamName", teamName);
        extraData.put("teamLogo", teamLogo);

        String title = getMessageTitle(messageType, teamName);
        String content = getMessageContent(messageType, teamName, extraInfo);

        // 创建消息
        AppMessage message = new AppMessage();
        message.setReceiverUserId(receiverUserId);
        message.setSenderUserId(null);
        message.setMessageType(messageType);
        message.setTitle(title);
        message.setContent(content);
        message.setExtraData(extraData);
        message.setIsRead(false);

        // 如果是申请加入消息，设置初始审核状态为未审核
        if (AppMessage.TYPE_APPLY_JOIN.equals(messageType)) {
            message.setReviewStatus(AppMessage.REVIEW_STATUS_PENDING);
        }

        save(message);
    }

    @Override
    public boolean updateApplicationMessageReviewStatus(Long teamId, Long applicantUserId, Integer reviewStatus) {
        LambdaUpdateWrapper<AppMessage> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(AppMessage::getMessageType, AppMessage.TYPE_APPLY_JOIN)
               .eq(AppMessage::getReceiverUserId,
                   // 这里需要根据extraData中的teamId和applicantUserId来查找对应的消息
                   // 由于MyBatis-Plus对JSON字段查询支持有限，我们需要先查询再更新
                   null); // 暂时设为null，下面用自定义查询

        // 先查询符合条件的消息
        LambdaQueryWrapper<AppMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppMessage::getMessageType, AppMessage.TYPE_APPLY_JOIN)
                   .isNull(AppMessage::getReviewStatus); // 只更新未审核的消息

        List<AppMessage> messages = list(queryWrapper);

        // 筛选出匹配的消息并更新
        for (AppMessage message : messages) {
            if (message.getExtraData() != null) {
                Long msgTeamId = message.getExtraData().getLong("teamId");
                Long msgApplicantUserId = message.getExtraData().getLong("applicantUserId");

                if (teamId.equals(msgTeamId) && applicantUserId.equals(msgApplicantUserId)) {
                    message.setReviewStatus(reviewStatus);
                    message.setReviewTime(new Date());
                    updateById(message);
                    return true;
                }
            }
        }

        return false;
    }

    @Override
    public Paging<AppMessage> getPendingApplicationMessages(Long userId, Integer page, Integer size) {
        Page<AppMessage> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<AppMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppMessage::getReceiverUserId, userId)
               .eq(AppMessage::getMessageType, AppMessage.TYPE_APPLY_JOIN)
               .eq(AppMessage::getReviewStatus, AppMessage.REVIEW_STATUS_PENDING)
               .orderByDesc(AppMessage::getCreateTime);

        IPage<AppMessage> iPage = page(pageParam, wrapper);
        return new Paging<>(iPage);
    }

    private String getMessageTitle(String messageType, String teamName) {
        switch (messageType) {
            case AppMessage.TYPE_APPLY_JOIN:
                return "入队申请";
            case AppMessage.TYPE_JOIN_SUCCESS:
                return "加入成功";
            case AppMessage.TYPE_JOIN_REJECTED:
                return "申请被拒绝";
            case AppMessage.TYPE_MEMBER_JOINED:
                return "新成员加入";
            case AppMessage.TYPE_TEAM_DISSOLVED:
                return "球队解散";
            case AppMessage.TYPE_KICKED_OUT:
                return "被移出球队";
            case AppMessage.TYPE_MEMBER_QUIT:
                return "成员退出";
            default:
                return "球队通知";
        }
    }

    private String getMessageContent(String messageType, String teamName, String extraInfo) {
        switch (messageType) {
            case AppMessage.TYPE_APPLY_JOIN:
                return "球队【" + teamName + "】收到新的入队申请";
            case AppMessage.TYPE_JOIN_SUCCESS:
                return "恭喜您成功加入球队【" + teamName + "】";
            case AppMessage.TYPE_JOIN_REJECTED:
                return "很遗憾，您申请加入球队【" + teamName + "】被拒绝";
            case AppMessage.TYPE_MEMBER_JOINED:
                return "球队【" + teamName + "】有新成员加入";
            case AppMessage.TYPE_TEAM_DISSOLVED:
                return "球队【" + teamName + "】已被解散";
            case AppMessage.TYPE_KICKED_OUT:
                return "您已被移出球队【" + teamName + "】";
            case AppMessage.TYPE_MEMBER_QUIT:
                return "球队【" + teamName + "】有成员退出";
            default:
                return extraInfo != null ? extraInfo : "球队【" + teamName + "】有新消息";
        }
    }
}
