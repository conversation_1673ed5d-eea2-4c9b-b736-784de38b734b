package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.AppMessage;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;
import java.util.Map;

/**
 * APP消息表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface AppMessageService extends BaseService<AppMessage> {

    /**
     * 发送消息
     *
     * @param receiverUserId 接收用户ID
     * @param senderUserId 发送用户ID（系统消息时为null）
     * @param messageType 消息类型
     * @param title 消息标题
     * @param content 消息内容
     * @param extraData 扩展数据
     * @return 消息ID
     */
    Long sendMessage(Long receiverUserId, Long senderUserId, String messageType, 
                    String title, String content, JSONObject extraData);

    /**
     * 获取用户消息列表（分页）
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 消息列表
     */
    Paging<AppMessage> getUserMessages(Long userId, Integer page, Integer size);

    /**
     * 获取用户消息统计
     *
     * @param userId 用户ID
     * @return 统计信息（总数、未读数）
     */
    Map<String, Long> getMessageCount(Long userId);

    /**
     * 标记消息为已读
     *
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean markAsRead(Long messageId, Long userId);

    /**
     * 标记用户所有消息为已读
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean markAllAsRead(Long userId);

    /**
     * 发送球队相关消息
     *
     * @param receiverUserId 接收用户ID
     * @param messageType 消息类型
     * @param teamId 球队ID
     * @param teamName 球队名称
     * @param teamLogo 球队logo
     * @param extraInfo 额外信息
     */
    void sendTeamMessage(Long receiverUserId, String messageType, Long teamId, String teamName, String teamLogo, String extraInfo);

    /**
     * 更新申请消息的审核状态
     *
     * @param teamId 球队ID
     * @param applicantUserId 申请人用户ID
     * @param reviewStatus 审核状态
     * @return 是否成功
     */
    boolean updateApplicationMessageReviewStatus(Long teamId, Long applicantUserId, Integer reviewStatus);

    /**
     * 获取用户的待审核申请消息列表
     *
     * @param userId 用户ID（队长）
     * @param page 页码
     * @param size 每页大小
     * @return 待审核消息列表
     */
    Paging<AppMessage> getPendingApplicationMessages(Long userId, Integer page, Integer size);
}
