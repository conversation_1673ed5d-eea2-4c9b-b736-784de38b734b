package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.constant.CacheConsts;
import io.geekidea.springbootplus.framework.util.EmailUtil;
import io.geekidea.springbootplus.framework.util.MD5Utils;
import io.geekidea.springbootplus.framework.util.MobileUtils;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.mapper.UserAppMapper;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.param.UserAppPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Slf4j
@Service
public class UserAppServiceImpl extends BaseServiceImpl<UserAppMapper, UserApp> implements UserAppService {

    @Autowired
    private UserAppMapper userAppMapper;

    @Autowired
    private TeamUserAppService teamUserAppService;

    @Autowired
    private StudentInfoService studentInfoService;

    @Autowired
    private SchoolInfoService schoolInfoService;

    @Autowired
    private TeamService teamService;

    @Autowired
    private HardwareService hardwareService;

    @Autowired
    private HardwareAppService hardwareAppService;

    @Autowired
    private CourseAmountService courseAmountService;

    @Autowired
    @Lazy
    private PersonDataService personDataService;

    @Autowired
    @Lazy
    private GameService gameService;
    @Autowired
    private KindergartenMonitorService kindergartenMonitorService;

    @Autowired
    private KindergartenMonitorStudentService kindergartenMonitorStudentService;

    @Autowired
    @Lazy
    private GameStudentService gameStudentService;

    @Autowired
    @Lazy
    private DrillService drillService;

    @Autowired
    @Lazy
    private DrillStudentService drillStudentService;

    @Autowired
    private AuditService auditService;
    @Autowired
    EmailUtil emailUtil;
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    MobileUtils mobileUtils;

    @Override
    @Transactional
    public Boolean register(UserApp userApp) {
        if (userApp.getPasswd() != null) {
            userApp.setPasswd(MD5Utils.getSaltMD5(userApp.getPasswd()));
        }
     /*   //默认PAD端创建一个对应的学生账号基本信息存的是APP的 目的为了不改动大量的训练模块代码
        // 后续勾连操作
        StudentInfo studentInfo = studentInfoService.getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getPhone, userApp.getPhone()));
        if (studentInfo == null) {
            studentInfo = new StudentInfo();
            studentInfo.setTeamId(0l);
            studentInfo.setSchoollId(0l);
            BeanUtils.copyProperties(userApp, studentInfo);
            studentInfo.setPhone("APP端手机:" + userApp.getPhone());
            studentInfoService.saveOrUpdate(studentInfo);
        }*/
        StudentInfo studentInfo = studentInfoService.getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getPhone, userApp.getPhone()));
        if (userApp.getPhone() != null && studentInfo != null) {
            userApp.setHdStudentId(studentInfo.getId());
        }

        return saveOrUpdate(userApp);
    }

    @Override
    public UserApp loginUserApp(UserApp userApp) {
        UserApp s = super.getOne(new LambdaQueryWrapper<UserApp>().eq(userApp.getLoginType() == 1, UserApp::getPhone, userApp.getPhone()).eq(userApp.getLoginType() == 2, UserApp::getEmail, userApp.getEmail()));
        if (s.getPasswd() == null || userApp.getPasswd() == null || !checkPassword(userApp.getPasswd(), s.getPasswd())) {
            return null;
        }
        s.setDeviceTokens(userApp.getDeviceTokens());
        //自动勾连PAD学生账号
        StudentInfo studentInfo = studentInfoService.getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getPhone, userApp.getPhone()));
        if (s.getPhone() != null && studentInfo != null) {
            s.setHdStudentId(studentInfo.getId());
        }
        s.setLoginType(userApp.getLoginType());
        saveOrUpdate(s);
        return getUser(s.getId());
    }

    @Override
    public UserApp examineName(UserApp userApp) {
        UserApp s = super.getOne(new LambdaQueryWrapper<UserApp>().eq(userApp.getLoginType() == 1, UserApp::getPhone, userApp.getPhone()).eq(userApp.getLoginType() == 2, UserApp::getEmail, userApp.getEmail()));
        return s;
    }

    @Override
    public Boolean checkPassword(String password, String checkpwd) {
        return MD5Utils.getSaltverifyMD5(password, checkpwd);
    }

    @Override
    public ApiCode onlyValidation(String phone) {
        UserApp userApp = null;
        userApp = getOne(new LambdaQueryWrapper<UserApp>().eq(UserApp::getPhone, phone));
        if (userApp != null) {
            return ApiCode.PHONE_REPETITION;
        }
        return null;
    }

    @Override
    public ApiCode onlyValidationEmail(String email) {
        UserApp userApp = null;
        userApp = getOne(new LambdaQueryWrapper<UserApp>().eq(UserApp::getEmail, email));
        if (userApp != null) {
            return ApiCode.EMAIL_REPETITION;
        }
        return null;
    }


    @Override
    public ApiCode updaValidation(String phone, Long userId) {
        UserApp userApp = getOne(new LambdaQueryWrapper<UserApp>().eq(UserApp::getPhone, phone));
        if (userApp != null) {
            if (userApp.getId() != userId) {
                return ApiCode.PHONE_REPETITION;
            }
        }
        return null;
    }

    @Override
    public ApiCode updaValidationEmail(String email, Long userId) {
        UserApp userApp = getOne(new LambdaQueryWrapper<UserApp>().eq(UserApp::getEmail, email));
        if (userApp != null) {
            if (userApp.getId() != userId) {
                return ApiCode.PHONE_REPETITION;
            }
        }
        return null;
    }

    @Override
    public Boolean resetPassword(UserApp userApp) {
        String password = userApp.getPasswd();
        userApp = getOne(new LambdaQueryWrapper<UserApp>().eq(userApp.getPhone()!=null,UserApp::getPhone, userApp.getPhone()).or(userApp.getPhone()==null).eq(userApp.getEmail()!=null,UserApp::getEmail, userApp.getEmail()));
        if (userApp != null) {
            userApp.setPasswd(MD5Utils.getSaltMD5(password));
        }
        return saveOrUpdate(userApp);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean logoutUser(UserApp user) throws Exception {
        Boolean b1 = deleteUserApp(user.getId());
        if (b1) {
            return true;
        } else {
            //手动强制回滚事务，这里一定要第一时间处理
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
    }

    @Override
    public UserApp getById(Long id) {
        return getBaseMapper().getById(id);
    }

    @Override
    public UserApp getUser(Long id) {
        UserApp s = getById(id);
        s.setHardware(hardwareService.getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getStudentId, s.getId()).eq(Hardware::getUnbind, false)));
        s.setHardwareApp(hardwareAppService.getOne(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getStudentId, s.getId()).eq(HardwareApp::getUnbind, false)));
        if (s.getHdStudentId() != null && s.getHdStudentId() != -1) {
            StudentInfo studentInfo1 = studentInfoService.getById(s.getHdStudentId());
            s.setCard(studentInfo1.getCard());
            SchoolInfo schoolInfo = schoolInfoService.getById(studentInfo1.getSchoollId());
            if (schoolInfo != null) {
                s.setSchoolId(schoolInfo.getId());
                s.setSchoolName(schoolInfo.getName());
                Team team = teamService.getById(studentInfo1.getTeamId());
                s.setHdTeamId(team.getId());
                s.setTeamName(team.getShortName());
                s.setMonitorSchollId(schoolInfo.getId());
            }
        }
        List<CourseAmount> courseAmounts = courseAmountService.list(new LambdaQueryWrapper<CourseAmount>().eq(CourseAmount::getMatchType, "DRILL").eq(CourseAmount::getStudentId, id));
        JSONObject courseCount = new JSONObject();
        courseAmounts.forEach(e -> {
            if (e.getCourseId() < 2) {
                courseCount.put("rest", e.getAmount());
            } else if (e.getCourseId() == 3) {
                courseCount.put("football", e.getAmount());
            } else if (e.getCourseId() == 2) {
                courseCount.put("basketball", e.getAmount());
            }
        });
        s.setGradeAudit(auditService.getGradeAudit(s.getId(), null, "grade"));
        s.setBasketballGradeAudit(auditService.getGradeAudit(s.getId(), null, "basketballGrade"));
        PersonData personData = personDataService.getOne(new LambdaQueryWrapper<PersonData>().eq(PersonData::getStudentId, s.getHdStudentId()).eq(PersonData::getMatchType, "MONITOR").orderByDesc(PersonData::getId).last("limit 1"));
        if (personData != null) {
            s.setHaveMonitorDate(true);
            s.setMonitorTeamId(personData.getTeamId());
        }
        return s;
    }

    @Override
    public List<UserApp> getByTeamId(Long teamId) {
        List<Long> teamUserApps = teamUserAppService.getByTeamId(teamId).stream().map(TeamUserApp::getUserId).collect(Collectors.toList());
        if (teamUserApps.size() == 0) {
            return new ArrayList<>();
        }
        return listByIds(teamUserApps);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveUserApp(UserApp userApp) throws Exception {
        return super.save(userApp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateUserApp(UserApp userApp) throws Exception {
        if (userApp.getPasswd() != null) {
            userApp.setPasswd(MD5Utils.getSaltMD5(userApp.getPasswd()));
        }

        return saveOrUpdate(userApp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteUserApp(Long id) throws Exception {
        Hardware hardware = hardwareService.getByStuId(id, null);
        if (hardware != null) {
            hardware.setUnbind(true);
            hardwareService.saveOrUpdate(hardware);
        }
        HardwareApp hardwareApp = hardwareAppService.getByStuId(id, null);
        if (hardwareApp != null) {
            hardwareApp.setUnbind(true);
            hardwareAppService.saveOrUpdate(hardwareApp);
        }
        UserApp userApp = getById(id);
        if (userApp != null) {
            if (userApp.getHdStudentId() != null && userApp.getHdStudentId() != 0) {
                StudentInfo studentInfo = studentInfoService.getById(userApp.getHdStudentId());
                if (studentInfo != null && studentInfo.getSchoollId() == 0) {
                    studentInfo.setPhone(studentInfo.getPhone() + "-" + studentInfo.getId());
                    studentInfo.setDeleted(true);
                    studentInfoService.saveOrUpdate(studentInfo);
                    studentInfoService.removeById(studentInfo.getId());
                }
            }
            userApp.setPhone(userApp.getPhone() + "-" + id);
            userApp.setUsername(userApp.getUsername() + id);
            userApp.setDeleted(true);
            super.saveOrUpdate(userApp);
            return removeById(id);
        } else {
            return false;
        }
    }

    @Override
    public Paging<UserApp> getUserAppPageList(UserAppPageParam userAppPageParam) throws Exception {
        Page<UserApp> page = new PageInfo<>(userAppPageParam, OrderItem.desc(getLambdaColumn(UserApp::getCreateTime)));
        LambdaQueryWrapper<UserApp> wrapper = new LambdaQueryWrapper<>();
        IPage<UserApp> iPage = userAppMapper.selectPage(page, wrapper);
        return new Paging<UserApp>(iPage);
    }

    @Override
    public Boolean checkPhoneExist(String phone) {
        List<UserApp> studentInfoList = list(new LambdaQueryWrapper<UserApp>().eq(UserApp::getPhone, phone));
        return studentInfoList.size() > 0;
    }

    @Override
    public String sendPhoneVerificationCode(String phoneNumber) {
        Map<Integer, String> map = mobileUtils.reviewPhoneNumber(phoneNumber);

        //生成6位随机数字,用于作验证码,(头一位数据不能为0)
        long contentCode = (long) ((Math.random() * 9 + 1) * 100000);
        String contentBody = String.valueOf(contentCode);

        JSONObject jsonObject = mobileUtils.sendSms(map.get(2), map.get(1), contentBody);

        if ("OK".equals(jsonObject.getString("Code"))) {
            redisUtils.set(io.geekidea.springbootplus.framework.constant.CacheConsts.MOBILE_PHONE_MESSAGE_PREFIX + phoneNumber, contentBody, io.geekidea.springbootplus.framework.constant.CacheConsts.MOBILE_PHONE_MESSAGE_TIME);
            return "0000";
        } else {
            String code = "-1";
            if ("isv.BUSINESS_LIMIT_CONTROL".equals(jsonObject.getString("Code"))) {
                String message = jsonObject.getString("Message");
                if (message.substring(2, 3).equals("分")) {
                    code = "20016";
                } else if (message.substring(3, 4).equals("时")) {
                    code = "20017";
                } else if (message.substring(4, 5).equals("天") || message.substring(2, 3).equals("天")) {
                    code = "20018";
                }
            }
            return code;
        }
    }

    @Override
    public ApiCode checkVerificationCode(String credential, String contentBody, Boolean delete) {
        return mobileUtils.checkVerificationCode(credential, contentBody, false);
    }

    @Override
    public String sendEmailVerificationCode(String email,Boolean en) {
        //生成6位随机数字,用于作验证码,(头一位数据不能为0)
        long contentCode = (long) ((Math.random() * 9 + 1) * 100000);
        String contentBody = String.valueOf(contentCode);
        emailUtil.sendEmail(email, contentBody,en);
        redisUtils.set(CacheConsts.EMAIL_MESSAGE_PREFIX + email, contentBody, CacheConsts.EMAIL_MESSAGE_TIME);
        return "0000";
    }

    @Override
    public ApiCode checkEmailVerificationCode(String email, String code) {
        return emailUtil.verify(false, email, code);
    }

    @Override
    public Boolean checkEmailExist(String email) {
        List<UserApp> studentInfoList = list(new LambdaQueryWrapper<UserApp>().eq(UserApp::getEmail, email));
        return studentInfoList.size() > 0;
    }

    @Override
    public JSONObject getCount(Long userId, String matchType, Long courseId) {
        JSONObject object = new JSONObject();
        List<Long> finish = new ArrayList<>();
        long noFinish = 0l;
        long upload = 0L;
        long noAppLookDrill = 0l;
        long noAppLookMonitor = 0l;
        UserApp userApp = new UserApp();
        List<Drill> drills = null;
        List<Game> games = null;
        List<KindergartenMonitor> kindergartenMonitors = null;
        if (matchType.equals("DRILL")) {
            drills = getDrillCount(userId, courseId, null);
            finish = drills.stream().filter(e -> e.getStatus().equals(MatchStatusEnum.FINISHED)).map(Drill::getId).collect(Collectors.toList());
            noFinish = (long) drills.size() - finish.size();
            upload = finish.stream()
                    .filter(e ->
                            personDataService.getOne(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, "DRILL").eq(PersonData::getStudentId, userId).eq(PersonData::getMatchId, e).last(" limit 1")) != null
                    ).count();
            userApp = getOne(new LambdaQueryWrapper<UserApp>().eq(UserApp::getHdStudentId, userId));
        } else if (matchType.equals("GAME")) {
            games = getGameCount(userId, courseId, null);
            finish = games.stream().filter(e -> e.getStatus().equals(MatchStatusEnum.FINISHED)).map(Game::getId).collect(Collectors.toList());
            noFinish = (long) games.size() - finish.size();
            upload = finish.stream()
                    .filter(e ->
                            personDataService.getOne(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, "GAME").eq(PersonData::getStudentId, userId).eq(PersonData::getMatchId, e).last(" limit 1")) != null
                    ).count();
            userApp = getOne(new LambdaQueryWrapper<UserApp>().eq(UserApp::getId, userId));
        } else {
            kindergartenMonitors = getMonitorCount(userId);
            finish = kindergartenMonitors.stream().filter(e -> e.getStatus().equals(MatchStatusEnum.FINISHED)).map(KindergartenMonitor::getId).collect(Collectors.toList());
            noFinish = (long) kindergartenMonitors.size() - finish.size();
            upload = finish.stream()
                    .filter(e ->
                            personDataService.getOne(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, "MONITOR").eq(PersonData::getStudentId, userId).eq(PersonData::getMatchId, e).last(" limit 1")) != null
                    ).count();
            userApp = getOne(new LambdaQueryWrapper<UserApp>().eq(UserApp::getHdStudentId, userId));
        }

        object.put("finish", finish.size());
        object.put("noFinish", noFinish);
        object.put("upload", upload);
        if (userApp != null) {
            List<Long> drillIds = getDrillCountPad(userApp.getHdStudentId(), courseId).stream().filter(e -> e.getStatus().equals(MatchStatusEnum.FINISHED)).map(Drill::getId).collect(Collectors.toList());

            if (drillIds.size() > 0) {
                noAppLookDrill = personDataService.count(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, "DRILL").eq(PersonData::getAppLook, 0)
                        .in(PersonData::getMatchId, drillIds).eq(PersonData::getStudentId, userApp.getHdStudentId()));
            }

            if (kindergartenMonitors == null) {
                kindergartenMonitors = getMonitorCount(userApp.getHdStudentId());
            }
            List<Long> monitorIds = kindergartenMonitors.stream().filter(e -> e.getStatus().equals(MatchStatusEnum.FINISHED)).map(KindergartenMonitor::getId).collect(Collectors.toList());
            if (monitorIds.size() > 0) {
                noAppLookMonitor = personDataService.count(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, "MONITOR").eq(PersonData::getAppLook, 0).in(PersonData::getMatchId, monitorIds).eq(PersonData::getStudentId, userApp.getHdStudentId()));
            }
        }
        object.put("noAppLookDrill", noAppLookDrill);
        object.put("noAppLookMonitor", noAppLookMonitor);

        return object;
    }

    public List<KindergartenMonitor> getMonitorCount(Long studentId) {
        // 筛选作废比赛
        List<Long> ids = kindergartenMonitorStudentService.list(new LambdaQueryWrapper<KindergartenMonitorStudent>()
                        .eq(KindergartenMonitorStudent::getStudentId, studentId).eq(KindergartenMonitorStudent::getCancel, false))
                .stream()
                .map(KindergartenMonitorStudent::getMonitorId)
                .collect(Collectors.toList());
        if (ids.size() == 0) {
            return new ArrayList<>();
        }
        return kindergartenMonitorService.list(new LambdaQueryWrapper<KindergartenMonitor>()
                .in(KindergartenMonitor::getId, ids));
    }

    public List<Game> getGameCount(Long userId, Long courseId, String dateString) {
        List<Long> teamIds = teamUserAppService.getTeamIdsByUserId(userId);
        // 筛选作废比赛
        List<Long> ids = gameStudentService.list(new LambdaQueryWrapper<GameStudent>()
                        .eq(GameStudent::getStudentId, userId).eq(GameStudent::getCancel, true))
                .stream()
                .map(GameStudent::getGameId)
                .collect(Collectors.toList());

        return gameService.list(new LambdaQueryWrapper<Game>()
                .notIn(ids.size() > 0, Game::getId, ids)
                .and(e -> e.in(teamIds.size() > 0, Game::getTeamId, teamIds).or().in(teamIds.size() > 0, Game::getOpponentId, teamIds).or().eq(Game::getCreateUserId, userId))
                .ge(dateString != null, Game::getCreateTime, dateString)
                .and(courseId != null, e -> e.lt(courseId == 4, Game::getCourseId, 2)
                        .eq(courseId != 4, Game::getCourseId, courseId)));
    }

    public List<Drill> getDrillCount(Long studentId, Long courseId, String dateString) {
        // 筛选作废比赛
        List<Long> ids = drillStudentService.list(new LambdaQueryWrapper<DrillStudent>()
                        .eq(DrillStudent::getStudentId, studentId).eq(DrillStudent::getCancel, true))
                .stream()
                .map(DrillStudent::getDrillId)
                .collect(Collectors.toList());


        return drillService.list(new LambdaQueryWrapper<Drill>().ne(Drill::getId, 0)
                .notIn(!ids.isEmpty(), Drill::getId, ids)
                .eq(Drill::getStudentId, studentId)
                .ge(dateString != null, Drill::getCreateTime, dateString)
                .and(courseId != null, e -> e.lt(courseId == 4, Drill::getCourseId, 2)
                        .eq(courseId != 4, Drill::getCourseId, courseId)));
    }

    public List<Drill> getDrillCountPad(Long studentId, Long courseId) {
        // 筛选作废比赛
        List<Long> ids = drillStudentService.list(new LambdaQueryWrapper<DrillStudent>()
                        .eq(DrillStudent::getStudentId, studentId).eq(DrillStudent::getCancel, false))
                .stream()
                .map(DrillStudent::getDrillId)
                .collect(Collectors.toList());


        return drillService.list(new LambdaQueryWrapper<Drill>().ne(Drill::getId, 0)
                .in(!ids.isEmpty(), Drill::getId, ids)
                .eq(Drill::getStudentId, 0)
                .and(courseId != null, e -> e.lt(courseId == 4, Drill::getCourseId, 2)
                        .eq(courseId != 4, Drill::getCourseId, courseId)));
    }

}
