package io.geekidea.springbootplus.using.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
@ApiModel(value = "KindergartenMonitorStuAo对象")
public class KindergartenMonitorStuAo {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("监测名")
    private String name;

    @ApiModelProperty("学校id")
    private Long schoolId;

    @ApiModelProperty("查询者所在球队")
    private Long teamId;

    @ApiModelProperty("监测状态(未开始:NOTSTARTED,已开始:STARTED,已结束:FINISHED) 新添加传入未开始状态")
    private MatchStatusEnum status;

    @ApiModelProperty("是否同步数据")
    private Boolean upload;

    @ApiModelProperty("查询者是否查看了这场数据")
    @TableField(exist = false)
    private Boolean appLook;

    @ApiModelProperty("监测开始时间")
    private Date startTime;

    @ApiModelProperty("监测结束时间")
    private Date stopTime;
}
