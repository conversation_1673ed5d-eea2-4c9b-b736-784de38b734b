package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MatchHardware对象")
public class MatchHardware extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "学员球鞋表id不能为空")
    @ApiModelProperty("学员球鞋表id")
    private Long hardwareId;

    @NotBlank(message = "DRILL||GAME不能为空")
    @ApiModelProperty("DRILL||GAME")
    private String matchType = "GAME";

    @NotNull(message = "球赛id不能为空")
    @ApiModelProperty("球赛id")
    private Long matchId;

    @ApiModelProperty("球赛id")
    private Long teamId;

    @ApiModelProperty("设备是否启动参加此比赛")
    private Boolean started;

    @ApiModelProperty("是否开启gps")
    private Integer gps;

    @ApiModelProperty("预留字段 训练代表训练环节id数组")
    private String reserved;

    @ApiModelProperty("设备启动时间")
    private Date startTime;

    @ApiModelProperty("是否app设备")
    private Boolean app;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
