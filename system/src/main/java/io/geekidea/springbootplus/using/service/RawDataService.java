package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.RawData;
import io.geekidea.springbootplus.using.param.RawDataPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface RawDataService extends BaseService<RawData> {

    List<RawData> findOfMatch(String matchType,Long matchId);

    List<RawData> findOfMatch(String matchType,Long matchId,Long teamId);

}
