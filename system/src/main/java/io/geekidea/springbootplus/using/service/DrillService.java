package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.param.DrillPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.vo.DailyDataSynthesizeAo;
import io.geekidea.springbootplus.using.vo.ExerciseConditionAo;
import io.geekidea.springbootplus.using.vo.PersonDataAo;
import io.geekidea.springbootplus.using.vo.PlayerAo;

import java.text.ParseException;
import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface DrillService extends BaseService<Drill> {

    /**
     * 保存
     *
     * @param drill
     * @return
     * @throws Exception
     */
    boolean saveDrill(Drill drill) throws Exception;

    /**
     * 修改
     *
     * @param drill
     * @return
     * @throws Exception
     */
    boolean updateDrill(Drill drill) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteDrill(Long id) throws Exception;


    Drill getDrill(Long id,Long teamId);
    /**
     * 获取分页对象
     *
     * @param drillQueryParam
     * @return
     * @throws Exception
     */
    Paging<Drill> getDrillPageList(DrillPageParam drillPageParam,Long teamId,Integer finish,Long courseId) throws Exception;

    /**
     * 获取分页对象
     *
     * @param drillQueryParam
     * @return
     * @throws Exception
     */
    Paging<Drill> getDrillPageListByStu(DrillPageParam drillPageParam,Long studentId,Integer finish) throws Exception;

    Paging<Drill> getDrillPageListByStu(DrillPageParam drillPageParam) throws Exception;

    Paging<Drill> getDrillPageListByStu1(DrillPageParam drillPageParam) throws Exception;
    /**
     * @desc: 结束训练
     * @author: DH
     * @date: 2022/5/9 9:26
     */
    Boolean stopDrill(Long drillId);

    /**
     * @desc: 上传数据
     * @author: DH
     * @date: 2022/4/29 11:09
     */
    boolean uploadData(String strJSONArray, Long matchId);

    /**
     * @desc: 查看数据
     * @author: DH
     * @date: 2022/4/29 14:56
     */
    Object getPlayerData(Long matchId, Long studentId,Long teamId,Boolean app);

    /**
     * @desc: 查看数据(提供鸵鸟)
     * @author: DH
     * @date: 2022/4/29 14:56
     */
    Object getPlayerDataTuoNiao(Long matchId, Long studentId,Long teamId,List<PersonData> personDataList);

    /**
     * @desc: 体能排行
     * @author: DH
     * @date: 2022/5/6 10:07
     */
    Object getPhysicalAnalysis(Long matchId,Long teamId);

    /**
     * @desc: 体能排行
     * @author: DH
     * @date: 2022/5/6 10:07
     */
    Object moveDistanceRanking(Long matchId, String rankingKey, List<PersonData> personDatas,Long teamId);

    /**
     * @desc: 训练进度
     * @author: DH
     * @date: 2022/4/29 16:30
     */
    List<PlayerAo> trainingPlan(Long drillId,Long teamId);

    /**
     * @desc: 获取上一场未同步赛事的赛事
     * @author: DH
     * @date: 2022/4/29 19:58
     */
    Drill unUploadOfDrill(Long teamId,Long drillId);

    Drill unUploadOfDrillStu(Long stuId,Long drillId);


    /**
     * @desc: 成长数据
     * @author: DH
     * @date: 2022/7/19 17:09
     */
    IPage<Drill> getGrowUpData(DrillPageParam drillPageParam);

    Object exerciseTime(Long teamId,String start,String stop);

    List<ExerciseConditionAo> exerciseConditionByMonth(Long studentId, Long date);

    List<ExerciseConditionAo> exerciseConditionByMonthStu(Long studentId, Long d);

    List<ExerciseConditionAo> exerciseConditionByTeam(Long teamId,Long date);

    List<ExerciseConditionAo> exerciseConditionByDayStu(Long teamId,Long studentId, Long d);

    DailyDataSynthesizeAo getDailyDataSynthesizeAndRun(Long teamId, String date) throws ParseException;

    JSONArray getDailyDataSynthesizeMoveDistanceRanking(Long teamId, String date, String rankingKey);

    Object getBestStu(Long teamId, String data);

    Object getRank(Long teamId,String date,Integer key);

    Object teamRank(Long teamId,Long drillId,Integer key);

    List<StudentInfo> attendance(Long teamId, String date);

    Object fieldRank(Long drillId,Long groupId,String key,Boolean zero);

    Boolean unUploadCount(Long teamId);

    Boolean startedCount(Long teamId);

    Integer getNoUploadCountByStu(Long studentId);


}
