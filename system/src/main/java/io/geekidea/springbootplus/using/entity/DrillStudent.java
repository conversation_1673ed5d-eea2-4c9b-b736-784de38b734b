package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DrillStudent对象")
@TableName(autoResultMap = true)
public class DrillStudent extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("训练表ID")
    private Long drillId;

    @ApiModelProperty("学生ID")
    private Long studentId;

    @ApiModelProperty("班级ID")
    private Long teamId;

    @ApiModelProperty("训练时间段")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON timeSlot = new JSONArray();

    @ApiModelProperty("true,正常：false缺勤")
    private Boolean status;

    @ApiModelProperty("是否首发 篮球模式用")
    private Boolean starter;

    @ApiModelProperty("球场四角是否定位成功")
    private Integer fourPosition;

    @ApiModelProperty("训练是否作废（个人）")
    private Boolean cancel;

    @ApiModelProperty("学生实际训练开始时间")
    private Date startTime;

    @ApiModelProperty("学生实际训练结束时间")
    private Date stopTime;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @TableLogic
    private Integer deleted;

    @ApiModelProperty("学号")
    @TableField(exist = false)
    private String card;


    @ApiModelProperty("学生名字")
    @TableField(exist = false)
    private String stuName;

    @ApiModelProperty("学生头像")
    @TableField(exist = false)
    private String image;
}
