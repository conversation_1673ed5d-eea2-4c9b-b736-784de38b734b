package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.Drill;
import io.geekidea.springbootplus.using.entity.Game;
import io.geekidea.springbootplus.using.entity.KindergartenMonitor;
import io.geekidea.springbootplus.using.entity.SchoolInfo;
import io.geekidea.springbootplus.using.service.DelayQueueService;
import io.geekidea.springbootplus.using.service.DrillService;
import io.geekidea.springbootplus.using.service.SchoolInfoService;
import io.geekidea.springbootplus.using.service.context.SpringContextUtils;
import io.geekidea.springbootplus.using.service.strategy.TaskStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
@Slf4j
public class DelayQueueServiceImpl implements DelayQueueService {

    @Resource
    volatile RedisUtils redisUtils;
    @Autowired
    DrillService drillService;
    @Autowired
    SchoolInfoService schoolInfoService;
    volatile TaskStrategy taskStrategy;
    private ExecutorService executorService=  Executors.newFixedThreadPool(10);

    //延迟时间（毫秒）
    long delay = 40 * 60 * 1000;

    /**
     * 比赛延迟队列监控器启动
     */
    @PostConstruct
    public void startDelayThread() {
        addDelayThread(CacheConsts.DRILLPASTDELAYQUEUE, 2000l);
        addDelayThread(CacheConsts.GAMEPASTDELAYQUEUE, 2000l);
        addDelayThread(CacheConsts.TEAMPASTDELAYQUEUE, 2000l);
        addSchoolMonitorDelayThread(2000l);
    }

    public void addDelayThread(String key, Long pollInterval) {
        log.info("开始监控延迟队列：{}", key);
        executorService.submit(() -> {
            try {
                Thread.sleep(10000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            while (true) {
                Long currentTime = System.currentTimeMillis();
                Set<Object> tasks = redisUtils.zSGet(key, -1.0, currentTime.doubleValue());
                if (tasks != null) {
                    tasks.forEach(task -> disposeTask(key, task));
                }
                try {
                    Thread.sleep(pollInterval);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    public void addSchoolMonitorDelayThread(Long pollInterval) {
        String key = "kindergartenMonitorDelayQueue";
        log.info("开始监控学校延迟队列：{}", key);
        executorService.submit(() -> {
            try {
                Thread.sleep(10000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            while (true) {
                Long currentTime = System.currentTimeMillis();
                List<SchoolInfo> schoolInfos = schoolInfoService.getSchoolInfoList();
                schoolInfos.forEach(school -> {
                    if (school.getMonitorTime() != null) {
                        long start = school.getMonitorTime().getTime();
                        long stop = school.getUploadTime().getTime();
                        Object startRedis = redisUtils.get(CacheConsts.KINDERGARTENMONITORDELAYQUEUESTART + school.getId());
                        Object stopRedis = redisUtils.get(CacheConsts.KINDERGARTENMONITORDELAYQUEUESTOP + school.getId());
                        if (stopRedis == null && currentTime >= stop) {
                            disposeTask("kindergartenMonitorDelayQueueStop", school);
                        } else if (startRedis == null && currentTime >= start) {
                            disposeTask("kindergartenMonitorDelayQueueStart", school);
                        }
                    }
                });
                try {
                    Thread.sleep(pollInterval);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public void addDrillDelayQueue(Drill drill) {
        long delayTime = drill.getLastStartTime().getTime() + drill.getDuration() * (60 * 1000);
        Boolean b = redisUtils.zSSet(CacheConsts.DRILLPASTDELAYQUEUE, drill.getId(), delayTime);
    }

    @Override
    public void removeDrillDelayQueue(Long drillId) {
        redisUtils.zSetRemove(CacheConsts.DRILLPASTDELAYQUEUE, drillId);
    }

    @Override
    public void addGameDelayQueue(Game game) {
        long delayTime = game.getLastStartTime().getTime() + game.getDuration() * (60 * 1000);
        Boolean b = redisUtils.zSSet(CacheConsts.GAMEPASTDELAYQUEUE, game.getId(), delayTime);
    }

    @Override
    public void removeGameDelayQueue(Long gameId) {
        redisUtils.zSetRemove(CacheConsts.GAMEPASTDELAYQUEUE, gameId);
    }

    @Override
    public void addMoreRestrictDelayQueue(Long teamId, Long delayTime) {
        Boolean b = redisUtils.zSSet(CacheConsts.TEAMPASTDELAYQUEUE, teamId, delayTime);
    }

    @Override
    public void removeGroupMoreRestrictDelayQueue(Long teamId) {
        redisUtils.zSetRemove(CacheConsts.TEAMPASTDELAYQUEUE, teamId);
    }

    @Override
    public void addkindergartenMonitor(KindergartenMonitor kindergartenMonitor) {
        //redisUtils.zSSet(CacheConsts.KINDERGARTENMONITORDELAYQUEUESTART, kindergartenMonitor.getId(), kindergartenMonitor.getStartTime().getTime());
        // redisUtils.zSSet(CacheConsts.KINDERGARTENMONITORDELAYQUEUESTOP, kindergartenMonitor.getId(), kindergartenMonitor.getStopTime().getTime());
    }

    @Override
    public void removekindergartenMonitor(String key, Long id) {
        redisUtils.zSetRemove(key, id);
        redisUtils.zSetRemove(key, id);
    }

    /**
     * 处理任务
     *
     * @param key
     * @param task
     */
    public synchronized void disposeTask(String key, Object task) {
        log.info("正在处理任务：{}", key);
        taskStrategy = SpringContextUtils.getBeans(TaskStrategy.class).get(key);
        taskStrategy.dispose(task);
    }
}
