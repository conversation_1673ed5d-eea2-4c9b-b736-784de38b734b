package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ProDataServiceImpl implements ProDataService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    @Lazy
    private FieldNotesService fieldNotesService;
    @Autowired
    @Lazy
    private FieldGroupService fieldGroupService;
    @Autowired
    @Lazy
    private FieldDataService fieldDataService;
    @Autowired
    @Lazy
    private DrillService drillService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private TeamAppService teamAppService;
    @Autowired
    private PersonDataService personDataService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private UserAppService userAppService;

    /**
     * @desc: 综合得分及排名
     * @author: DH
     * @date: 2023/8/17 16:29
     */
    public Object getComprehensiveScoreRanking(Long teamId, Long matchId, String matchType, Long stuId, Boolean isNew, JSONObject teamRankingData) {
        //缓存起来
        Object redisArray = getComprehensiveScoreRankingCacheArray(teamId, matchId, matchType);
        JSONArray jsonArray = redisArray == null ? new JSONArray() : JSON.parseArray(JSON.toJSONString(redisArray));
        if (redisArray == null || isNew) {
            List<PersonData> personDatas = personDataService.findOfMatchId(matchId, teamId, matchType);
            FieldNotes fieldNotes = fieldNotesService.getOne(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, matchType).eq(FieldNotes::getMatchId, matchId).last(" limit 0,1"));
            List<FieldData> fieldDataList = fieldDataService.list(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId, fieldNotes.getId()));
            Map<Long, FieldData> fieldDataMap = fieldDataList.stream()
                    .collect(Collectors.toMap(FieldData::getStuId, Function.identity()));

            FieldNotes fieldNotesArrow = fieldNotesService.getOne(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, matchType).eq(FieldNotes::getMatchId, personDataService.getArrow(stuId, matchId, matchType).getMatchId()).last(" limit 0,1"));
            if (fieldNotesArrow == null) {
                fieldNotesArrow = new FieldNotes();
            }
            List<FieldData> fieldDataListArrow = fieldDataService.list(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId, fieldNotesArrow.getId()));
            if (fieldDataListArrow == null) {
                fieldDataListArrow = new ArrayList<>();
            }
            Map<Long, FieldData> fieldDataMapArrow = fieldDataListArrow.stream()
                    .collect(Collectors.toMap(FieldData::getStuId, Function.identity()));
            if (matchType.equals("DRILL")) {
                Drill drill = drillService.getById(matchId);
                Map<Long, StudentInfo> studentInfoMap = studentInfoService.getStusByTeamId(teamId)
                        .stream().collect(Collectors.toMap(StudentInfo::getId, Function.identity()));
                for (Map.Entry<Long, StudentInfo> stu : studentInfoMap.entrySet()) {
                    if (fieldDataMap.get(stu.getKey()) != null) {
                        jsonArray.add(getSpecialistRankingDataDrill(drill, stu.getValue(), personDatas, fieldDataList, fieldDataMap.get(stu.getValue().getId()), fieldDataMapArrow.get(stu.getValue().getId())));
                    }
                }
            } else {
                Map<Long, UserApp> studentInfoMap = userAppService.getByTeamId(teamId)
                        .stream().collect(Collectors.toMap(UserApp::getId, Function.identity()));
                for (Map.Entry<Long, UserApp> stu : studentInfoMap.entrySet()) {
                    if (fieldDataMap.get(stu.getKey()) != null) {
                        jsonArray.add(getSpecialistRankingDataGame(stu.getValue(), personDatas, fieldDataList, fieldDataMap.get(stu.getValue().getId()), fieldDataMapArrow.get(stu.getValue().getId())));

                    }
                }
            }

            bkRate(teamRankingData, fieldDataList);//投球成功率
            resetScore(jsonArray, fieldDataList.get(0).getFieldId());//重置分数
            spider(jsonArray, teamRankingData);//蜘蛛图
            sortScoreRankingJSONArray(fieldDataList.size(), jsonArray, teamRankingData, matchType);
            teamGroupControl(teamRankingData, fieldDataList);
            cacheComprehensiveScoreRankingArray(teamId, matchId, matchType, jsonArray, teamRankingData);
        }

        if (stuId != null && stuId != -1L && stuId != 0L) {
            for (int i = 0; i < jsonArray.size(); i++) {
                if (jsonArray.getJSONObject(i).getLong("studentId").equals(stuId)) {
                    return jsonArray.getJSONObject(i);
                }
            }
        } else {
            return jsonArray.size() > 4 ? jsonArray.stream().limit(3) : jsonArray;
        }
        return null;
    }

    @Override
    public Object getProTeamData(Long teamId, Long matchId, String matchType) {
        JSONObject teamRankingData = new JSONObject();
        Object o = redisUtils.get(CacheConsts.GETCOMPREHENSIVESCORERANKINGTEAM + matchType + "-" + teamId + "-" + matchId);
        if (o != null) {
            teamRankingData = (JSONObject) o;
        } else {
            getComprehensiveScoreRanking(teamId, matchId, matchType, -1L, false, teamRankingData);
        }
        return teamRankingData;
    }


    public void resetScore(JSONArray jsonArray, Long fieldId) {
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            if (object.getDouble("score") != 0) {
                Long winGroupId = 0l;
                List<FieldGroup> fieldGroupList = fieldGroupService.getFieldGroupByNotesId(fieldId);
                if (fieldGroupList.get(0).getGroupFraction() > fieldGroupList.get(1).getGroupFraction()) {
                    winGroupId = fieldGroupList.get(0).getId();
                } else {
                    winGroupId = fieldGroupList.get(1).getId();
                }
                double faultSum = object.getJSONObject("fault").getDoubleValue("sumScore");
                double doubleScore = getScore(object.getDouble("score") - faultSum, winGroupId.equals(object.getLong("groupId")));
                if (doubleScore > 10) {
                    doubleScore = 10;
                }

                if (object.getBoolean("lose") != null && object.getBoolean("lose")) {
                    doubleScore = doubleScore * 0.8;
                }
                JSONObject sumSplitRatio = resetScoreSplitRatio(doubleScore);
                double runningFitnessScore = object.getJSONObject("runningFitness").getDoubleValue("sumScore");
                double offenseScore = object.getJSONObject("offense").getDoubleValue("sumScore");
                double guardScore = object.getJSONObject("guard").getDoubleValue("sumScore");
                if (runningFitnessScore > 0 && offenseScore > 0 && guardScore > 0) {
                    //1 1 1
                    runningFitnessScore = sumSplitRatio.getDoubleValue("part1");
                    offenseScore = sumSplitRatio.getDoubleValue("part2");
                    guardScore = sumSplitRatio.getDoubleValue("part3");

                } else if (runningFitnessScore > 0 && offenseScore > 0 && guardScore == 0) {
                    //1 1 0
                    runningFitnessScore = sumSplitRatio.getDoubleValue("part1") + (sumSplitRatio.getDoubleValue("part3") / 2);
                    offenseScore = sumSplitRatio.getDoubleValue("part2") + (sumSplitRatio.getDoubleValue("part3") / 2);

                } else if (runningFitnessScore > 0 && offenseScore == 0 && guardScore == 0) {
                    //1 0 0
                    runningFitnessScore = sumSplitRatio.getDoubleValue("part1") + sumSplitRatio.getDoubleValue("part2") + sumSplitRatio.getDoubleValue("part3");

                } else if (runningFitnessScore > 0 && offenseScore == 0 && guardScore > 0) {
                    //1 0 1
                    runningFitnessScore = sumSplitRatio.getDoubleValue("part1") + (sumSplitRatio.getDoubleValue("part2") / 2);
                    guardScore = sumSplitRatio.getDoubleValue("part3") + (sumSplitRatio.getDoubleValue("part2") / 2);
                } else if (runningFitnessScore == 0 && offenseScore > 0 && guardScore > 0) {
                    //0 1 1
                    offenseScore = sumSplitRatio.getDoubleValue("part2") + (sumSplitRatio.getDoubleValue("part1") / 2);
                    guardScore = sumSplitRatio.getDoubleValue("part3") + (sumSplitRatio.getDoubleValue("part1") / 2);

                } else if (runningFitnessScore == 0 && offenseScore > 0 && guardScore == 0) {
                    //0 1 0
                    offenseScore = sumSplitRatio.getDoubleValue("part1") + sumSplitRatio.getDoubleValue("part2") + sumSplitRatio.getDoubleValue("part3");
                } else if (runningFitnessScore == 0 && offenseScore == 0 && guardScore > 1) {
                    //0 0 1
                    guardScore = sumSplitRatio.getDoubleValue("part1") + sumSplitRatio.getDoubleValue("part2") + sumSplitRatio.getDoubleValue("part3");
                }
                double faultScore = getScore(object.getJSONObject("fault").getDoubleValue("sumScore"), winGroupId.equals(object.getLong("groupId")));
                if (faultScore < -10) {
                    faultScore = -10;
                }

                double sum = runningFitnessScore + offenseScore + guardScore + faultScore;
                if (sum > 10 && sum <= 10.1) {
                    if (runningFitnessScore > offenseScore && runningFitnessScore > guardScore) {
                        runningFitnessScore = runningFitnessScore - (sum - 10);
                    } else if (offenseScore > runningFitnessScore && offenseScore > guardScore) {
                        offenseScore = offenseScore - (sum - 10);
                    } else if (guardScore > runningFitnessScore && guardScore > offenseScore) {
                        guardScore = guardScore - (sum - 10);
                    }
                }
                object.getJSONObject("runningFitness").put("sumScore", String.format("%.1f", runningFitnessScore));
                object.getJSONObject("offense").put("sumScore", String.format("%.1f", offenseScore));
                object.getJSONObject("guard").put("sumScore", String.format("%.1f", guardScore));
                object.getJSONObject("fault").put("sumScore", String.format("%.1f", faultScore));

                String score = String.format("%.1f", object.getJSONObject("runningFitness").getDouble("sumScore") + object.getJSONObject("offense").getDouble("sumScore")
                        + object.getJSONObject("guard").getDouble("sumScore") + object.getJSONObject("fault").getDouble("sumScore"));
                object.put("score", score);
            }
        }
    }

    public void spider(JSONArray jsonArray, JSONObject team) {
        double runningFitnessScoreTeam = 0;
        double offenseScoreTeam = 0;
        double guardScoreTeam = 0;
        double faultScoreTeam = 0;
        double synthesisTeam = 0;
        JSONObject spiderTeam = new JSONObject();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            JSONObject spider = new JSONObject();
            double runningFitnessScore = object.getJSONObject("runningFitness").getDoubleValue("sumScore");
            double offenseScore = object.getJSONObject("offense").getDoubleValue("sumScore");
            double guardScore = object.getJSONObject("guard").getDoubleValue("sumScore");
            double faultScore = object.getJSONObject("fault").getDoubleValue("sumScore");
            double synthesis = object.getDoubleValue("score");
            spider.put("synthesis", synthesis);
            spider.put("runningFitness", String.format("%.1f", runningFitnessScore));
            spider.put("offense", String.format("%.1f", offenseScore));
            spider.put("guard", String.format("%.1f", guardScore));
            spider.put("fault", String.format("%.1f", faultScore));

            spider.put("surpass", String.format("%.1f", (synthesis * 0.9 * 10)));
            object.put("spider", spider);

            runningFitnessScoreTeam += runningFitnessScore;
            offenseScoreTeam += offenseScore;
            guardScoreTeam += guardScore;
            faultScoreTeam += faultScore;
            synthesisTeam += synthesis;
        }

        spiderTeam.put("synthesis", synthesisTeam / jsonArray.size());
        spiderTeam.put("runningFitness", String.format("%.1f", runningFitnessScoreTeam / jsonArray.size()));
        spiderTeam.put("offense", String.format("%.1f", offenseScoreTeam / jsonArray.size()));
        spiderTeam.put("guard", String.format("%.1f", guardScoreTeam / jsonArray.size()));
        spiderTeam.put("fault", String.format("%.1f", faultScoreTeam / jsonArray.size()));
        spiderTeam.put("surpass", String.format("%.1f", ((synthesisTeam * 0.9) / jsonArray.size()) * 10));
        team.put("spider", spiderTeam);
    }

    public static double calculatePercentageOf4(double y) {
        double x = 4.0;
        if (y > x) {
            double surpass = (double) (y - x) / 4;
            return surpass;
        } else {
            double below = (double) (x - y) / 4;
            return -below;
        }
    }

    public JSONObject resetScoreSplitRatio(double v) {
        JSONObject ratio = new JSONObject();
        double part1 = v * 0.40; // 第一份数值
        double part2 = v * 0.35; // 第二份数值
        double part3 = v * 0.25; // 第三份数值

        ratio.put("part1", part1);
        ratio.put("part2", part2);
        ratio.put("part3", part3);
        return ratio;
    }

    public Double getScoreByRanking(double inputValue) {
        double v = 0.0;
        if (inputValue >= 8) {
            v = 1;
        } else if (inputValue >= 7) {
            v = 2;
        } else if (inputValue >= 6) {
            v = 3;
        } else if (inputValue >= 5) {
            v = 4;
        } else if (inputValue >= 4) {
            v = 5;
        } else if (inputValue >= 3) {
            v = 6;
        } else if (inputValue >= 2) {
            v = 7;
        } else if (inputValue >= 1) {
            v = 8;
        }
        return v;
    }

    public void bkRate(JSONObject teamRankingData, List<FieldData> fieldDataList) {
        teamRankingData.put("rate", calculateBatRateTeam(fieldDataList));
    }

    public void teamGroupControl(JSONObject teamRankingData, List<FieldData> fieldDataList) {
        List<FieldGroup> fieldGroupList = fieldGroupService.getFieldGroupByNotesId(fieldDataList.get(0).getFieldId());
        if (fieldGroupList.size() > 1) {
            teamRankingData.put("teamControlTime", fieldGroupList.get(0).getGroupControl());
            teamRankingData.put("opponentControlTime", fieldGroupList.get(1).getGroupControl());
        } else {
            teamRankingData.put("teamControlTime", 0);
            teamRankingData.put("opponentControlTime", 0);
        }
    }

    public JSONObject groupControl(FieldData fieldData) {
        List<FieldGroup> fieldGroupList = fieldGroupService.getFieldGroupByNotesId(fieldData.getFieldId());
        JSONObject o = new JSONObject();
        if (fieldGroupList.size() > 1) {
            o.put("teamControlTime", fieldGroupList.get(0).getGroupControl());
            o.put("opponentControlTime", fieldGroupList.get(1).getGroupControl());
        } else {
            o.put("teamControlTime", 0);
            o.put("opponentControlTime", 0);
        }
        return o;
    }

    @Override
    public Object highLadder(Long teamId, Long matchId, String matchType, Long studentId) {
        JSONObject getComprehensiveScoreRanking = (JSONObject) getComprehensiveScoreRanking(teamId, matchId, matchType, studentId, false, new JSONObject());
        int ranking = 0;
        int count = 0;
        if (matchType.equals("DRILL")) {
            ranking = getComprehensiveScoreRanking.getInteger("ranking");
            count = getComprehensiveScoreRanking.getInteger("count");
        } else {
            ranking = getComprehensiveScoreRanking.getInteger("pentacle");
            count = 10;
        }
        int grade = getComprehensiveScoreRanking.getInteger("grade");
        JSONObject highLadder = new JSONObject();
        highLadder.put("stuName", getComprehensiveScoreRanking.getString("name"));
        highLadder.put("headImg", getComprehensiveScoreRanking.getString("headImg"));
        Map<Integer, Integer> map = getScoreHighLadderMap();
        int f = map.get(getGradeByLevel(grade));
        double x = (1 - ((double) (ranking / count))) * f;
        int pentacle = (int) Math.ceil(x);
        highLadder.put("pentacle", pentacle);
        highLadder.put("index", pentacle > 0 ? pentacle - 1 : 0);
        highLadder.put("level", getGradeByLevel(grade));
        return highLadder;
    }

    /**
     * @desc: 个人专业排名数据
     * @author: DH
     * @date: 2023/8/16 16:42
     */
    public JSONObject getSpecialistRankingDataDrill(Drill drill, StudentInfo studentInfo, List<PersonData> personDatas, List<FieldData> fieldDataList, FieldData fieldData, FieldData fieldDataArrow) {
        Team team = teamService.getById(drill.getTeamId());
        int grade = team.getGrade();
        JSONObject object = new JSONObject();
        //跑动体能
        JSONObject runningFitness = calculateRunRankingScore(studentInfo.getId(), personDatas, grade);
        //进攻
        JSONObject offense = calculateFieldRankingScore("offense", fieldDataList, fieldData, fieldDataArrow, grade,"DRILL");
        //防守
        JSONObject guard = calculateFieldRankingScore("guard", fieldDataList, fieldData, fieldDataArrow, grade,"DRILL");
        //失误犯规
        JSONObject fault = calculateFieldRankingScore("fault", fieldDataList, fieldData, fieldDataArrow, grade,"DRILL");
        //总分
        double sumScore = runningFitness.getDoubleValue("sumScore") + offense.getDoubleValue("sumScore") + guard.getDoubleValue("sumScore") + fault.getDoubleValue("sumScore");
        String formatted = String.format("%.1f", sumScore);
        object.put("runningFitness", runningFitness);
        object.put("offense", offense);
        object.put("guard", guard);
        object.put("fault", fault);
        object.put("control", groupControl(fieldData));
        object.put("groupId", fieldData.getGroupId());
        object.put("score", formatted);
        object.put("studentId", studentInfo.getId());
        object.put("name", studentInfo.getName());
        object.put("card", studentInfo.getCard());
        object.put("headImg", studentInfo.getHeadImg());
        object.put("footballShirt", studentInfo.getFootballShirt());
        object.put("shirt", studentInfo.getShirt());
        object.put("grade", grade);
        object.put("count", fieldDataList.size());
        return object;
    }

    public JSONObject getSpecialistRankingDataGame(UserApp studentInfo, List<PersonData> personDatas, List<FieldData> fieldDataList, FieldData fieldData, FieldData fieldDataArrow) {
        int grade = studentInfo.getGrade();
        JSONObject object = new JSONObject();
        //跑动体能
        JSONObject runningFitness = calculateRunRankingScore(studentInfo.getId(), personDatas, grade);
        //进攻
        JSONObject offense = calculateFieldRankingScore("offense", fieldDataList, fieldData, fieldDataArrow, grade,"GAME");
        //防守
        JSONObject guard = calculateFieldRankingScore("guard", fieldDataList, fieldData, fieldDataArrow, grade,"GAME");
        //失误犯规
        JSONObject fault = calculateFieldRankingScore("fault", fieldDataList, fieldData, fieldDataArrow, grade,"GAME");
        //总分
        double sumScore = runningFitness.getDoubleValue("sumScore") + offense.getDoubleValue("sumScore") + guard.getDoubleValue("sumScore") + fault.getDoubleValue("sumScore");

        Long winGroupId = 0l;
        List<FieldGroup> fieldGroupList = fieldGroupService.getFieldGroupByNotesId(fieldData.getFieldId());
        if (fieldGroupList.get(0).getGroupFraction() < fieldGroupList.get(1).getGroupFraction()) {
            winGroupId = fieldGroupList.get(0).getId();
        } else {
            winGroupId = fieldGroupList.get(1).getId();
        }
        if (runningFitness.getIntValue("value") < 6000 || fieldData.getGroupId() != winGroupId) {
            object.put("lose", true);
        }

        String formatted = String.format("%.1f", sumScore);
        object.put("runningFitness", runningFitness);
        object.put("offense", offense);
        object.put("guard", guard);
        object.put("fault", fault);
        object.put("score", formatted);
        object.put("studentId", studentInfo.getId());
        object.put("name", studentInfo.getName());
        object.put("card", studentInfo.getCard());
        object.put("headImg", studentInfo.getHeadImg());
        object.put("footballShirt", studentInfo.getFootballShirt());
        object.put("grade", grade);
        object.put("count", fieldDataList.size());
        return object;
    }


    /**
     * @desc: 计算跑动消耗排名得分
     * @author: DH
     * @date: 2023/8/16 16:08
     */
    public JSONObject calculateRunRankingScore(Long stuId, List<PersonData> personDatas, int grade) {
        JSONObject runScoreJson = new JSONObject();
        //跑动
        JSONObject ranking = new JSONObject();//排名前
        List<String> rankingKey = getRankingScoreKey();
        rankingKey.stream().forEach(key -> ranking.put(key, new JSONArray()));

        JSONObject avg =  new JSONObject();

        personDatas.stream().forEach(e -> {
            JSONObject o;
            PersonData arrow = personDataService.getArrow(e.getStudentId(), e.getMatchId(), e.getMatchType());
            //跑动
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getRunDistance());
            o.put("arrow", getRunArrow(e, arrow, "runDistance"));
            ranking.getJSONArray("runDistance").add(o);
            // 最高冲刺速度
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getMaxSprintSpeed());
            o.put("arrow", getRunArrow(e, arrow, "maxSprintSpeed"));
            ranking.getJSONArray("maxSprintSpeed").add(o);
            // 高速跑动距离
            o = new JSONObject();
            List<Object> speedDatas = e.getSpeedDataList().toJavaObject(JSONArray.class).stream().collect(Collectors.toList());
            o.put("k", e.getStudentId());
            o.put("v", speedDatas.stream().filter(a -> ((JSONObject) a).getString("speed").equals("SUP")).mapToInt(a -> ((JSONObject) a).getInteger("distance")).sum());
            o.put("turning", getRunArrow(e, arrow, "highDistance"));
            ranking.getJSONArray("highDistance").add(o);
            // {"LOW":0-6km/h的距离,"MID":5-15km/h的距离,"HIGH":15-18,"EXCEED":18-24km/h的距离,"SUP":大于24km/h的距离}
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", speedDatas.stream().filter(a -> ((JSONObject) a).getString("speed").equals("SUP")).mapToInt(a -> ((JSONObject) a).getInteger("count")).sum());
            ranking.getJSONArray("nsHighCount").add(o);
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", speedDatas.stream().filter(a -> ((JSONObject) a).getString("speed").equals("LOW")).mapToInt(a -> ((JSONObject) a).getInteger("count")).sum());
            ranking.getJSONArray("nsLowCount").add(o);
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", speedDatas.stream().filter(a -> ((JSONObject) a).getString("speed").equals("MID")).mapToInt(a -> ((JSONObject) a).getInteger("count")).sum());
            ranking.getJSONArray("nsMidCount").add(o);
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", speedDatas.stream().filter(a -> ((JSONObject) a).getString("speed").equals("EXCEED")).mapToInt(a -> ((JSONObject) a).getInteger("count")).sum());
            ranking.getJSONArray("nsExceedCount").add(o);
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", speedDatas.stream().filter(a -> ((JSONObject) a).getString("speed").equals("SUP")).mapToInt(a -> ((JSONObject) a).getInteger("count")).sum());
            ranking.getJSONArray("nsSupCount").add(o);
            // 消耗
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getCalorie());
            o.put("arrow", getRunArrow(e, arrow, "calorie"));
            ranking.getJSONArray("calorie").add(o);
            // 变向
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getDepthData().getIntValue("leftTurning") + e.getDepthData().getIntValue("rightTurning"));
            o.put("arrow", getRunArrow(e, arrow, "turning"));
            ranking.getJSONArray("turning").add(o);
            // 急停
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getDepthData().getIntValue("brake"));
            o.put("arrow", getRunArrow(e, arrow, "jerk"));
            ranking.getJSONArray("jerk").add(
                    o);
        });
        double score = 0;
        if (stuId != null) {
            for (String e : rankingKey) {
                JSONArray jsonArray = null;
                if (!e.substring(0, 2).equals("ns")) {
                    jsonArray = sortJSONArray(ranking.getJSONArray(e), "v");
                }
                if (jsonArray != null) {
                    avg.put(e,jsonArray.stream().mapToInt(a ->{
                        JSONObject jsonObject=(JSONObject) a;
                        return jsonObject.getIntValue(e);
                    }).average().orElse(0.0));
                    for (int i = 0; i < jsonArray.size(); i++) {
                        if (jsonArray.getJSONObject(i).getLong("k").equals(stuId)) {
                            JSONObject o = new JSONObject();
                            double value = jsonArray.getJSONObject(i).getDoubleValue("v");
                            int rk = i + 1;
                            o.put("ranking", rk);
                            o.put("value", value);
                            if(personDatas.get(0).getMatchType().equals("DRILL")) {//团队版计算团体平均
                                o.put("arrow", getRunArrowAvg((int) value, avg.getDouble(e).intValue()));
                            }else{
                                o.put("arrow",jsonArray.getJSONObject(i).getDoubleValue("arrow"));
                            }
                            o.put("score", value == 0 ? 0 : calculateRunScore(rk, jsonArray.size(), e, value));
                            if (!e.substring(0, 2).equals("ns")) {
                                runScoreJson.put(e, o);
                            }
                            score += o.getDoubleValue("score");
                            break;
                        }
                    }

                }
            }
        }

        runScoreJson.put("sumScore", score);
        return runScoreJson;
    }

    public double getScore(double score, boolean win) {
        double newScore = score * 0.35;
        if (!win) {
            newScore = newScore * 0.8;
        }
        return newScore;
    }

    /**
     * @desc: 个人计算场记排名得分
     * @author: DH
     * @date: 2023/8/16 16:08
     */
    public JSONObject calculateFieldRankingScore(String key, List<FieldData> fieldDatas, FieldData fieldData, FieldData fieldDataArrow, int grade,String matchType) {
        //得分为分值*次数*系数；
        JSONObject dataMap = fieldData.getDataMap();
        JSONObject fieldDataScoreJson = new JSONObject();//进攻
        List<String> offenseKeys = getFieldDataMapKeys(key);
        double score = 0;
        for (String k : offenseKeys) {
            JSONObject o = new JSONObject();
            o.put("value", dataMap != null ? getFieldDataOrDefault(dataMap, k) : null);
            o.put("ranking", dataMap != null ? rankingFieldDataByKey(fieldDatas, k, fieldData.getStuId()) : 0);
            o.put("score", dataMap != null ? (key.equals("fault") ? (getFieldDataScoreByKey(k) * o.getInteger("value")) : (getFieldDataScoreByKey(k) * o.getInteger("value") * getGradeByCoefficient(grade))) : 0);
            if(matchType.equals("DRILL")){
                o.put("arrow", setArrowAvg(fieldDatas,dataMap, offenseKeys,k));
            }else{
                if(fieldDataArrow!=null) {
                    o.put("arrow", setArrow(dataMap, fieldDataArrow.getDataMap(), k));
                }else{
                    o.put("arrow", -1);
                }
            }

            score += o.getDoubleValue("score");
            fieldDataScoreJson.put(k, o);
        }
        fieldDataScoreJson.put("sumScore", score);
        if (key.equals("offense")) {
            //篮球计算投球成功率
            fieldDataScoreJson.put("rate", calculateBatRate(fieldData));
        }
        return fieldDataScoreJson;
    }

    public Integer getRunArrow(PersonData e, PersonData arrowPersonData, String key) {
        if (arrowPersonData == null||e==null) {
            return 0;
        }

        if (key.equals("runDistance")) {
            if (e.getRunDistance() > arrowPersonData.getRunDistance()) {
                return 1;
            } else {
                return -1;
            }
        } else if (key.equals("maxSprintSpeed")) {
            if (e.getMaxSprintSpeed() > arrowPersonData.getMaxSprintSpeed()) {
                return 1;
            } else {
                return -1;
            }
        } else if (key.equals("highDistance")) {
            List<Object> speedDatas = e.getSpeedDataList().toJavaObject(JSONArray.class).stream().collect(Collectors.toList());
            if (arrowPersonData.getSpeedDataList() != null) {
                List<Object> speedDatasArrow = arrowPersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().collect(Collectors.toList());
                if (speedDatas.stream().filter(a -> ((JSONObject) a).getString("speed").equals("SUP")).mapToInt(a -> ((JSONObject) a).getInteger("distance")).sum()
                        > speedDatasArrow.stream().filter(a -> ((JSONObject) a).getString("speed").equals("SUP")).mapToInt(a -> ((JSONObject) a).getInteger("distance")).sum()) {
                    return 1;
                } else {
                    return -1;
                }
            }
        } else if (key.equals("calorie")) {
            if (e.getCalorie() < arrowPersonData.getCalorie()) {
                return 1;
            } else {
                return -1;
            }
        } else if (key.equals("turning")) {
            if (arrowPersonData.getDepthData() != null) {
                if ((e.getDepthData().getIntValue("leftTurning") + e.getDepthData().getIntValue("rightTurning")) > (arrowPersonData.getDepthData().getIntValue("leftTurning") + arrowPersonData.getDepthData().getIntValue("rightTurning"))) {
                    return 1;
                } else {
                    return -1;
                }
            }
        } else if (key.equals("jerk")) {
            if (arrowPersonData.getDepthData() != null) {
                if (e.getDepthData().getIntValue("brake") > arrowPersonData.getDepthData().getIntValue("brake")) {
                    return 1;
                } else {
                    return -1;
                }
            }
        }
        return 0;
    }

    public Integer getRunArrowAvg(Integer v, Integer arrowPersonData) {
        if (v> arrowPersonData) {
            return 1;
        } else {
            return -1;
        }
    }

    public Integer setArrow(JSONObject jsonObject, JSONObject arrowField, String k) {
        if (arrowField == null || arrowField == null) {
            return 0;
        }
        if (jsonObject == null) {
            return 0;
        }
        if (getFieldDataOrDefault(jsonObject, k) > getFieldDataOrDefault(arrowField, k)) {
            return 1;
        } else {
            return -1;
        }
    }

    public int setArrowAvg(List<FieldData> fieldDatas,JSONObject jsonObject,List<String> keys ,String key) {
        if(fieldDatas.size()==0){
            return 0;
        }
        JSONObject arrowField=new JSONObject();
        for (String k:keys) {
            Double average = fieldDatas.stream()
                    .mapToInt(fd -> {
                        FieldData o = (FieldData) fd;
                        return getFieldDataOrDefault(o.getDataMap(), k);
                    }).average().orElse(0.0);
            arrowField.put(k,average.intValue());
        }
        if (getFieldDataOrDefault(jsonObject, key) > getFieldDataOrDefault(arrowField, key)) {
            return 1;
        } else {
            return -1;
        }
        
    }

    //权重
    public double getWeightByKeY(String key) {
        if (key.equals("offense")) {//进攻
            return 1;
        } else if (key.equals("guard")) {//防守
            return 1;
        } else if (key.equals("run")) {//跑动
            return 1;
        } else {
            return 1;
        }
    }

    //篮球计算投球成功率
    public JSONObject calculateBatRate(FieldData fieldData) {
        JSONObject playerData = fieldData.getDataMap();
        if (playerData == null) {
            playerData = new JSONObject();
        }
        JSONObject o = new JSONObject();
        // 计算一分命中率
        int onePointer = (int) playerData.getOrDefault("onePointer", 0);
        int nullone = (int) playerData.getOrDefault("nullone", 0);
        double onePointHitRate = (double) onePointer / (onePointer + nullone) * 100;
        o.put("onePointHitRate", Math.round(onePointHitRate));
        // 计算二分命中率
        int twoPointer = (int) playerData.getOrDefault("twoPointer", 0);
        int nulltwo = (int) playerData.getOrDefault("nulltwo", 0);
        double twoPointHitRate = (double) twoPointer / (twoPointer + nulltwo) * 100;
        o.put("twoPointHitRate", Math.round(twoPointHitRate));
        // 计算三分命中率
        int threePointer = (int) playerData.getOrDefault("threePointer", 0);
        int nullthree = (int) playerData.getOrDefault("nullthree", 0);
        double threePointHitRate = (double) threePointer / (threePointer + nullthree) * 100;
        o.put("threePointHitRate", Math.round(threePointHitRate));
        // 计算总命中率
        int totalShots = onePointer + nullone + twoPointer + nulltwo + threePointer + nullthree;
        int totalHits = onePointer + twoPointer + threePointer;
        double totalHitRate = (double) totalHits / totalShots * 100;
        o.put("totalHitRate", Math.round(totalHitRate));
        return o;
    }

    public JSONObject calculateBatRateTeam(List<FieldData> fieldDatas) {
        JSONObject o = new JSONObject();
        int onePointer = 0;
        int nullone = 0;
        int twoPointer = 0;
        int nulltwo = 0;
        int threePointer = 0;
        int nullthree = 0;
        for (FieldData fieldData : fieldDatas) {
            JSONObject playerData = fieldData.getDataMap();
            if (playerData == null) {
                playerData = new JSONObject();
            }
            // 计算一分命中率
            onePointer += (int) playerData.getOrDefault("onePointer", 0);
            nullone += (int) playerData.getOrDefault("nullone", 0);
            // 计算二分命中率
            twoPointer = (int) playerData.getOrDefault("twoPointer", 0);
            nulltwo = (int) playerData.getOrDefault("nulltwo", 0);
            // 计算三分命中率
            threePointer += (int) playerData.getOrDefault("threePointer", 0);
            nullthree += (int) playerData.getOrDefault("nullthree", 0);
        }
        int totalShots = onePointer + nullone + twoPointer + nulltwo + threePointer + nullthree;
        int totalHits = onePointer + twoPointer + threePointer;
        double onePointHitRate = (double) onePointer / (onePointer + nullone) * 100;
        o.put("onePointHitRate", Math.round(onePointHitRate));
        double twoPointHitRate = (double) twoPointer / (twoPointer + nulltwo) * 100;
        o.put("twoPointHitRate", Math.round(twoPointHitRate));
        double threePointHitRate = (double) threePointer / (threePointer + nullthree) * 100;
        o.put("threePointHitRate", Math.round(threePointHitRate));
        // 计算总命中率
        double totalHitRate = (double) totalHits / totalShots * 100;
        o.put("totalHitRate", Math.round(totalHitRate));
        return o;
    }

    /**
     * @desc: 个人计算场记综合排名
     * @author: DH
     * @date: 2023/8/24 15:55
     */
    public JSONArray calculateFieldSynthesisRanking(String key, JSONArray array) {
        JSONArray array1 = new JSONArray();
        for (int i = 0; i < array.size(); i++) {
            JSONObject object = new JSONObject();
            object.put("studentId", array.getJSONObject(i).getLongValue("studentId"));
            object.put("card", array.getJSONObject(i).getString("card"));
            object.put("name", array.getJSONObject(i).getString("name"));
            object.put("score", String.format("%.1f", array.getJSONObject(i).getJSONObject(key).getDoubleValue("sumScore")));
            array1.add(object);
        }
        return array1;
    }


    /**
     * @desc: 场记key
     * @author: DH
     * @date: 2023/8/17 16:26
     */
    public List<String> getFieldDataMapKeys(String key) {
        List<String> keys = new ArrayList<>();
        if (key.equals("offense") || key == null) {
            keys.add("surpass");//过人 +1
            keys.add("threatball");//威胁传球 +1
            keys.add("headgoal");//精准长传 +1
            keys.add("wonderfulstop");//精彩停球 +1.5
            keys.add("shoot");//射门 +1
            keys.add("threatenshoot");//威胁射门 +1.5
            keys.add("scoring");//攻入禁区 +1.5
            keys.add("goal");//进球 +3
            keys.add("assisting");//助攻 +1.5
            keys.add("longpass");//精彩长传
            //篮球
            keys.add("onePointer");//罚球（命中），+1
            keys.add("nullone");//罚球（不中），-0.5
            keys.add("twoPointer");//二分（命中），+2
            keys.add("nulltwo");//二分（不中），-0.5
            keys.add("threePointer");//三分（命中），+3
            keys.add("nullthree");//三分（不中），-0.5
            keys.add("frontback");//篮板（前场），+1.5
            keys.add("dunk");//扣篮，+2
            keys.add("assist");//助攻，+1
            keys.add("nicepass");//秒传，+1
            keys.add("treeBasket");//三步上篮，+1
            keys.add("amazing");//精彩过人，+1
            keys.add("helpball");//精彩救球+1
        }
        if (key.equals("guard") || key == null) {
            keys.add("preemption");//抢断 +1.5
            keys.add("excitingSyeals");//精彩抢断 +2
            keys.add("headclear");//头球解围 +1.5
            keys.add("wonderful");//精彩扑救 +1.5
            keys.add("clearance");//精彩解围 +1.5

            //篮球
            keys.add("afterback");//篮板（后场）+1.5
            keys.add("snatch");//抢断  +1
            keys.add("blockShot");//盖帽  +1
            keys.add("illegalDefense");//阻挡  +1 x
        }
        if (key.equals("fault") || key == null) {
            keys.add("passingerror");//传球失误 -0.5
            keys.add("stoperror");//停球失误 -0.5
            keys.add("defensiveerror");//防守失误 -0.5
            keys.add("waveshooting");//浪射 -1
            keys.add("puking");//冒顶 -1
            keys.add("kickair");//踢空 -0.5
            keys.add("offside");//越位 -0.5
            keys.add("seriouserror");//严重失误 -2
            keys.add("rules");//犯规 -1
            keys.add("yellowcard");//黄牌 -2
            keys.add("redcard");//红牌 -3
            //篮球
            keys.add("outBall");//使球出界 -1 x
            keys.add("illegalHands");//打手 -0.5 x
            keys.add("flyingElbow");//过分挥肘 -0.5 x
            keys.add("illegalAttack");//移动挡拆 -0.5 x
            keys.add("intentionalBall");//脚球违例 -0.5 x
            keys.add("comeback");//回场        -1 .
            keys.add("pullPeople");//拉人       -0.5.
            keys.add("dribbler");//带球撞人 -0.5.
            keys.add("walk");//走步 -0.5.
            keys.add("threeViolation");//3秒违例 -0.5.
            keys.add("pushPepole");//推人 -0.5.
            keys.add("headFoul");//击头犯规 -0.5.
            keys.add("technicalfoul");//技术犯规 -0.5.
            keys.add("penaltyExit");//罚满离场 -3.
            keys.add("violationfoul");//违体犯规 -0.5.

        }
        return keys;
    }

    /**
     * @desc: 场记key对应分数
     * @author: DH
     * @date: 2023/8/17 16:26
     */
    public double getFieldDataScoreByKey(String key) {
        double score = 0;
        switch (key) {
            case "surpass":
                score = 1;
                break;
            case "threatball":
                score = 1;
                break;
            case "headgoal":
                score = 1;
                break;
            case "wonderfulstop":
                score = 1.5;
                break;
            case "shoot":
                score = 1;
                break;
            case "threatenshoot":
                score = 1.5;
                break;
            case "scoring":
                score = 1.5;
                break;
            case "goal":
                score = 3;
                break;
            case "assisting":
                score = 1.5;
                break;
            case "preemption":
                score = 1.5;
                break;
            case "excitingSyeals":
                score = 2;
                break;
            case "headclear":
                score = 1.5;
                break;
            case "wonderful":
                score = 1.5;
                break;
            case "clearance":
                score = +1.5;
                break;
            case "longpass":
                score = +1;
                break;
            case "passingerror":
                score = -0.5;
                break;
            case "stoperror":
                score = -0.5;
                break;
            case "defensiveerror":
                score = -0.5;
                break;
            case "waveshooting":
                score = -1;
                break;
            case "puking":
                score = -1;
                break;
            case "kickair":
                score = -0.5;
                break;
            case "offside":
                score = -0.5;
                break;
            case "seriouserror":
                score = -2;
                break;
            case "rules":
                score = -1;
                break;
            case "yellowcard":
                score = -2;
                break;
            case "redcard":
                score = -3;
                break;
            case "onePointer":
                score = +1;
                break;
            case "nullone":
                score = -0.5;
                break;
            case "twoPointer":
                score = +2;
                break;
            case "nulltwo":
                score = -0.5;
                break;
            case "threePointer":
                score = +3;
                break;
            case "nullthree":
                score = -0.5;
                break;
            case "frontback":
                score = +1.5;
                break;
            case "dunk":
                score = +2;
                break;
            case "assist":
                score = +1;
                break;
            case "nicepass":
                score = +1;
                break;
            case "treeBasket":
                score = +1;
                break;
            case "amazing":
                score = +1;
                break;
            case "helpball":
                score = +1;
                break;
            case "afterback":
                score = +1.5;
                break;
            case "snatch":
                score = +1;
                break;
            case "blockShot":
                score = +1;
                break;
            case "comeback":
                score = -1;
                break;
            case "pullPeople":
                score = -0.5;
                break;
            case "dribbler":
                score = -0.5;
                break;
            case "walk":
                score = -0.5;
                break;
            case "threeViolation":
                score = -0.5;
                break;
            case "pushPepole":
                score = -0.5;
                break;
            case "headFoul":
                score = -0.5;
                break;
            case "technicalfoul":
                score = -0.5;
                break;
            case "penaltyExit":
                score = -3;
                break;
            case "violationfoul":
                score = -0.5;
                break;
            case "illegalDefense":
                score = +1;
                break;
            case "outBall":
                score = -1;
                break;
            case "illegalHands":
                score = -0.5;
                break;
            case "flyingElbow":
                score = -0.5;
                break;
            case "illegalAttack":
                score = -0.5;
                break;
            case "intentionalBall":
                score = -0.5;
                break;
            default:
                score = 1;
                break;
        }
        return score;
    }

    /**
     * @desc: 场记key排名
     * @author: DH
     * @date: 2023/8/17 17:05
     */
    public int rankingFieldDataByKey(List<FieldData> fieldDatas, String key, Long studentId) {
        List<FieldData> sortedFieldDatas = fieldDatas.stream()
                .sorted(Comparator.comparingInt(fd -> {
                    FieldData o = (FieldData) fd;
                    return getFieldDataOrDefault(o.getDataMap(), key);
                }).reversed())
                .collect(Collectors.toList());
        for (int i = 0; i < sortedFieldDatas.size(); i++) {
            if (studentId.equals(sortedFieldDatas.get(i).getStuId())) {
                return (i + 1);
            }
        }
        return 0;
    }


    /**
     * @desc: 跑动key得分
     * @author: DH
     * @date: 2023/8/17 17:05
     */
    public double calculateRunScore(int rk, int size, String key, double value) {
        if (key.equals("jerk")) {
            return 0;
        }

        if (key.equals("turning")) {
            if (rk <= 3) {
                return rk + 1.5;
            } else if (rk > size - 3) {
                return rk - 1;
            }
        }
        if (key.equals("runDistance")) {
            if (value > 10000) {
                return 7;
            } else if (value > 7000) {
                return 6;
            } else if (value > 3500) {
                return 5;
            } else if (value > 3000) {
                return 4;
            } else if (value > 2000) {
                return 3;
            } else if (value > 1500) {
                return 2;
            } else if (value > 0) {
                return 1;
            }
        }

        if (key.equals("calorie")) {
            if (value > 1500) {
                return 7;
            } else if (value > 1200) {
                return 6;
            } else if (value > 1000) {
                return 5;
            } else if (value > 800) {
                return 4;
            } else if (value > 600) {
                return 3;
            } else if (value > 400) {
                return 2;
            } else if (value > 0) {
                return 1;
            }
        }

        if (key.equals("maxSprintSpeed")) {
            if (value > 40) {
                return 7;
            } else if (value > 35) {
                return 6;
            } else if (value > 30) {
                return 5;
            } else if (value > 25) {
                return 4;
            } else if (value > 20) {
                return 3;
            } else if (value > 15) {
                return 2;
            } else if (value > 0) {
                return 1;
            }
        }

        if (key.equals("nsLowCount")) {
            return value * 1;
        } else if (key.equals("nsMidCount")) {
            return value * 2;
        } else if (key.equals("nsHighCount")) {
            return value * 3;
        } else if (key.equals("nsExceedCount")) {
            return value * 4;
        } else if (key.equals("nsSupCount")) {
            return value * 5;
        }

        return 0;
    }


    /**
     * @desc: 场记单项排序
     * @author: DH
     * @date: 2023/8/17 21:32
     */
    public JSONArray sortScoreRankingJSONArray(int count, JSONArray jsonArray, JSONObject teamRankingData, String matchType) {
        if (matchType.equals("GAME")) {
            return sortScoreRankingJSONArrayGame(jsonArray, teamRankingData);
        }
        jsonArray.sort((o1, o2) -> {
            double sumScore1 = ((JSONObject) o1).getJSONObject("runningFitness").getDoubleValue("sumScore");
            double sumScore2 = ((JSONObject) o2).getJSONObject("runningFitness").getDoubleValue("sumScore");
            return Double.compare(sumScore2, sumScore1);
        });
        for (int i = 0; i < jsonArray.size(); i++) {
            jsonArray.getJSONObject(i).getJSONObject("runningFitness").put("ranking", i + 1);
            double score = jsonArray.getJSONObject(i).getJSONObject("runningFitness").getDoubleValue("sumScore");
            if (score > 0) {
                jsonArray.getJSONObject(i).getJSONObject("runningFitness").put("pentacle", getPentacle(count, (i + 1)));
            } else {
                jsonArray.getJSONObject(i).getJSONObject("runningFitness").put("pentacle", 0);
            }
        }
        teamRankingData.put("runningFitness", calculateFieldSynthesisRanking("runningFitness", jsonArray));

        jsonArray.sort((o1, o2) -> {
            double sumScore1 = ((JSONObject) o1).getJSONObject("offense").getDoubleValue("sumScore");
            double sumScore2 = ((JSONObject) o2).getJSONObject("offense").getDoubleValue("sumScore");
            return Double.compare(sumScore2, sumScore1);
        });
        for (int i = 0; i < jsonArray.size(); i++) {
            double score = jsonArray.getJSONObject(i).getJSONObject("offense").getDoubleValue("sumScore");
            jsonArray.getJSONObject(i).getJSONObject("offense").put("ranking", i + 1);
            if (score > 0) {
                jsonArray.getJSONObject(i).getJSONObject("offense").put("pentacle", getPentacle(count, (i + 1)));
            } else {
                jsonArray.getJSONObject(i).getJSONObject("offense").put("pentacle", 0);
            }
        }
        teamRankingData.put("offense", calculateFieldSynthesisRanking("offense", jsonArray));

        jsonArray.sort((o1, o2) -> {
            double sumScore1 = ((JSONObject) o1).getJSONObject("guard").getDoubleValue("sumScore");
            double sumScore2 = ((JSONObject) o2).getJSONObject("guard").getDoubleValue("sumScore");
            return Double.compare(sumScore2, sumScore1);
        });
        for (int i = 0; i < jsonArray.size(); i++) {
            double score = jsonArray.getJSONObject(i).getJSONObject("guard").getDoubleValue("sumScore");
            jsonArray.getJSONObject(i).getJSONObject("guard").put("ranking", i + 1);
            if (score > 0) {
                jsonArray.getJSONObject(i).getJSONObject("guard").put("pentacle", getPentacle(count, (i + 1)));
            } else {
                jsonArray.getJSONObject(i).getJSONObject("guard").put("pentacle", 0);
            }
        }
        teamRankingData.put("guard", calculateFieldSynthesisRanking("guard", jsonArray));

        jsonArray.sort((o1, o2) -> {
            double sumScore1 = ((JSONObject) o1).getJSONObject("fault").getDoubleValue("sumScore");
            double sumScore2 = ((JSONObject) o2).getJSONObject("fault").getDoubleValue("sumScore");
            return Double.compare(sumScore2, sumScore1);
        });
        for (int i = 0; i < jsonArray.size(); i++) {
            double score = jsonArray.getJSONObject(i).getJSONObject("fault").getDoubleValue("sumScore");
            jsonArray.getJSONObject(i).getJSONObject("fault").put("ranking", i + 1);
            jsonArray.getJSONObject(i).getJSONObject("fault").put("pentacle", getPentacle(count, (i + 1)));

            if (score > 0) {
                jsonArray.getJSONObject(i).getJSONObject("fault").put("pentacle", getPentacle(count, (i + 1)));
            } else {
                jsonArray.getJSONObject(i).getJSONObject("fault").put("pentacle", 0);
            }
        }
        teamRankingData.put("fault", calculateFieldSynthesisRanking("fault", jsonArray));
        //总排名
        jsonArray.sort((o1, o2) -> {
            double sumScore1 = ((JSONObject) o1).getDoubleValue("score");
            double sumScore2 = ((JSONObject) o2).getDoubleValue("score");
            return Double.compare(sumScore2, sumScore1);
        });
        for (int i = 0; i < jsonArray.size(); i++) {
            jsonArray.getJSONObject(i).put("ranking", i + 1);
            jsonArray.getJSONObject(i).put("pentacle", getPentacle(count, (i + 1)));
        }

        return jsonArray;
    }

    public JSONArray sortScoreRankingJSONArrayGame(JSONArray jsonArray, JSONObject teamRankingData) {
        Integer grade = jsonArray.getJSONObject(0).getInteger("grade");
        for (int i = 0; i < jsonArray.size(); i++) {
            jsonArray.getJSONObject(i).getJSONObject("runningFitness").put("ranking", i + 1);
            jsonArray.getJSONObject(i).getJSONObject("runningFitness").put("pentacle", getPentacle(jsonArray.getJSONObject(i).getJSONObject("runningFitness").getDouble("sumScore"), grade));
        }
        teamRankingData.put("runningFitness", calculateFieldSynthesisRanking("runningFitness", jsonArray));

        for (int i = 0; i < jsonArray.size(); i++) {
            jsonArray.getJSONObject(i).getJSONObject("offense").put("ranking", i + 1);
            jsonArray.getJSONObject(i).getJSONObject("offense").put("pentacle", getPentacle(jsonArray.getJSONObject(i).getJSONObject("offense").getDouble("sumScore"), grade));
        }
        teamRankingData.put("offense", calculateFieldSynthesisRanking("offense", jsonArray));

        for (int i = 0; i < jsonArray.size(); i++) {
            jsonArray.getJSONObject(i).getJSONObject("guard").put("ranking", i + 1);
            jsonArray.getJSONObject(i).getJSONObject("guard").put("pentacle", getPentacle(jsonArray.getJSONObject(i).getJSONObject("guard").getDouble("sumScore"), grade));
        }
        teamRankingData.put("guard", calculateFieldSynthesisRanking("guard", jsonArray));

        for (int i = 0; i < jsonArray.size(); i++) {
            jsonArray.getJSONObject(i).getJSONObject("fault").put("ranking", i + 1);
            jsonArray.getJSONObject(i).getJSONObject("fault").put("pentacle", getPentacle(jsonArray.getJSONObject(i).getJSONObject("fault").getDouble("sumScore"), grade));
        }
        teamRankingData.put("fault", calculateFieldSynthesisRanking("fault", jsonArray));

        //总排名
        for (int i = 0; i < jsonArray.size(); i++) {
            jsonArray.getJSONObject(i).put("ranking", i + 1);
            Integer sumPentacle = jsonArray.getJSONObject(i).getJSONObject("runningFitness").getInteger("pentacle") +
                    jsonArray.getJSONObject(i).getJSONObject("offense").getInteger("pentacle") +
                    jsonArray.getJSONObject(i).getJSONObject("guard").getInteger("pentacle") +
                    jsonArray.getJSONObject(i).getJSONObject("fault").getInteger("pentacle");
            jsonArray.getJSONObject(i).put("pentacle", sumPentacle / 4);
        }

        return jsonArray;
    }

    public int getPentacle(int count, int ranking) {
        int pentacle = 0;
        if (ranking < 1) {
            return 0;
        }
        double x = (1 - ((double) (ranking - 1) / count)) * 10;
        pentacle = (int) x;
        return pentacle;
    }

    public int getPentacle(double score, int grade) {
        int pentacle = 0;
        double coefficient = getGradeByCoefficient(grade);
        double section = 30;
        for (int i = 0; i < 10; i++) {
            if (coefficient != 0.5) {
                section = section * coefficient;
            }
            if (score <= section) {
                pentacle = i + 1;
                break;
            }
        }
        return pentacle;
    }

    public Object getComprehensiveScoreRankingCacheArray(Long teamId, Long matchId, String matchType) {
        String key = CacheConsts.GETCOMPREHENSIVESCORERANKING + matchType + "-" + teamId + "-" + matchId;
        Object redisArray = redisUtils.get(key);
        return redisArray;
    }

    public void cacheComprehensiveScoreRankingArray(Long teamId, Long matchId, String matchType, JSONArray jsonArray, JSONObject teamRankingData) {
        delComprehensiveScoreRankingCacheArray(teamId, matchId, matchType);
        redisUtils.set(CacheConsts.GETCOMPREHENSIVESCORERANKING + matchType + "-" + teamId + "-" + matchId, jsonArray, CacheConsts.TOKEN_EXPIRED_TIME);
        redisUtils.set(CacheConsts.GETCOMPREHENSIVESCORERANKINGTEAM + matchType + "-" + teamId + "-" + matchId, teamRankingData, CacheConsts.TOKEN_EXPIRED_TIME);
    }

    public void delComprehensiveScoreRankingCacheArray(Long teamId, Long matchId, String matchType) {
        redisUtils.del(CacheConsts.GETCOMPREHENSIVESCORERANKING + matchType + "-" + teamId + "-" + matchId);
        redisUtils.del(CacheConsts.GETCOMPREHENSIVESCORERANKINGTEAM + matchType + "-" + teamId + "-" + matchId);


    }

    public int getFieldDataOrDefault(JSONObject o, String key) {
        if (o == null) {
            return 0;
        }
        int count = (int) o.getOrDefault(key, 0);
        return count;
    }

    public List<String> getRankingScoreKey() {
        List<String> keys = new ArrayList<>();
        keys.add("runDistance");//跑动
        keys.add("maxSprintSpeed");//最高冲刺速度
        keys.add("highDistance");//高速跑动距离
        keys.add("nsHighCount");//高速跑动距离
        keys.add("nsMidCount");//高速跑动距离
        keys.add("nsLowCount");//高速跑动距离
        keys.add("nsExceedCount");//高速跑动距离
        keys.add("nsSupCount");//高速跑动距离
        keys.add("calorie");//消耗
        keys.add("turning");//变向
        keys.add("jerk");//急停
        return keys;
    }

    /**
     * @desc: JSONArray排序
     * @author: DH
     * @date: 2021/3/13 14:09
     */
    public JSONArray sortJSONArray(JSONArray jsonArray, String sortKey) {
        jsonArray.sort((o1, o2) -> {
            Double v1 = JSONObject.parseObject(JSON.toJSONString(o1)).getDoubleValue(sortKey);
            Double v2 = JSONObject.parseObject(JSON.toJSONString(o2)).getDoubleValue(sortKey);
            return Double.compare(v2, v1);
        });
        return jsonArray;
    }

    public int getGradeByLevel(int grade) {
        if (grade >= 2 && 4 <= grade) {
            return 2;
        } else if (grade >= 5 && 7 <= grade) {
            return 3;
        } else if (grade >= 8 && 10 <= grade) {
            return 4;
        } else if (grade >= 11 && 14 <= grade) {
            return 5;
        } else if (grade == 15) {
            return 6;
        } else if (grade >= 16 && 20 <= grade) {
            return 7;
        }
        return 1;
    }

    public double getGradeByCoefficient(int grade) {
        if (grade == 1) {
            return 0.5;
        }
        return grade / 10 + 0.6;
    }


    //天梯区间最高名次数量
    public Map<Integer, Integer> getScoreHighLadderMap() {
        Map<Integer, Integer> map = new HashMap<>();
        map.put(1, getHighLadderRedis(1));
        map.put(2, getHighLadderRedis(2));
        map.put(3, getHighLadderRedis(3));
        map.put(4, getHighLadderRedis(4));
        map.put(5, getHighLadderRedis(5));
        map.put(6, getHighLadderRedis(6));
        map.put(7, getHighLadderRedis(7));
        return map;
    }

    public int getHighLadderRedis(Object key) {
        return (int) redisUtils.get("HighLadder:" + key);
    }
}
