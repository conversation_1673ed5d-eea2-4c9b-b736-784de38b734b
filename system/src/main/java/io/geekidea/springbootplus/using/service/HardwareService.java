package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.FacilityHardware;
import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.param.HardwarePageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.vo.BatchExchangeBindAo;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface HardwareService extends BaseService<Hardware> {

    /**
     * 保存
     *
     * @param hardware
     * @return
     * @throws Exception
     */
    boolean saveHardware(Hardware hardware) throws Exception;

    /**
     * 修改
     *
     * @param hardware
     * @return
     * @throws Exception
     */
    boolean updateHardware(List<Hardware> hardwares) throws Exception;

    /**
     * 修改电量
     *
     * @param hardware
     * @return
     * @throws Exception
     */
    boolean updateBattery(List<Hardware> hardwares) throws Exception;

    /**
     * 修改版本
     *
     * @param hardware
     * @return
     * @throws Exception
     */
    boolean updateVersion(List<Hardware> hardwares) throws Exception;

    /**
     * @desc: 启动设备
     * @author: DH
     * @date: 2022/4/29 10:09
     */
    boolean startHardwares(List<Hardware> hardwareList, Long drillId,Long teamId);

    boolean startHardwaresMonitorId(List<Hardware> hardwareList, Long monitorId,Long teamId);


    /**
     * @desc: 根据球队箱号 查询绑定数量
     * @author: DH
     * @date: 2022/8/4 15:17
     */
    int countByTeamAndBoxNum(Long teamId, String boxNum);

    /**
     * 剩余可绑定设备使用 拆分模式
     */
    int getResidue(String box);

    /**
     * @desc: 学生绑定设备组设备
     * @author: DH
     * @date: 2022/11/24 15:34
     */
    List<Hardware> addHardwares(List<Hardware> hardwares, Long schoolId, Long teamId);

    /**
     * @desc: 剩余可绑设备
     * @author: DH
     * @date: 2022/11/24 15:35
     */
    List<FacilityHardware> mayBindHards(Long groupId, Long teamId);

    /**
     * @desc: 学生换绑设备
     * @author: DH
     * @date: 2022/11/24 16:10
     */
    Object exchangeBind(Long groupId,Long studentId,Long exchangeStuId,Integer num,Boolean ct);

    /**
     * @desc: 学生换绑设备(批量)
     * @author: DH
     * @date: 2023/3/28 15:02
     */
    Object batchExchangeBind(List<BatchExchangeBindAo> batchExchangeBindAos,List<StudentInfo> studentInfos);

    /**
     * @desc: 前端打印用
     * @author: DH
     * @date: 2022/11/25 10:20
     */
    JSONObject getPrintStuHard(Long teamId);

    Hardware getByStuId(Long stuId,String mac);

    void test();

}
