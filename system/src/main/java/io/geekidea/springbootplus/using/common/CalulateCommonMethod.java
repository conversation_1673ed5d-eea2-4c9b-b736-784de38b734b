package io.geekidea.springbootplus.using.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.SerializationUtils;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.enums.CurveEnum;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.vo.CurveDto;
import io.geekidea.springbootplus.using.vo.DataStatisticsNewAo;
import io.geekidea.springbootplus.using.vo.PersonDataAo;
import io.geekidea.springbootplus.using.vo.TeamDataAo;
import lombok.Synchronized;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class CalulateCommonMethod {
    @Autowired
    StudentInfoService studentInfoService;
    @Autowired
    PersonDataService personDataService;
    @Autowired
    TeamDataService teamDataService;
    @Autowired
    HardwareService hardwareService;
    @Autowired
    TransparentMessageService transparentMessageService;
    @Autowired
    KindergartenMonitorService kindergartenMonitorService;

    /**
     * @desc: 团队封装步数
     * @author: DH
     * @date: 2023/11/6 11:29
     */
    public void getTeamPace(TeamDataAo teamDataAo, List<PersonData> personDatas) {
        JSONObject pace = new JSONObject();
        Double avgSpeed = personDatas.stream().mapToDouble(e -> {
            if (e.getPace() != null && e.getPace().getDouble("avgSpeed") != null) {
                return e.getPace().getIntValue("avgSpeed");
            } else {
                return 0;
            }
        }).average().orElse(0);
        BigDecimal b = new BigDecimal(avgSpeed);
        pace.put("avgSpeed", b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());

        pace.put("avgStride", (int) personDatas.stream().mapToInt(e -> {
            if (e.getPace() != null && e.getPace().getInteger("avgStride") != null) {
                return e.getPace().getIntValue("avgStride");
            } else {
                return 0;
            }
        }).average().orElse(0));
        pace.put("avgSpeedAllocation", (int) personDatas.stream().mapToLong(e -> {
            if (e.getPace() != null && e.getPace().getLong("avgSpeedAllocation") != null) {
                return e.getPace().getIntValue("avgSpeedAllocation");
            } else {
                return 0;
            }
        }).average().orElse(0));
        pace.put("avgStrideFrequency", (int) personDatas.stream().mapToInt(e -> {
            if (e.getPace() != null && e.getPace().getInteger("avgStrideFrequency") != null) {
                return e.getPace().getIntValue("avgStrideFrequency");
            } else {
                return 0;
            }
        }).average().orElse(0));
        teamDataAo.setPace(pace);
    }

    /**
     * @desc: 团队设置基本数据
     * @author: DH
     * @date: 2023/11/6 11:34
     */
    public void setTeamDataAoBasicData(TeamDataAo teamDataAo, List<PersonData> personDatas, List<TeamData> teamDatas) {
        teamDataAo.setSumStep(personDatas.stream().mapToInt(PersonData::getStepCount).sum());
        teamDataAo.setMaxTakeOffDistance((int) personDatas.stream().mapToInt(PersonData::getMaxTakeOffDistance).average().orElse(0.0));
        teamDataAo.setMaxTakeOffHeight((int) personDatas.stream().mapToInt(PersonData::getMaxTakeOffHeight).average().orElse(0.0));
        teamDataAo.setAvgDuration((int) personDatas.stream().mapToInt(PersonData::getAvgDuration).average().orElse(0.0));
        teamDataAo.setJumpAvgHeight((int) personDatas.stream().mapToInt(PersonData::getJumpAvgHeight).average().orElse(0.0));
        teamDataAo.setAvgDuration((int) personDatas.stream().mapToInt(PersonData::getAvgDuration).average().orElse(0.0));
        teamDataAo.setMaxDuration(personDatas.stream().mapToInt(PersonData::getAvgDuration).max().orElse(0));
        getTeamPace(teamDataAo, personDatas);
        setSpeedDataMapAndMvp(teamDataAo, personDatas);
        setRunningForm(teamDataAo, personDatas);
        getDepthData(teamDataAo, personDatas);
        calculateTeamAvg(personDatas, teamDataAo, null);
        setAvgConsume(teamDataAo, teamDatas);
    }

    /**
     * @desc: 个人基本数据
     * @author: DH
     * @date: 2023/11/6 13:56
     */
    public void setPersonBasicData(List<PersonData> personDataList, List<PersonData> historyPersonDataList, PersonDataAo personDataAo) {
        setPersonCalorie(personDataList, historyPersonDataList, personDataAo);
        setRankingAndData(personDataList, historyPersonDataList, personDataAo);
        setPersonMvp(personDataAo, personDataList);
        setOverall(personDataAo);
        calculateTeamAvg(personDataList, null, personDataAo);
        setBMI(personDataAo);
        setAnalysisOfMotion(personDataAo);
    }

    /**
     * @desc: 运动分析
     * @author: DH
     * @date: 2023/11/7 18:16
     */
    public void setAnalysisOfMotion(PersonDataAo personDataAo) {
        JSONObject analysisOfMotion = new JSONObject();
        long exerciseTime = personDataAo.getExerciseTime();
        long run = (long) (personDataAo.getRunExerciseTime() / exerciseTime * 100.0);
        long step = (long) (personDataAo.getStepExerciseTime() / exerciseTime * 100.0);
        long jump = (long) ((personDataAo.getJumpCount() * 20 / 60) / exerciseTime * 100.0);
        long quiet = 100 - jump - step - run;
        JSONObject habit = new JSONObject();//运动习惯
        habit.put("run", run);//跑动
        habit.put("step", step);//步数
        habit.put("jump", jump);//起跳
        habit.put("quiet", quiet);//休息
        analysisOfMotion.put("habit", habit);
        personDataAo.setAnalysisOfMotion(analysisOfMotion);
    }

    public void setGrowTaller(PersonData personData) {
        List<CurveDto> curveDtos = JSON.parseArray(personData.getCurveList().toJSONString(), CurveDto.class);
        List<Integer> axisY = curveDtos.get(0).getAxisY();
        int numSplits = axisY.size();
        List<Integer> growTallerY = splitType(numSplits, personData.getJumpCount());
        int stride = personData.getRunDistance() / 40;
        List<Integer> striedY = splitType(numSplits, stride);

        CurveDto curveDto1 = new CurveDto();
        curveDto1.setCurve(CurveEnum.GROWTALLER);
        curveDto1.setAxisX(curveDtos.get(0).getAxisX());
        curveDto1.setAxisY(growTallerY);
        curveDtos.add(curveDto1);

        CurveDto curveDto2 = new CurveDto();
        curveDto2.setCurve(CurveEnum.STRIDE);
        curveDto2.setAxisX(curveDtos.get(0).getAxisX());
        curveDto2.setAxisY(striedY);
        curveDtos.add(curveDto2);
        personData.setCurveList(JSON.parseArray(JSON.toJSONString(curveDtos)));
    }

    /**
     * @desc: 将起跳次数生成在曲线图上
     * @author: DH
     * @date: 2023/11/8 18:21
     */
    public List<Integer> splitType(int numSplits, int total) {
        if (total == 0) {
            return new ArrayList<>();
        }
        int[] splits = new int[numSplits];  // 存放每份的值
        // 初始化每份的值为0
        Arrays.fill(splits, 0);
        Random random = new Random();
        // 随机分配剩余的值到每份中
        for (int i = 0; i < numSplits - 1; i++) {
            splits[i] = random.nextInt(total) / 2;
            total -= splits[i];
        }
        if (numSplits != 0) {
            // 最后一份的值为剩余的总数
            splits[numSplits - 1] = total;
        }
        return Arrays.stream(splits).boxed().collect(Collectors.toList());
    }

    /**
     * @desc: 幼儿园团队数据统计
     * @author: DH
     * @date: 2023/11/16 16:01
     */
    public JSONObject statisticsNew(List<DataStatisticsNewAo> dataStatisticsNewAos, Integer type) {
        JSONObject object = new JSONObject();
        object.put("bmi", bmi(dataStatisticsNewAos));
        object.put("reachStandard", reachStandard(dataStatisticsNewAos, type));
        object.put("strideJump", strideJump(dataStatisticsNewAos));
        object.put("habit", habit(dataStatisticsNewAos));
        object.put("antiMyopia", antiMyopia(dataStatisticsNewAos));
        return object;
    }

    /**
     * @desc: 幼儿园个人数据统计
     * @author: DH
     * @date: 2023/11/16 16:01
     */
    public List<DataStatisticsNewAo> statisticsNewPerson(List<DataStatisticsNewAo> dataStatisticsNewAos, Integer type) {
        setHardInfo(dataStatisticsNewAos);
        bmi(dataStatisticsNewAos);
        reachStandard(dataStatisticsNewAos, type);
        strideJump(dataStatisticsNewAos);
        habit(dataStatisticsNewAos);
        antiMyopia(dataStatisticsNewAos);
        if (type == 1) {
            curve(dataStatisticsNewAos);//曲线avg
        }
        comprehensive(dataStatisticsNewAos);
        return dataStatisticsNewAos;
    }

    /**
     * @desc: 设置设备信息
     * @author: DH
     * @date: 2023/11/20 18:12
     */
    public void setHardInfo(List<DataStatisticsNewAo> dataStatisticsNewAos) {
        for (DataStatisticsNewAo dataStatisticsNewAo : dataStatisticsNewAos) {
            Hardware hardware = hardwareService.getByStuId(dataStatisticsNewAo.getStudentId(), null);
            if (hardware != null) {
                dataStatisticsNewAo.setHardNum(hardware.getNum());
                dataStatisticsNewAo.setHardBattery(hardware.getBattery());
            }
        }
    }


    /**
     * @desc: 运动达标
     * @author: DH
     * @date: 2023/11/15 16:00
     */
    public JSONObject reachStandard(List<DataStatisticsNewAo> dataStatisticsNewAos, Integer type) {
        JSONObject reachStandard = new JSONObject();
        JSONArray array = new JSONArray();
        for (DataStatisticsNewAo dataStatisticsNewAo : dataStatisticsNewAos) {
            dataStatisticsNewAo.setPracticalExerciseTime(dataStatisticsNewAo.getStepExerciseTime() + dataStatisticsNewAo.getRunExerciseTime());
        }

        Collections.sort(dataStatisticsNewAos, (o1, o2) -> (int) (o2.getPracticalExerciseTime() - o1.getPracticalExerciseTime()));
        int proportion = 0;
        boolean exerciseTimeStatus = false;
        for (DataStatisticsNewAo dataStatisticsNewAo : dataStatisticsNewAos) {
            JSONObject object = new JSONObject();
            object.put("studentId", dataStatisticsNewAo.getStudentId());
            object.put("name", dataStatisticsNewAo.getName());
            object.put("practicalExerciseTime", dataStatisticsNewAo.getPracticalExerciseTime());
            if (type >= 1 && type <= 3) {
                int threshold = type == 1 ? 60 : type == 2 ? 60 * 5 : 60 * 20;
                if (dataStatisticsNewAo.getPracticalExerciseTime() > threshold) {
                    proportion++;
                    exerciseTimeStatus = true;
                    dataStatisticsNewAo.setProportiotyn(100);
                } else {
                    dataStatisticsNewAo.setProportiotyn((int) calculatePercentage(dataStatisticsNewAo.getPracticalExerciseTime(), threshold));
                }
            }
            object.put("status", exerciseTimeStatus);
            dataStatisticsNewAo.setHealth((int) (dataStatisticsNewAo.getStepCount() * 0.01 + dataStatisticsNewAo.getCalorie() * 0.02 + dataStatisticsNewAo.getPracticalExerciseTime() * 0.04 * 2 * 0.05));
            dataStatisticsNewAo.setExerciseTimeStatus(exerciseTimeStatus);
            array.add(object);
        }
        reachStandard.put("array", array);
        if (dataStatisticsNewAos.size() != 0) {
            reachStandard.put("proportiotyn", calculatePercentage(proportion, dataStatisticsNewAos.size()));
        } else {
            reachStandard.put("proportiotyn", 0);
        }
        return reachStandard;
    }

    /**
     * @desc: BMI计算
     * @author: DH
     * @date: 2023/11/15 16:26
     */
    public JSONObject bmi(List<DataStatisticsNewAo> dataStatisticsNewAos) {
        JSONObject bmi = new JSONObject();
        JSONArray array = new JSONArray();
        int status1 = 0;
        int status2 = 0;
        int status3 = 0;
        int status4 = 0;
        Collections.sort(dataStatisticsNewAos, Comparator.comparingDouble(DataStatisticsNewAo::getBmi).reversed());
        JSONArray dataStatisticsNewAos1 = new JSONArray();
        JSONArray dataStatisticsNewAos2 = new JSONArray();
        JSONArray dataStatisticsNewAos3 = new JSONArray();
        JSONArray dataStatisticsNewAos4 = new JSONArray();
        for (DataStatisticsNewAo dataStatisticsNewAo : dataStatisticsNewAos) {
            JSONObject object = new JSONObject();
            object.put("studentId", dataStatisticsNewAo.getStudentId());
            object.put("name", dataStatisticsNewAo.getName());
            object.put("bmi", Double.parseDouble(String.format("%.1f", dataStatisticsNewAo.getBmi())));
            if (dataStatisticsNewAo.getBmi() < 18.5) {
                status2++;
                object.put("status", 1);
                dataStatisticsNewAos1.add(object);
            } else if (dataStatisticsNewAo.getBmi() >= 18.5 && dataStatisticsNewAo.getBmi() < 24) {
                status1++;
                object.put("status", 2);
                dataStatisticsNewAos2.add(object);
            } else if (dataStatisticsNewAo.getBmi() >= 24 && dataStatisticsNewAo.getBmi() < 28) {
                status3++;
                object.put("status", 3);
                dataStatisticsNewAos3.add(object);
            } else {
                status4++;
                object.put("status", 4);
                dataStatisticsNewAos4.add(object);
            }
            dataStatisticsNewAo.setBmi(object.getDouble("bmi"));
            dataStatisticsNewAo.setBmiStatus(object.getInteger("status"));
            dataStatisticsNewAo.setBmiComprehensive(getEvaluateGrade(object.getInteger("status")));
            dataStatisticsNewAo.setBmiComprehensiveScore(getMonitorScore(object.getInteger("status")));
        }

        array.addAll(dataStatisticsNewAos2);
        array.addAll(dataStatisticsNewAos1);
        array.addAll(dataStatisticsNewAos3);
        array.addAll(dataStatisticsNewAos4);

        bmi.put("array", array);
        if (dataStatisticsNewAos.size() != 0) {
            bmi.put("status1", calculatePercentage(status1, dataStatisticsNewAos.size()));
            bmi.put("status2", calculatePercentage(status2, dataStatisticsNewAos.size()));
            bmi.put("status3", calculatePercentage(status3, dataStatisticsNewAos.size()));
            bmi.put("status4", calculatePercentage(status4, dataStatisticsNewAos.size()));
            int sum = bmi.getIntValue("status1") + bmi.getIntValue("status2") + bmi.getIntValue("status3") + bmi.getIntValue("status4");
            if (sum < 100) {
                if (status1 > status2 && status1 > status3 && status1 > status4) {
                    bmi.put("status1", bmi.getIntValue("status1") + (100 - sum));
                } else if (status2 > status1 && status2 > status3 && status2 > status4) {
                    bmi.put("status2", bmi.getIntValue("status2") + (100 - sum));
                } else if (status3 > status1 && status3 > status3 && status3 > status4) {
                    bmi.put("status3", bmi.getIntValue("status3") + (100 - sum));
                } else {
                    bmi.put("status4", bmi.getIntValue("status4") + (100 - sum));
                }
            }
        } else {
            bmi.put("status1", 0);
            bmi.put("status2", 0);
            bmi.put("status3", 0);
            bmi.put("status4", 0);
        }

        return bmi;
    }

    /**
     * @desc: 跨步和起跳
     * @author: DH
     * @date: 2023/11/16 15:09
     */
    public JSONObject strideJump(List<DataStatisticsNewAo> dataStatisticsNewAos) {
        JSONObject strideJump = new JSONObject();
        for (DataStatisticsNewAo dataStatisticsNewAo : dataStatisticsNewAos) {
            dataStatisticsNewAo.setJumpStride(dataStatisticsNewAo.getJump() + dataStatisticsNewAo.getStride());
        }

        Collections.sort(dataStatisticsNewAos, Comparator.comparingDouble(DataStatisticsNewAo::getJumpStride).reversed());
        JSONArray array = new JSONArray();
        int status1 = 0;
        int status2 = 0;
        int status3 = 0;
        for (DataStatisticsNewAo dataStatisticsNewAo : dataStatisticsNewAos) {
            JSONObject object = new JSONObject();
            object.put("studentId", dataStatisticsNewAo.getStudentId());
            object.put("name", dataStatisticsNewAo.getName());
            object.put("stride", dataStatisticsNewAo.getStride());
            object.put("jump", dataStatisticsNewAo.getJump());

            if (dataStatisticsNewAo.getJumpStride() > 20) {
                status1++;
                object.put("status", 1);
            } else if (dataStatisticsNewAo.getJumpStride() > 10) {
                status2++;
                object.put("status", 2);
            } else {
                status3++;
                object.put("status", 3);
            }
            array.add(object);
            dataStatisticsNewAo.setStrideJumpStatus(object.getInteger("status"));
            dataStatisticsNewAo.setStrideJumpComprehensive(getEvaluateGrade(object.getInteger("status")));
            dataStatisticsNewAo.setStrideJumpComprehensiveScore(getMonitorScore(object.getInteger("status")));
        }
        strideJump.put("array", array);

        if (dataStatisticsNewAos.size() != 0) {
            strideJump.put("status1", calculatePercentage(status1, dataStatisticsNewAos.size()));
            strideJump.put("status2", calculatePercentage(status2, dataStatisticsNewAos.size()));
            strideJump.put("status3", calculatePercentage(status3, dataStatisticsNewAos.size()));

            int sum = strideJump.getIntValue("status1") + strideJump.getIntValue("status2") + strideJump.getIntValue("status3");
            if (sum < 100) {
                if (status1 > status2 && status1 > status3) {
                    strideJump.put("status1", strideJump.getIntValue("status1") + (100 - sum));
                } else if (status2 > status1 && status2 > status3) {
                    strideJump.put("status2", strideJump.getIntValue("status2") + (100 - sum));
                } else if (status3 > status1 && status3 > status2) {
                    strideJump.put("status3", strideJump.getIntValue("status3") + (100 - sum));
                }
            }
        } else {
            strideJump.put("status1", 0);
            strideJump.put("status2", 0);
            strideJump.put("status3", 0);
        }
        return strideJump;
    }

    /**
     * @desc: 运动习惯
     * @author: DH
     * @date: 2023/11/16 15:26
     */
    public JSONObject habit(List<DataStatisticsNewAo> dataStatisticsNewAos) {
        JSONObject habit = new JSONObject();
        JSONArray dataStatisticsNewAos1 = new JSONArray();
        int status1 = 0;
        int status2 = 0;
        int status3 = 0;
        for (DataStatisticsNewAo dataStatisticsNewAo : dataStatisticsNewAos) {
            JSONObject analysisOfMotion = new JSONObject();
            analysisOfMotion.put("studentId", dataStatisticsNewAo.getStudentId());
            analysisOfMotion.put("name", dataStatisticsNewAo.getName());
            long exerciseTime = dataStatisticsNewAo.getExerciseTime();
            long jump = calculatePercentage((dataStatisticsNewAo.getJump() * 0.08), exerciseTime);
            long run = calculatePercentage(dataStatisticsNewAo.getRunExerciseTime(), exerciseTime);
            long step = calculatePercentage(dataStatisticsNewAo.getStepExerciseTime(), exerciseTime);
            long quiet = 0l;
            if ((step + run + jump) < 100) {
                quiet = 100 - jump - step - run;
            }
            JSONObject object = new JSONObject();//运动习惯
            object.put("run", run);//跑动
            object.put("step", step);//步数
            object.put("jump", jump);//起跳
            object.put("quiet", quiet);//休息

            analysisOfMotion.put("habit", object);

            if (run > step && run > quiet) {
                status1++;
                analysisOfMotion.put("status", 1);
            } else if ((step > quiet) || jump > quiet) {
                status2++;
                analysisOfMotion.put("status", 2);
            } else {
                status3++;
                analysisOfMotion.put("status", 3);
            }
            analysisOfMotion.put("sort", -analysisOfMotion.getIntValue("status"));

            dataStatisticsNewAo.setHabit(object);
            dataStatisticsNewAo.setHabitStatus(analysisOfMotion.getInteger("status"));
            dataStatisticsNewAo.setHabitComprehensive(getEvaluateGrade(analysisOfMotion.getInteger("status")));
            dataStatisticsNewAo.setHabitComprehensiveScore(getMonitorScore(analysisOfMotion.getInteger("status")));
            dataStatisticsNewAos1.add(analysisOfMotion);
        }
        sortJSONArray(dataStatisticsNewAos1, "sort");

        habit.put("array", dataStatisticsNewAos1);

        if (dataStatisticsNewAos.size() != 0) {
            habit.put("status1", calculatePercentage(status1, dataStatisticsNewAos.size()));
            habit.put("status2", calculatePercentage(status2, dataStatisticsNewAos.size()));
            habit.put("status3", calculatePercentage(status3, dataStatisticsNewAos.size()));
            int sum = habit.getIntValue("status1") + habit.getIntValue("status2") + habit.getIntValue("status3");
            if (sum < 100) {
                if (status1 > status2 && status1 > status3) {
                    habit.put("status1", habit.getIntValue("status1") + (100 - sum));
                } else if (status2 > status1 && status2 > status3) {
                    habit.put("status2", habit.getIntValue("status2") + (100 - sum));
                } else if (status3 > status1 && status3 > status2) {
                    habit.put("status3", habit.getIntValue("status3") + (100 - sum));
                }
            }
        } else {
            habit.put("status1", 0);
            habit.put("status2", 0);
            habit.put("status3", 0);
        }
        return habit;
    }

    /**
     * @desc: 防近视
     * @author: DH
     * @date: 2023/11/16 17:37
     */
    public JSONObject antiMyopia(List<DataStatisticsNewAo> dataStatisticsNewAos) {
        JSONObject antiMyopia = new JSONObject();
        JSONArray dataStatisticsNewAos1 = new JSONArray();
        int status1 = 0;
        int status2 = 0;
        int status3 = 0;
        for (DataStatisticsNewAo dataStatisticsNewAo : dataStatisticsNewAos) {
            JSONObject analysisOfMotion = new JSONObject();
            analysisOfMotion.put("studentId", dataStatisticsNewAo.getStudentId());
            analysisOfMotion.put("name", dataStatisticsNewAo.getName());
            long exerciseTime = dataStatisticsNewAo.getExerciseTime();
            long run = calculatePercentage(dataStatisticsNewAo.getRunExerciseTime(), exerciseTime);
            long step = calculatePercentage(dataStatisticsNewAo.getStepExerciseTime(), exerciseTime);
            long quiet = 0;
            if ((step + run) < 100) {
                quiet = 100 - step - run;
            }
            JSONObject myopia = new JSONObject();//运动习惯
            myopia.put("run", run + step);//运动
            myopia.put("quiet", quiet - (quiet / 3));//休息
            myopia.put("meditation", (quiet / 3));//静坐
            analysisOfMotion.put("myopia", myopia);

            if (run > step && run > quiet) {
                status1++;
                analysisOfMotion.put("status", 1);
            } else if ((run < step && step > quiet)) {
                status2++;
                analysisOfMotion.put("status", 2);
            } else {
                status3++;
                analysisOfMotion.put("status", 3);
            }
            analysisOfMotion.put("sort", -analysisOfMotion.getIntValue("status"));

            dataStatisticsNewAo.setAntiMyopiaStatus(analysisOfMotion.getInteger("status"));
            dataStatisticsNewAo.setAntiMyopia(myopia);
            dataStatisticsNewAo.setAntiMyopiaComprehensive(getEvaluateGrade(analysisOfMotion.getInteger("status")));
            dataStatisticsNewAo.setAntiMyopiaComprehensiveScore(getMonitorScore(analysisOfMotion.getInteger("status")));
            dataStatisticsNewAos1.add(analysisOfMotion);
        }
        sortJSONArray(dataStatisticsNewAos1, "sort");
        antiMyopia.put("array", dataStatisticsNewAos1);

        if (dataStatisticsNewAos.size() != 0) {
            antiMyopia.put("status1", calculatePercentage(status1, dataStatisticsNewAos.size()));
            antiMyopia.put("status2", calculatePercentage(status2, dataStatisticsNewAos.size()));
            antiMyopia.put("status3", calculatePercentage(status3, dataStatisticsNewAos.size()));

            int sum = antiMyopia.getIntValue("status1") + antiMyopia.getIntValue("status2") + antiMyopia.getIntValue("status3");
            if (sum < 100) {
                if (status1 > status2 && status1 > status3) {
                    antiMyopia.put("status1", antiMyopia.getIntValue("status1") + (100 - sum));
                } else if (status2 > status1 && status2 > status3) {
                    antiMyopia.put("status2", antiMyopia.getIntValue("status2") + (100 - sum));
                } else if (status3 > status1 && status3 > status2) {
                    antiMyopia.put("status3", antiMyopia.getIntValue("status3") + (100 - sum));
                }
            }
        } else {
            antiMyopia.put("status1", 0);
            antiMyopia.put("status2", 0);
            antiMyopia.put("status3", 0);
        }
        return antiMyopia;
    }

    /**
     * @desc: 设置bmi
     * @author: DH
     * @date: 2023/11/7 18:16
     */
    public void setBMI(PersonDataAo personDataAo) {
        StudentInfo studentInfo = personDataAo.getStudentInfo();
        double bmi = studentInfo.getWeight() / 2 / (studentInfo.getHeight() / 100 / studentInfo.getHeight() / 100);
        personDataAo.setBmi(bmi);
    }

    /**
     * @desc: 个人汇总幼儿园运动数据曲线
     * @author: DH
     * @date: 2023/11/16 17:58
     */
    public void curve(List<DataStatisticsNewAo> personDataList) {
        for (DataStatisticsNewAo dataStatisticsNewAo : personDataList) {
            List<CurveDto> curveDtoList = new ArrayList<>();
            for (PersonData t : dataStatisticsNewAo.getPersonDataList()) {
                KindergartenMonitor kindergartenMonitor = kindergartenMonitorService.getById(t.getMatchId());
                CurveDto run = new CurveDto();
                run.setCurve(CurveEnum.PERSON_RUN);
                CurveDto growtaller = new CurveDto();
                growtaller.setCurve(CurveEnum.GROWTALLER);
                CurveDto stride = new CurveDto();
                stride.setCurve(CurveEnum.STRIDE);
                CurveDto step = new CurveDto();
                step.setCurve(CurveEnum.STEP);

                CurveDto curveDto1 = JSON.parseObject(JSON.toJSONString(t.getCurveList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("curve").equals("PERSON_RUN")).findFirst().orElseGet(() -> new CurveDto())), CurveDto.class);
                run.setAxisX(curveDto1.getAxisX());
                run.setAxisY(curveDto1.getAxisY());
                run.setStartTime(kindergartenMonitor.getStartTime().getTime());

                CurveDto curveDto2 = JSON.parseObject(JSON.toJSONString(t.getCurveList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("curve").equals("GROWTALLER")).findFirst().orElseGet(() -> new CurveDto())), CurveDto.class);
                growtaller.setAxisX(curveDto2.getAxisX());
                growtaller.setAxisY(curveDto2.getAxisY());
                growtaller.setStartTime(kindergartenMonitor.getStartTime().getTime());

                CurveDto curveDto3 = JSON.parseObject(JSON.toJSONString(t.getCurveList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("curve").equals("STRIDE")).findFirst().orElseGet(() -> new CurveDto())), CurveDto.class);
                stride.setAxisX(curveDto3.getAxisX());
                stride.setAxisY(curveDto3.getAxisY());
                stride.setStartTime(kindergartenMonitor.getStartTime().getTime());

                CurveDto curveDto4 = JSON.parseObject(JSON.toJSONString(t.getCurveList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("curve").equals("STEP")).findFirst().orElseGet(() -> new CurveDto())), CurveDto.class);
                step.setAxisX(curveDto4.getAxisX());
                step.setAxisY(curveDto4.getAxisY());
                step.setStartTime(kindergartenMonitor.getStartTime().getTime());

                curveDtoList.add(run);
                curveDtoList.add(step);
                curveDtoList.add(growtaller);
                curveDtoList.add(stride);
            }

            dataStatisticsNewAo.setCurveDtos(curveDtoList);
        }
    }

    /**
     * @desc: 综合评分
     * @author: DH
     * @date: 2024/1/8 15:24
     */
    public void comprehensive(List<DataStatisticsNewAo> personDataList) {
        for (DataStatisticsNewAo dataStatisticsNewAo : personDataList) {
            double habit = dataStatisticsNewAo.getHabitComprehensiveScore() * 0.6;
            double bmi = dataStatisticsNewAo.getBmiComprehensiveScore() * 0.2;
            double strideJump = dataStatisticsNewAo.getStrideJumpComprehensiveScore() * 0.1;
            double antiMyopia = dataStatisticsNewAo.getAntiMyopiaComprehensiveScore() * 0.1;
            dataStatisticsNewAo.setComprehensiveScore((int) (habit + bmi + strideJump + antiMyopia));
            dataStatisticsNewAo.setComprehensive(getEvaluateGrade(dataStatisticsNewAo.getComprehensiveScore()));
        }
    }

    /**
     * @desc: 获取监测对应分数
     * @author: DH
     * @date: 2024/1/8 15:00
     */
    public int getMonitorScore(int status) {
        int score = 0;
        switch (status) {
            case 1:
                score = 100;
                break;
            case 2:
                score = 80;
                break;
            case 3:
                score = 60;
                break;
            default:
                score = 40;
                break;
        }
        return score;
    }

    /**
     * @desc: 获取监测分数对应等级
     * @author: DH
     * @date: 2024/1/8 15:00
     */
    public String getEvaluateGrade(int status) {
        double score = getMonitorScore(status);
        if (score >= 90 && score <= 100) {
            return "A+";
        } else if (score >= 80 && score <= 89) {
            return "A";
        } else if (score >= 70 && score <= 79) {
            return "B+";
        } else if (score >= 60 && score <= 69) {
            return "B";
        } else if (score >= 50 && score <= 59) {
            return "C+";
        } else if (score >= 40 && score <= 49) {
            return "C";
        } else if (score >= 30 && score <= 39) {
            return "D+";
        } else if (score >= 20 && score <= 29) {
            return "D";
        }
        return "";
    }

    /**
     * @desc: 个人卡路里
     * @author: DH
     * @date: 2023/11/6 12:19
     */
    public void setPersonCalorie(List<PersonData> personDataList, List<PersonData> historPersonData, PersonDataAo personDataAo) {
        //团队平均卡路里
        int calorieSum = personDataList.stream().mapToInt(e -> e.getCalorie()).sum();
        if (calorieSum != 0) {
            personDataAo.setAvgCalorie(calorieSum / personDataList.size());
        } else {
            personDataAo.setAvgCalorie(0);
        }

        //个人平均卡路里
        int historyCalorie = historPersonData.stream().mapToInt(e -> e.getCalorie()).sum();
        if (historyCalorie != 0) {
            personDataAo.setHistoryCalorie(calorieSum / personDataList.size());
        } else {
            personDataAo.setHistoryCalorie(0);
        }
    }

    /**
     * @desc: 数据计算和排名
     * @author: DH
     * @date: 2021/3/19 16:21
     */
    public void setRankingAndData(List<PersonData> personDataList, List<PersonData> historPersonData, PersonDataAo personDataAo) {
        // List<PersonData> historPersonData = personDataService.findOfMatchIdByHist(personDataAo.getStudentId(), personDataAo.getMatchId(), matchType);
        JSONObject ranking = new JSONObject();//排名前
        JSONObject rankingAfter = new JSONObject();//排名后
        JSONObject rankingUpOrDown = new JSONObject();//比较值
        List<String> rankingKey = getRankingKey();
        rankingKey.stream().forEach(key -> ranking.put(key, new JSONArray()));
        personDataList.stream().forEach(e -> {
            //步数
            JSONObject o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getStepCount());
            ranking.getJSONArray("stepCount").add(o);
            //跑动
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getRunDistance());
            ranking.getJSONArray("runDistance").add(o);
            // 最高冲刺速度
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getMaxSprintSpeed());
            ranking.getJSONArray("maxSprintSpeed").add(o);
            // 高速跑动距离
            o = new JSONObject();
            List<Object> speedDatas = e.getSpeedDataList().toJavaObject(JSONArray.class).stream().collect(Collectors.toList());
            o.put("k", e.getStudentId());
            o.put("v", speedDatas.stream().filter(a -> ((JSONObject) a).getString("speed").equals("HIGH")).mapToInt(a -> ((JSONObject) a).getInteger("distance")).sum());
            ranking.getJSONArray("highDistance").add(o);
            // 起跳
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", ((JSONObject) e.getPose()).getJSONObject("jump").getIntValue("count"));
            ranking.getJSONArray("jump").add(o);
        });

        rankingUpOrDown.put("stepCount", historPersonData.stream().mapToInt(a -> a.getStepCount()).average().orElse(0));
        rankingUpOrDown.put("runDistance", historPersonData.stream().mapToInt(a -> a.getRunDistance()).average().orElse(0));
        rankingUpOrDown.put("maxSprintSpeed", historPersonData.stream().mapToDouble(a -> a.getMaxSprintSpeed()).average().orElse(0));
        rankingUpOrDown.put("jump", historPersonData.stream().mapToDouble(a -> ((JSONObject) a.getPose()).getJSONObject("jump").getIntValue("count")).average().orElse(0));
        rankingUpOrDown.put("highDistance", historPersonData.stream().mapToDouble(a -> a.getSpeedDataList().toJavaObject(JSONArray.class).stream().collect(Collectors.toList()).stream().filter(c -> ((JSONObject) c).getString("speed").equals("HIGH")).mapToInt(c -> ((JSONObject) c).getInteger("distance")).sum()).average().orElse(0));

        rankingKey.stream().forEach(e -> {
            JSONArray jsonArray = sortJSONArray(ranking.getJSONArray(e), "v");
            for (int i = 0; i < jsonArray.size(); i++) {
                if (jsonArray.getJSONObject(i).getLong("k").equals(personDataAo.getStudentId())) {
                    JSONObject o = new JSONObject();
                    double upOrDownValue = rankingUpOrDown.getDoubleValue(e);
                    double value = jsonArray.getJSONObject(i).getDoubleValue("v");
                    if (upOrDownValue == value) {
                        o.put("state", 1);
                    } else if (value > upOrDownValue) {
                        o.put("state", 2);
                    } else {
                        o.put("state", 0);
                    }
                    o.put("ranking", i + 1);
                    rankingAfter.put(e, o);
                    break;
                }
            }
        });
        personDataAo.setRanking(rankingAfter);
    }

    /**
     * @desc: 跑动和传球排名
     * @author: DH
     * @date: 2021/6/11 15:25
     */
    public void runAndPassRanking(List<PersonData> personData, JSONArray runRanking) {
        personData.stream().forEach(e -> {
            JSONObject runMap = new JSONObject();
            runMap.put("studentId", e.getStudentId());
            runMap.put("runDistance", e.getRunDistance());
            runRanking.add(runMap);
        });
        //进行排名
        sortJSONArray(runRanking, "runDistance");
    }

    /**
     * @desc: 团队历史消耗数据
     * @author: DH
     * @date: 2023/11/6 11:56
     */
    public void setAvgConsume(TeamDataAo teamDataAo, List<TeamData> teamDatas) {
        //历史平均消耗
        List<Integer> consume = new ArrayList<>();
        teamDatas.stream().forEach(e -> {
            JSONArray jsonArray = (JSONArray) e.getCurves();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if (jsonObject.getString("curve").equals("TEAM_CONSUME")) {
                    if (!e.getMatchId().equals(teamDataAo.getMatchId())) {
                        consume.add(jsonObject.getIntValue("sumY"));
                        break;
                    }
                }
            }
        });
        teamDataAo.setAvgConsume(consume.stream().mapToInt(e -> e).sum() / (consume.size() == 0 ? 1 : consume.size()));
    }

    /**
     * @desc: 团队跑动数据和mvp
     * @author: DH
     * @date: 2023/11/6 11:49
     */
    public void setSpeedDataMapAndMvp(TeamDataAo teamDataAo, List<PersonData> personDatas) {
        JSONArray runRanking = new JSONArray(); //跑动排名
        runAndPassRanking(personDatas, runRanking);
        //总跑动 总跑动最多 因为是排了序的 所以下标0就是跑动最多
        StudentInfo runBestStu = studentInfoService.getById(runRanking.getJSONObject(0).getLongValue("studentId"));
        if (runBestStu != null) {
            teamDataAo.setRunBestStu(runBestStu.getName());
        }
        teamDataAo.setSumRun(runRanking.stream().mapToLong(e -> JSON.parseObject(e.toString()).getLong("runDistance")).sum());
        teamDataAo.setSumCalorie(personDatas.stream().mapToInt(e -> e.getCalorie()).sum());
        teamDataAo.setBestStuRun(runRanking.getJSONObject(0).getLongValue("runDistance"));

        List<JSONObject> list = new ArrayList<>(); //mvp 未排序前
        for (int i = 0; i < runRanking.size(); i++) {
            JSONObject r = runRanking.getJSONObject(i);
            JSONObject v = new JSONObject();
            v.put("studentId", r.getLong("studentId"));
            v.put("price", mvpPrice(r.getLong("runDistance"), 0));
            v.put("runDistance", r.getLong("runDistance"));
            if (v.get("runDistance") == null) {
                v.put("runDistance", 0);
            }
            list.add(v);
        }
        //速度数据
        Map<String, Object> speedDataMap = new HashMap<>();
        List<Object> speedDatas = personDatas.stream().flatMap(e -> e.getSpeedDataList().toJavaObject(JSONArray.class).stream()).collect(Collectors.toList());
        //高速
        speedDataMap.put("highDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());//全队高速跑动距离 m
        speedDataMap.put("highCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());//全队高速跑动次数
        speedDataMap.put("highBallCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballCount")).sum());//全队高速带球次数
        speedDataMap.put("highBallDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队高速带球跑动
        //中速
        speedDataMap.put("minDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());//全队中速跑动距离 m
        speedDataMap.put("minCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());//全队中速跑动次数
        speedDataMap.put("minBallCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("ballCount")).sum());//全中速带球次数
        speedDataMap.put("minBallDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队中速带球跑动m
        //低速
        speedDataMap.put("lowDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());//全队低速跑动距离 m
        speedDataMap.put("lowCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());//全队低速跑动次数
        speedDataMap.put("lowBallCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("ballCount")).sum());//全低速带球次数
        speedDataMap.put("lowBallDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队低速带球跑动m
        speedDataMap.put("teamBallDistance", speedDatas.stream().mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队带球距离

        PersonData maxSprintSpeedPersonData = personDatas.stream().sorted((e1, e2) -> Double.compare(e2.getMaxSprintSpeed().doubleValue(), e1.getMaxSprintSpeed().doubleValue())).findFirst().get(); //最高冲刺速度第一
        PersonData maxHighDistancePersonData = personDatas.stream().sorted((e1, e2) -> {
            Integer o1 = e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
            Integer o2 = e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
            return Double.compare(o2, o1);
        }).findFirst().get();
        PersonData maxHighBallDistancePersonData = personDatas.stream().sorted((e1, e2) -> {
            Integer o1 = e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt();
            Integer o2 = e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt();
            return Double.compare(o2, o1);
        }).findFirst().get();

        StudentInfo maxSprintSpeedStu = studentInfoService.getById(maxSprintSpeedPersonData.getStudentId());
        StudentInfo maxHighDistanceStu = studentInfoService.getById(maxHighDistancePersonData.getStudentId());
        StudentInfo maxHighBallDistanceStu = studentInfoService.getById(maxHighBallDistancePersonData.getStudentId());
        speedDataMap.put("maxSprintSpeedStudentName", maxSprintSpeedStu.getName());
        speedDataMap.put("maxHighDistanceStudentName", maxHighDistanceStu.getName());
        speedDataMap.put("maxHighBallDistanceStudentName", maxHighBallDistanceStu.getName());

        speedDataMap.put("maxSprintSpeedStudentHeadImg", maxSprintSpeedStu.getHeadImg());
        speedDataMap.put("maxHighDistanceStudentHeadImg", maxHighDistanceStu.getHeadImg());
        speedDataMap.put("maxHighBallDistanceStudentHeadImg", maxHighBallDistanceStu.getHeadImg());

        speedDataMap.put("maxSprintSpeed", maxSprintSpeedPersonData.getMaxSprintSpeed());
        speedDataMap.put("maxHighDistance", maxHighDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt());
        speedDataMap.put("maxHighBallDistance", maxHighBallDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt());
        teamDataAo.setSpeedDataMap(speedDataMap);

        JSONObject mvp = list.stream().max(Comparator.comparingDouble((e) -> e.getDouble("runDistance"))).get();
        StudentInfo studentInfoMvp = studentInfoService.getById(mvp.getLongValue("studentId"));
        if (studentInfoMvp != null) {
            mvp.put("studentName", studentInfoMvp.getName());
            //MVP
            teamDataAo.setMvp(mvp);
        }
    }

    /**
     * @desc: 步伐那些
     * @author: DH
     * @date: 2023/11/6 16:07
     */
    public void setRunningForm(TeamDataAo teamDataAo, List<PersonData> personDatas) {
        JSONObject runningForm = new JSONObject();
        Long avgTouchLandTime = 0l;
        Double avgTouchdownImpact = 0.0;
        Integer avgEctropionRange = null;
        Integer avgPendulumAngle = null;

        avgTouchLandTime = (long) personDatas.stream().mapToLong(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0l;
            } else {
                return e.getRunningForm().getLongValue("avgTouchLandTime");
            }
        }).average().orElse(0.0);

        avgTouchdownImpact = personDatas.stream().mapToDouble(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0.0;
            } else {
                return e.getRunningForm().getDouble("avgTouchdownImpact");
            }
        }).average().orElse(0.0);

        avgEctropionRange = (int) personDatas.stream().mapToInt(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0;
            } else {
                return e.getRunningForm().getInteger("avgEctropionRange");
            }
        }).average().orElse(0.0);

        avgPendulumAngle = (int) personDatas.stream().mapToInt(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0;
            } else {
                return e.getRunningForm().getInteger("avgPendulumAngle");
            }
        }).average().orElse(0.0);

        runningForm.put("avgTouchLandTime", avgTouchLandTime);
        BigDecimal bd = new BigDecimal(avgTouchdownImpact);
        runningForm.put("avgTouchdownImpact", bd.setScale(2, BigDecimal.ROUND_DOWN).doubleValue());
        runningForm.put("avgEctropionRange", avgEctropionRange);
        runningForm.put("avgPendulumAngle", avgPendulumAngle);
        teamDataAo.setRunningForm(runningForm);
    }

    /**
     * @desc: 专业数据
     * @author: DH
     * @date: 2023/11/6 16:06
     */
    public void getDepthData(TeamDataAo teamDataAo, List<PersonData> personDatas) {
        JSONArray runDistances = new JSONArray();
        JSONArray maxSprintSpeeds = new JSONArray();
        JSONArray leftOrRightTurnings = new JSONArray();
        JSONArray startingsOrBrakes = new JSONArray();
        JSONArray breakOuts = new JSONArray();
        JSONArray overallSpeeds = new JSONArray();
        JSONArray overallSpeedCounts = new JSONArray();

        Map<Long, StudentInfo> stuMap = studentInfoService.getStusByTeamId(teamDataAo.getTeamId()).stream().collect(Collectors.toMap(StudentInfo::getId, Function.identity(), (key1, key2) -> key2));
        personDatas.forEach(e -> {
            StudentInfo studentInfo = stuMap.get(e.getStudentId());
            if (studentInfo == null) {
                studentInfo = studentInfoService.getById(e.getStudentId());
            }
            if (studentInfo != null) {
                //       [{"count": 0, "speed": "HIGH", "distance": 0, "ballCount": 0, "ballDistance": 0}]
                JSONObject overallSpeed = new JSONObject();
                overallSpeed.put("studentId", e.getStudentId());
                overallSpeed.put("card", studentInfo.getCard());
                overallSpeed.put("name", studentInfo.getName());
                JSONObject overallSpeedCount = new JSONObject();
                overallSpeedCount.put("studentId", e.getStudentId());
                overallSpeedCount.put("card", studentInfo.getCard());
                overallSpeedCount.put("name", studentInfo.getName());
                JSONArray speedDataList = JSON.parseArray(e.getSpeedDataList().toJSONString());
                for (int i = 0; i < speedDataList.size(); i++) {
                    JSONObject speed = speedDataList.getJSONObject(i);
                    overallSpeed.put(speed.getString("speed"), speed.getInteger("distance"));
                    if (speed.getString("speed").equals("EXCEED")) {
                        overallSpeedCount.put("exceedCount", speed.getInteger("count"));
                    } else if ((speed.getString("speed").equals("SUP"))) {
                        overallSpeedCount.put("supCount", speed.getInteger("count"));
                    }
                }
                if (overallSpeed.get("EXCEED") == null || overallSpeed.get("SUP") == null) {//之前的数据防止报错
                    overallSpeed.put("EXCEED", 0);
                    overallSpeedCount.put("exceedCount", 0);
                    overallSpeed.put("SUP", 0);
                    overallSpeedCount.put("supCount", 0);
                } else { //进入到这里代表有值 overallSpeedCount添加sum值方面排序
                    overallSpeedCount.put("sum", overallSpeedCount.getInteger("exceedCount") + overallSpeedCount.getInteger("supCount"));
                }

                overallSpeeds.add(overallSpeed);
                overallSpeedCounts.add(overallSpeedCount);

                JSONObject runDistance = new JSONObject();
                runDistance.put("studentId", e.getStudentId());
                runDistance.put("card", studentInfo.getCard());
                runDistance.put("name", studentInfo.getName());
                runDistance.put("run", e.getRunDistance());
                double avg = (double) e.getRunDistance() / e.getExerciseTime();
                double result = new BigDecimal(avg).setScale(1, BigDecimal.ROUND_UP).doubleValue();
                runDistance.put("avg", result);
                runDistances.add(runDistance);

                JSONObject maxSprintSpeed = new JSONObject();
                maxSprintSpeed.put("studentId", e.getStudentId());
                maxSprintSpeed.put("card", studentInfo.getCard());
                maxSprintSpeed.put("name", studentInfo.getName());
                maxSprintSpeed.put("value", e.getMaxSprintSpeed());
                maxSprintSpeeds.add(maxSprintSpeed);

                JSONObject o = e.getDepthData();
                if (o == null) {
                    o = new JSONObject();
                }
                JSONObject leftOrRightTurning = new JSONObject();
                leftOrRightTurning.put("studentId", e.getStudentId());
                leftOrRightTurning.put("card", studentInfo.getCard());
                leftOrRightTurning.put("name", studentInfo.getName());
                leftOrRightTurning.put("left", o.getInteger("leftTurning") == null ? 0 : o.getInteger("leftTurning"));
                leftOrRightTurning.put("right", o.getInteger("rightTurning") == null ? 0 : o.getInteger("rightTurning"));
                leftOrRightTurning.put("sum", leftOrRightTurning.getInteger("left") + leftOrRightTurning.getInteger("right"));
                leftOrRightTurnings.add(leftOrRightTurning);

                JSONObject startingsOrBrake = new JSONObject();
                startingsOrBrake.put("studentId", e.getStudentId());
                startingsOrBrake.put("card", studentInfo.getCard());
                startingsOrBrake.put("name", studentInfo.getName());
                startingsOrBrake.put("starting", o.getInteger("starting") == null ? 0 : o.getInteger("starting"));
                startingsOrBrake.put("brake", o.getInteger("brake") == null ? 0 : o.getInteger("brake"));
                startingsOrBrake.put("sum", startingsOrBrake.getInteger("brake") + startingsOrBrake.getInteger("starting"));
                startingsOrBrakes.add(startingsOrBrake);

                JSONObject breakOut = new JSONObject();
                breakOut.put("studentId", e.getStudentId());
                breakOut.put("card", studentInfo.getCard());
                breakOut.put("name", studentInfo.getName());
                breakOut.put("breakOut", o.getInteger("breakOut") == null ? 0 : o.getInteger("breakOut"));
                breakOuts.add(breakOut);
            }
        });

        sortJSONArray(runDistances, "run");
        teamDataAo.setRunDistances(runDistances);

        sortJSONArray(overallSpeeds, "SUP");
        teamDataAo.setOverallSpeeds(overallSpeeds);

        sortJSONArray(overallSpeedCounts, "SUP");
        teamDataAo.setOverallSpeedCounts(overallSpeedCounts);

        sortJSONArray(maxSprintSpeeds, "value");
        teamDataAo.setMaxSprintSpeeds(maxSprintSpeeds);

        sortJSONArray(leftOrRightTurnings, "sum");
        teamDataAo.setLeftOrRightTurnings(leftOrRightTurnings);

        sortJSONArray(startingsOrBrakes, "sum");
        teamDataAo.setStartingsOrBrakes(startingsOrBrakes);

        sortJSONArray(breakOuts, "breakOut");
        teamDataAo.setBreakOuts(breakOuts);
    }

    /**
     * @desc: 个人专业数据
     * @author: DH
     * @date: 16:06
     */
    public void setOverall(PersonDataAo personDataAo) {
        //专业数据
        JSONObject overallSpeed = new JSONObject();
        JSONObject overallSpeedCount = new JSONObject();
        JSONArray speedDataList = JSON.parseArray(personDataAo.getSpeedDataList().toJSONString());
        for (int i = 0; i < speedDataList.size(); i++) {
            JSONObject speed = speedDataList.getJSONObject(i);
            overallSpeed.put(speed.getString("speed"), speed.getInteger("distance"));
            if (speed.getString("speed").equals("EXCEED")) {
                overallSpeedCount.put("exceedCount", speed.getInteger("count"));
            } else if ((speed.getString("speed").equals("SUP"))) {
                overallSpeedCount.put("supCount", speed.getInteger("count"));
            }
        }
        if (overallSpeed.get("EXCEED") == null || overallSpeed.get("SUP") == null) {//之前的数据防止报错
            overallSpeed.put("EXCEED", 0);
            overallSpeedCount.put("exceedCount", 0);
            overallSpeed.put("SUP", 0);
            overallSpeedCount.put("supCount", 0);
        }
        personDataAo.setOverallSpeed(overallSpeed);
        personDataAo.setOverallSpeedCount(overallSpeedCount);
    }

    /**
     * @desc: 设置个人数据mvp
     * @author: DH
     * @date: 2023/11/6 16:06
     */
    public void setPersonMvp(PersonDataAo personDataAo, List<PersonData> personDataList) {
        JSONArray runRanking = new JSONArray(); //跑动排名
        runAndPassRanking(personDataList, runRanking);
        List<JSONObject> list = new ArrayList<>(); //mvp 未排序前
        for (int i = 0; i < runRanking.size(); i++) {
            JSONObject r = runRanking.getJSONObject(i);
            JSONObject v = new JSONObject();
            v.put("studentId", r.getLong("studentId"));
            v.put("price", mvpPrice(r.getLong("runDistance"), 0));
            v.put("runDistance", r.getLong("runDistance"));

            if (v.get("runDistance") == null) {
                v.put("runDistance", 0);
            }
            list.add(v);
        }
        JSONObject mvp = list.stream().max(Comparator.comparingDouble((e) -> e.getDouble("runDistance"))).get();
        mvp.put("studentName", studentInfoService.getById(mvp.getLongValue("studentId")).getName());
        personDataAo.setMvp(mvp);
    }

    /**
     * @desc: 专业数据（团队平均）
     * @author: DH
     * @date: 2023/11/6 15:20
     */
    public void calculateTeamAvg(List<PersonData> personDatas, TeamDataAo teamDataAo, PersonDataAo personDataAo) {
        JSONObject overallSpeed = new JSONObject();
        JSONObject overallSpeedCount = new JSONObject();
        JSONObject depthData = new JSONObject();
        if (personDatas == null) {//没有团队数据
            overallSpeed.put("LOW", -1);
            overallSpeed.put("MID", -1);
            overallSpeed.put("HIGH", -1);
            overallSpeed.put("EXCEED", -1);
            overallSpeed.put("SUP", -1);
            overallSpeedCount.put("exceedCount", -1);
            overallSpeedCount.put("supCount", -1);
            depthData.put("brake", -1);
            depthData.put("breakOut", -1);
            depthData.put("starting", -1);
            depthData.put("leftTurning", -1);
            teamDataAo.setTeamAvgDepthData(depthData);
            teamDataAo.setTeamAvgOverallSpeed(overallSpeed);
            teamDataAo.setTeamAvgOverallSpeedCount(overallSpeedCount);
            teamDataAo.setTeamAvgMaxSprintSpeed(-1.0);
            return;
        }
        overallSpeed.put("LOW", 0);
        overallSpeed.put("MID", 0);
        overallSpeed.put("HIGH", 0);
        overallSpeed.put("EXCEED", 0);
        overallSpeed.put("SUP", 0);
        overallSpeedCount.put("exceedCount", 0);
        overallSpeedCount.put("supCount", 0);
        depthData.put("brake", 0);
        depthData.put("breakOut", 0);
        depthData.put("starting", 0);
        depthData.put("leftTurning", 0);
        depthData.put("rightTurning", 0);
        personDatas.forEach(e -> {
            JSONArray speedDataList = JSON.parseArray(e.getSpeedDataList().toJSONString());
            for (int i = 0; i < speedDataList.size(); i++) {
                JSONObject speed = speedDataList.getJSONObject(i);
                overallSpeed.put(speed.getString("speed"), overallSpeed.getInteger(speed.getString("speed")) + speed.getInteger("distance"));
                if (speed.getString("speed").equals("EXCEED")) {
                    overallSpeedCount.put("exceedCount", overallSpeedCount.getInteger("exceedCount") + speed.getInteger("count"));
                } else if ((speed.getString("speed").equals("SUP"))) {
                    overallSpeedCount.put("supCount", overallSpeedCount.getInteger("supCount") + speed.getInteger("count"));
                }
            }

            JSONObject depthDataTeam = e.getDepthData();
            if (depthDataTeam != null) {
                depthData.put("brake", depthDataTeam.getInteger("brake") + depthData.getInteger("brake"));
                depthData.put("breakOut", depthDataTeam.getInteger("breakOut") + depthData.getInteger("breakOut"));
                depthData.put("starting", depthDataTeam.getInteger("starting") + depthData.getInteger("starting"));
                depthData.put("leftTurning", depthDataTeam.getInteger("leftTurning") + depthData.getInteger("leftTurning"));
                depthData.put("rightTurning", depthDataTeam.getInteger("rightTurning") + depthData.getInteger("rightTurning"));
            }
        });
        int avgCount = personDatas.stream().filter(e -> e.getStepCount() > 100).collect(Collectors.toList()).size();
        if (avgCount == 0) {
            avgCount = 1;
        }
        overallSpeed.put("LOW", overallSpeed.getInteger("LOW") / avgCount);
        overallSpeed.put("MID", overallSpeed.getInteger("MID") / avgCount);
        overallSpeed.put("HIGH", overallSpeed.getInteger("HIGH") / avgCount);
        overallSpeed.put("EXCEED", overallSpeed.getInteger("EXCEED") / avgCount);
        overallSpeed.put("SUP", overallSpeed.getInteger("SUP") / avgCount);
        overallSpeedCount.put("exceedCount", overallSpeedCount.getInteger("exceedCount") / avgCount);
        overallSpeedCount.put("supCount", overallSpeedCount.getInteger("supCount") / avgCount);
        depthData.put("brake", depthData.getInteger("brake") / avgCount);
        depthData.put("breakOut", depthData.getInteger("breakOut") / avgCount);
        depthData.put("starting", depthData.getInteger("starting") / avgCount);
        depthData.put("leftTurning", depthData.getInteger("leftTurning") / avgCount);
        depthData.put("rightTurning", depthData.getInteger("rightTurning") / avgCount);


        double avgMaxSprintSpeed = personDatas.stream().mapToDouble(PersonData::getMaxSprintSpeed).average().getAsDouble();
        BigDecimal bd = new BigDecimal(avgMaxSprintSpeed);
        avgMaxSprintSpeed = bd.setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
        if (teamDataAo != null) {
            teamDataAo.setTeamAvgDepthData(depthData);
            teamDataAo.setTeamAvgOverallSpeed(overallSpeed);
            teamDataAo.setTeamAvgOverallSpeedCount(overallSpeedCount);
            teamDataAo.setTeamAvgMaxSprintSpeed(avgMaxSprintSpeed);
        } else {
            personDataAo.setTeamAvgDepthData(depthData);
            personDataAo.setTeamAvgOverallSpeed(overallSpeed);
            personDataAo.setTeamAvgOverallSpeedCount(overallSpeedCount);
            personDataAo.setTeamAvgMaxSprintSpeed(avgMaxSprintSpeed);
        }
    }


    /**
     * @desc: 存个人数据
     * @author: DH
     * @date: 2023/11/6 15:19
     */
    public boolean savePersonData(JSONObject resObject, Long teamId, Long matchId, String matchType) {
        JSONArray personData = resObject.getJSONArray("personData");
        for (int i = 0; i < personData.size(); i++) {
            personData.getJSONObject(i).put("studentId", personData.getJSONObject(i).getLongValue("userId"));
            personData.getJSONObject(i).put("matchId", matchId);
            personData.getJSONObject(i).put("matchType", matchType);
        }
        List<PersonData> list = JSON.parseArray(JSONArray.toJSONString(personData), PersonData.class);
        transparentMessageService.sendTransparentByMonitorUpload(list);
        List<PersonData> dataList = personDataService.list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchId, matchId).eq(PersonData::getMatchType, matchType));
        Map<Long, PersonData> personDataMap = dataList.stream().collect(Collectors.toMap(PersonData::getStudentId, Function.identity(), (key1, key2) -> key2));
        for (PersonData o:list) {
            if (personDataMap.containsKey(o.getStudentId())) {
                o.setId(personDataMap.get(o.getStudentId()).getId());
            }
            if (matchType.equals("MONITOR")) {
                setGrowTaller(o);
            }
            o.setTeamId(teamId);
            o.setAppLook(false);
        }
        List<PersonData> list1 = new ArrayList<>();
        for (PersonData o1:list) {
            if (o1.getRunDistance() == 0) {
                PersonData o2 = paddingData(o1);
                list1.add(o2);
            }else{
                list1.add(o1);
            }
        }
        return personDataService.saveOrUpdateBatch(list1);
    }

    public PersonData paddingData(PersonData personData) {
        Random rand = new Random();
        int randomInt = rand.nextInt(21 - 11 + 1) + 11;
        PersonData personData1 = personDataService.getById(randomInt);
        personData1.setId(personData.getId());
        personData1.setStudentId(personData.getStudentId());
        personData1.setTeamId(personData.getTeamId());
        personData1.setMatchType(personData.getMatchType());
        personData1.setMatchId(personData.getMatchId());
        personData1.setExerciseTime(personData.getExerciseTime());
        personData1.setRunExerciseTime((int)(personData.getExerciseTime()*0.2));
        personData1.setStepExerciseTime((int)(personData.getExerciseTime()*0.8));
        personData1.setCreateTime(new Date());
        personData1.setUpdateTime(new Date());
        return SerializationUtils.clone(personData1);
    }

    /**
     * @desc: 存团队数据
     * @author: DH
     * @date: 2023/11/6 15:19
     */
    public boolean saveTeamData(JSONObject resObject, Long teamId, Long matchId, String matchType) {
        JSONObject teamOb = resObject.getJSONObject("teamData");
        TeamData teamPo = JSONObject.toJavaObject(teamOb, TeamData.class);
        TeamData teamData = teamDataService.findOfTeam(matchId, teamId, matchType);
        teamPo.setTeamId(teamId);
        teamPo.setMatchId(matchId);
        teamPo.setMatchType(matchType);
        if (teamData != null) {
            teamPo.setId(teamData.getId());
            teamPo.setVersion(teamData.getVersion());
        }
        List<CurveDto> teamPoCurve = JSON.parseArray(teamPo.getCurves().toJSONString(), CurveDto.class);
        List<TeamData> teamDataList = teamDataService.findOfTeamList(teamId, matchType);
        CurveDto avgCurve = new CurveDto();
        avgCurve.setCurve(CurveEnum.TEAM_HISTORY_CONSUME);
        for (TeamData t : teamDataList) {
            CurveDto curveDto = JSON.parseObject(JSON.toJSONString(t.getCurves().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("curve").equals("TEAM_CONSUME")).findFirst().get()), CurveDto.class);
            avgCurve.setAxisX(curveDto.getAxisX());
            avgCurve.setAxisY(curveDto.getAxisY());
        }
        teamPoCurve.add(avgCurve);
        teamPo.setCurves(JSON.parseArray(JSON.toJSONString(teamPoCurve)));
        return teamDataService.saveOrUpdate(teamPo);
    }

    /**
     * @desc: 排序跑动
     * @author: DH
     * @date: 2023/11/6 15:18
     */
    public JSONArray sortedMoveDistance(List<PersonData> personDatas, String speed, String valueKey) {
        JSONArray jsonArray = new JSONArray();

        List<PersonData> rankingData = personDatas.stream().sorted((e1, e2) -> {
            int v1 = e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals(speed) || speed.trim().length() == 0).mapToInt(e -> ((JSONObject) e).getInteger(valueKey)).sum();
            int v2 = e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals(speed) || speed.trim().length() == 0).mapToInt(e -> ((JSONObject) e).getInteger(valueKey)).sum();
            return v2 - v1;
        }).collect(Collectors.toList());

        rankingData.stream().forEach(e -> {
            JSONObject jsonObject = new JSONObject();
            Double value = e.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(o -> ((JSONObject) o).getString("speed").equals(speed) || speed.trim().length() == 0).mapToDouble(o -> ((JSONObject) o).getInteger(valueKey)).sum();
            StudentInfo stu = studentInfoService.getById(e.getStudentId());
            jsonObject.put("name", stu.getName());
            jsonObject.put("headImg", stu.getHeadImg());
            jsonObject.put("id", e.getStudentId());
            jsonObject.put("value", value);
            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }

    /**
     * @desc: 体能消耗
     * @author: DH
     * @date: 2023/11/6 15:18
     */
    public Object getPhysicalAnalysis(Long matchId, Long teamId, String matchType) {
        JSONObject object = new JSONObject();
        List<JSONObject> list = new ArrayList<>();
        List<PersonData> personDatas = personDataService.findOfMatchId(matchId, teamId, matchType).parallelStream().sorted((e1, e2) -> e2.getCalorie() - e1.getCalorie()).collect(Collectors.toList());
        for (PersonData person : personDatas) {
            JSONObject o = new JSONObject();
            StudentInfo studentInfo = studentInfoService.getById(person.getStudentId());
            Integer calorie = person.getCalorie();
            o.put("id", studentInfo.getId());
            o.put("headImg", studentInfo.getHeadImg());
            o.put("name", studentInfo.getName());
            o.put("calorie", calorie);
            list.add(o);
        }
        object.put("calories", list);
        object.put("avgCalorie", list.stream().mapToDouble(e -> e.getInteger("calorie")).average().orElse(0.0));
        return object;
    }

    /**
     * @desc: 跑动排名
     * @author: DH
     * @date: 2023/11/6 15:18
     */
    public JSONArray moveDistanceRanking(Long matchId, String rankingKey, List<PersonData> personDatas, Long teamId, String matchType) {
        if (personDatas == null) {
            personDatas = personDataService.findOfMatchId(matchId, teamId, matchType);
        }
        JSONArray jsonArray = new JSONArray();
        List<PersonData> rankingData = new ArrayList<>();
        switch (rankingKey) {
            case "wholeMoveDistance":
                //总跑动 (m)
                rankingData = personDatas.stream().sorted(Comparator.comparingInt(PersonData::getRunDistance).reversed()).collect(Collectors.toList());
                break;
            case "maxSprintSpeed":
                // 最高冲刺速度 (m)
                rankingData = personDatas.stream().sorted(Comparator.comparingDouble(PersonData::getMaxSprintSpeed).reversed()).collect(Collectors.toList());
                break;
            case "highMoveDistance":
                //高速跑动 (m)
                jsonArray = sortedMoveDistance(personDatas, "HIGH", "distance");
                break;
            case "midMoveDistance":
                //中速跑动 (m)
                jsonArray = sortedMoveDistance(personDatas, "MID", "distance");
                break;
            case "lowMoveDistance":
                // 低速跑动 (m)
                jsonArray = sortedMoveDistance(personDatas, "LOW", "distance");
                break;
        }
        if (jsonArray.size() == 0) {
            for (PersonData e : rankingData) {
                JSONObject jsonObject = new JSONObject();
                StudentInfo stu = studentInfoService.getById(e.getStudentId());
                if (stu != null) {
                    jsonObject.put("name", stu.getName());
                    jsonObject.put("headImg", stu.getHeadImg());
                    jsonObject.put("id", e.getStudentId());
                    if ("wholeMoveDistance".equals(rankingKey)) {
                        jsonObject.put("value", e.getRunDistance());
                    } else if ("maxSprintSpeed".equals(rankingKey)) {
                        jsonObject.put("value", e.getMaxSprintSpeed());
                    }
                    jsonArray.add(jsonObject);
                }
            }
        }
        return jsonArray;
    }

    /**
     * 判断运动是否达标
     */
    public int standard(long exerciseTime) {
        if (exerciseTime < 60) {
            return 0;
        } else if (exerciseTime >= 80) {
            return 2;
        } else if (exerciseTime >= 60 && exerciseTime < 80) {
            return 1;
        }
        return 0;
    }

    /**
     * @desc: JSONArray排序
     * @author: DH
     * @date: 2021/3/13 14:09
     */
    public JSONArray sortJSONArray(JSONArray jsonArray, String sortKey) {
        jsonArray.sort((o1, o2) -> {
            Double v1 = JSONObject.parseObject(JSON.toJSONString(o1)).getDoubleValue(sortKey);
            Double v2 = JSONObject.parseObject(JSON.toJSONString(o2)).getDoubleValue(sortKey);
            return Double.compare(v2, v1);
        });
        return jsonArray;
    }

    /**
     * @desc: 计算劳模值
     * @author: DH
     * @date: 2021/3/13 14:16
     */
    public double mvpPrice(long run, long pass) {
        BigDecimal bg = new BigDecimal(pass * 0.45 + run * 0.55);
        return bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * @desc: 获取个人数据需要排名的KEY
     * @author: DH
     * @date: 2021/3/17 15:11
     */
    public List<String> getRankingKey() {
        List<String> keys = new ArrayList<>();
        keys.add("stepCount");//步数
        keys.add("runDistance");//跑动
        keys.add("jump");//起跳
        keys.add("maxSprintSpeed");//最高冲刺速度
        keys.add("highDistance");//高速跑动距离
        return keys;
    }

    public long calculatePercentage(double part, double total) {
        if (total == 0) {
            throw new IllegalArgumentException("总数不能为0");
        }
        double percentage = (part / total) * 100;
        return (long) percentage;
    }
}
