package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.Game;
import io.geekidea.springbootplus.using.entity.TeamUserApp;
import io.geekidea.springbootplus.using.param.TeamUserAppPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface TeamUserAppService extends BaseService<TeamUserApp> {
    List<TeamUserApp> getByUserId(Long userId);

    List<Long> getTeamIdsByUserId(Long userId);

    List<TeamUserApp> getByTeamId(Long teamId);

    Long getByUserIdAndGameId(Long userId,Game game);
}
