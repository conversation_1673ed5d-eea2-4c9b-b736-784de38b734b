package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.Game;
import io.geekidea.springbootplus.using.entity.Team;
import io.geekidea.springbootplus.using.entity.TeamApp;
import io.geekidea.springbootplus.using.entity.TeamUserApp;
import io.geekidea.springbootplus.using.entity.UserApp;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.mapper.TeamAppMapper;
import io.geekidea.springbootplus.using.service.GameService;
import io.geekidea.springbootplus.using.service.TeamAppService;
import io.geekidea.springbootplus.using.service.UserAppService;
import io.geekidea.springbootplus.using.service.AppMessageService;
import io.geekidea.springbootplus.using.service.TransparentMessageService;
import io.geekidea.springbootplus.using.param.TeamAppPageParam;
import io.geekidea.springbootplus.using.param.TeamAppListParam;
import io.geekidea.springbootplus.using.param.TeamMemberPageParam;
import io.geekidea.springbootplus.using.vo.TeamAppVO;
import io.geekidea.springbootplus.using.vo.TeamMemberVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.service.TeamUserAppService;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Slf4j
@Service
public class TeamAppServiceImpl extends BaseServiceImpl<TeamAppMapper, TeamApp> implements TeamAppService {

    @Autowired
    private TeamAppMapper teamAppMapper;
    @Autowired
    private TeamUserAppService teamUserAppService;
    @Autowired
    private UserAppService userAppService;
    @Autowired
    private AppMessageService appMessageService;
    @Autowired
    private TransparentMessageService transparentMessageService;
    @Autowired
    private GameService gameService;

    @Override
    public TeamApp getTeamApp(Long id) {
        return getBaseMapper().getById(id);
    }

    @Override
    public TeamApp getTeamAppWithStats(Long id) {
        TeamApp teamApp = getBaseMapper().getById(id);
        if (teamApp == null) {
            return null;
        }

        // 统计球员数量
        List<TeamUserApp> members = teamUserAppService.getByTeamId(id);
        teamApp.setMemberCount(members.size());

        // 统计赛事场次和胜负平
        calculateMatchStats(teamApp);

        return teamApp;
    }

    /**
     * 计算比赛统计信息
     */
    private void calculateMatchStats(TeamApp teamApp) {
        Long teamId = teamApp.getId();

        // 查询该球队参与的所有已结束比赛（作为主队或客队）
        LambdaQueryWrapper<Game> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(w -> w.eq(Game::getTeamId, teamId).or().eq(Game::getOpponentId, teamId))
               .eq(Game::getStatus, MatchStatusEnum.FINISHED)
               .eq(Game::getApp, true); // 只统计APP创建的比赛

        List<Game> games = gameService.list(wrapper);

        int totalMatches = games.size();
        int winCount = 0;
        int loseCount = 0;
        int drawCount = 0;

        for (Game game : games) {
            Integer teamScore;
            Integer opponentScore;

            // 判断当前球队是主队还是客队
            if (teamId.equals(game.getTeamId())) {
                // 当前球队是主队
                teamScore = game.getTeamScore();
                opponentScore = game.getOpponentScore();
            } else {
                // 当前球队是客队
                teamScore = game.getOpponentScore();
                opponentScore = game.getTeamScore();
            }

            // 统计胜负平（只有当双方得分都不为空时才统计）
            if (teamScore != null && opponentScore != null) {
                if (teamScore > opponentScore) {
                    winCount++;
                } else if (teamScore < opponentScore) {
                    loseCount++;
                } else {
                    drawCount++;
                }
            }
        }

        teamApp.setTotalMatches(totalMatches);
        teamApp.setWinCount(winCount);
        teamApp.setLoseCount(loseCount);
        teamApp.setDrawCount(drawCount);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveTeamApp(TeamApp teamApp) throws Exception {
        super.saveOrUpdate(teamApp);
        TeamUserApp teamUserApp = new TeamUserApp();
        teamUserApp.setTeamId(teamApp.getId());
        teamUserApp.setUserId(teamApp.getUserId());
        return teamUserAppService.saveOrUpdate(teamUserApp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTeamApp(TeamApp teamApp) throws Exception {
        return super.updateById(teamApp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTeamApp(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<TeamApp> getTeamAppPageList(TeamAppPageParam teamAppPageParam) throws Exception {
        Page<TeamApp> page = new PageInfo<>(teamAppPageParam);
        LambdaQueryWrapper<TeamApp> wrapper = new LambdaQueryWrapper<>();
        List<TeamUserApp> teamUserAppList = teamUserAppService.getByUserId(teamAppPageParam.getUserId());
        if (!teamAppPageParam.getOpponent() && teamUserAppList.size() == 0) {
            return new Paging<>();
        } else if (teamUserAppList.size() > 0) {
            List<Long> ids = teamUserAppList.stream().map(TeamUserApp::getTeamId).distinct().collect(Collectors.toList());
            if (teamAppPageParam.getOpponent()) {
                wrapper.eq(teamAppPageParam.getUserId()!=null&&teamAppPageParam.getUserId()!=-1l,TeamApp::getCreateUserId,teamAppPageParam.getUserId()).notIn(TeamApp::getId, ids);
            } else {
                wrapper.in(TeamApp::getId, ids);
            }
        }
        wrapper.eq(teamAppPageParam.getCourseId() != null && teamAppPageParam.getCourseId() != -1, TeamApp::getCourseId, teamAppPageParam.getCourseId());
        wrapper.like(teamAppPageParam.getKeyword() != null, TeamApp::getFullName, teamAppPageParam.getKeyword());
        wrapper.orderByDesc(TeamApp::getId);
        IPage<TeamApp> iPage = teamAppMapper.selectPage(page, wrapper);
        return new Paging<TeamApp>(iPage);
    }

    @Override
    public Boolean startedCount(String matchType, Long teamId, Boolean app) {
        return gameService.startedCount(teamId, app);
    }

    @Override
    public Paging<TeamAppVO> getHomeTeamsList(TeamAppListParam param, Long currentUserId) throws Exception {
        Page<TeamApp> page = new Page<>(param.getPageIndex(), param.getPageSize());
        LambdaQueryWrapper<TeamApp> wrapper = new LambdaQueryWrapper<>();

        // 主队筛选（isHomeTeam为true或null的都算主队）
        wrapper.and(w -> w.eq(TeamApp::getIsHomeTeam, true).or().isNull(TeamApp::getIsHomeTeam));

        // 根据createdByMe参数筛选
        if (param.getCreatedByMe() != null) {
            if (param.getCreatedByMe()) {
                wrapper.eq(TeamApp::getCreateUserId, currentUserId);
            } else {
                wrapper.ne(TeamApp::getCreateUserId, currentUserId);
            }
        }

        // 运动类型筛选
        if (param.getCourseId() != null && param.getCourseId() != -1L) {
            wrapper.eq(TeamApp::getCourseId, param.getCourseId());
        }

        // 关键词搜索
        if (param.getKeyword() != null && !param.getKeyword().trim().isEmpty()) {
            wrapper.like(TeamApp::getFullName, param.getKeyword().trim());
        }

        wrapper.orderByDesc(TeamApp::getCreateTime);

        IPage<TeamApp> iPage = page(page, wrapper);

        // 转换为VO
        List<TeamAppVO> voList = convertToVOList(iPage.getRecords(), currentUserId);

        // 创建结果对象
        Paging<TeamAppVO> result = new Paging<>();
        result.setTotal(iPage.getTotal());
        result.setRecords(voList);
        result.setPageIndex(iPage.getCurrent());
        result.setPageSize(iPage.getSize());

        return result;
    }

    @Override
    public Paging<TeamAppVO> getJoinedTeamsList(TeamAppListParam param, Long currentUserId) throws Exception {
        // 先获取用户加入的球队ID列表
        List<TeamUserApp> userTeams = teamUserAppService.getByUserId(currentUserId);
        List<Long> teamIds = userTeams.stream()
                .map(TeamUserApp::getTeamId)
                .collect(Collectors.toList());

        if (teamIds.isEmpty()) {
            return new Paging<>();
        }

        Page<TeamApp> page = new Page<>(param.getPageIndex(), param.getPageSize());
        LambdaQueryWrapper<TeamApp> wrapper = new LambdaQueryWrapper<>();

        // 只查询用户加入的球队，但排除用户创建的球队
        wrapper.in(TeamApp::getId, teamIds)
               .ne(TeamApp::getCreateUserId, currentUserId);

        // 运动类型筛选
        if (param.getCourseId() != null && param.getCourseId() != -1) {
            wrapper.eq(TeamApp::getCourseId, param.getCourseId());
        }

        // 关键词搜索
        if (param.getKeyword() != null && !param.getKeyword().trim().isEmpty()) {
            wrapper.like(TeamApp::getFullName, param.getKeyword().trim());
        }

        wrapper.orderByDesc(TeamApp::getCreateTime);

        IPage<TeamApp> iPage = page(page, wrapper);

        // 转换为VO
        List<TeamAppVO> voList = convertToVOList(iPage.getRecords(), currentUserId);

        // 创建结果对象
        Paging<TeamAppVO> result = new Paging<>();
        result.setTotal(iPage.getTotal());
        result.setRecords(voList);
        result.setPageIndex(iPage.getCurrent());
        result.setPageSize(iPage.getSize());

        return result;
    }


    @Override
    public Paging<TeamMemberVO> getTeamMembersPageList(TeamMemberPageParam param, Long currentUserId) throws Exception {
        // 检查当前用户是否为球队成员
        if (!isMember(param.getTeamId(), currentUserId)) {
            throw new Exception("您不是该球队成员，无权查看成员列表");
        }

        // 获取球队信息
        TeamApp team = getById(param.getTeamId());
        if (team == null) {
            throw new Exception("球队不存在");
        }

        // 获取球队成员总数，用于分页计算
        List<TeamUserApp> allTeamUsers = teamUserAppService.getByTeamId(param.getTeamId());

        // 构建成员VO列表（先获取所有成员，然后在内存中进行筛选和分页）
        List<TeamMemberVO> allMembers = new ArrayList<>();

        for (TeamUserApp teamUser : allTeamUsers) {
            UserApp user = userAppService.getById(teamUser.getUserId());
            if (user != null) {
                TeamMemberVO memberVO = new TeamMemberVO();
                BeanUtils.copyProperties(user, memberVO);
                memberVO.setUserId(user.getId());
                memberVO.setIsCaptain(team.getCaptainUserId() != null && team.getCaptainUserId().equals(user.getId()));
                memberVO.setIsCreator(team.getCreateUserId().equals(user.getId()));
                memberVO.setJoinTime(teamUser.getCreateTime());

                // 应用筛选条件
                boolean shouldInclude = true;

                // 关键词搜索（姓名）
                if (param.getKeyword() != null && !param.getKeyword().trim().isEmpty()) {
                    if (user.getName() == null || !user.getName().contains(param.getKeyword().trim())) {
                        shouldInclude = false;
                    }
                }

                // 性别筛选
                if (param.getSex() != null && !param.getSex().equals(user.getSex())) {
                    shouldInclude = false;
                }

                // 队长筛选
                if (param.getIsCaptain() != null && !param.getIsCaptain().equals(memberVO.getIsCaptain())) {
                    shouldInclude = false;
                }

                if (shouldInclude) {
                    allMembers.add(memberVO);
                }
            }
        }

        // 手动分页处理
        int total = allMembers.size();
        int pageIndex = param.getPageIndex().intValue();
        int pageSize = param.getPageSize().intValue();
        int startIndex = (pageIndex - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        List<TeamMemberVO> pageMembers = new ArrayList<>();
        if (startIndex < total) {
            pageMembers = allMembers.subList(startIndex, endIndex);
        }

        // 构建分页结果
        Paging<TeamMemberVO> paging = new Paging<>();
        paging.setRecords(pageMembers);
        paging.setTotal((long) total);
        paging.setPageSize((long) pageSize);
        paging.setPageIndex((long) pageIndex);
        paging.setTotal((long) Math.ceil((double) total / pageSize));

        return paging;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeMember(Long teamId, Long userId, Long currentUserId) throws Exception {
        // 检查当前用户是否为队长
        if (!isCaptain(teamId, currentUserId)) {
            throw new Exception("只有队长才能删除球员");
        }

        // 不能删除自己
        if (userId.equals(currentUserId)) {
            throw new Exception("不能删除自己");
        }

        // 检查要删除的用户是否为球队成员
        if (!isMember(teamId, userId)) {
            throw new Exception("该用户不是球队成员");
        }

        // 删除球队成员关系
        LambdaQueryWrapper<TeamUserApp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TeamUserApp::getTeamId, teamId)
               .eq(TeamUserApp::getUserId, userId);
        teamUserAppService.remove(wrapper);

        // 获取球队信息
        TeamApp team = getById(teamId);

        // 发送消息通知被删除的用户
        appMessageService.sendTeamMessage(
            userId,
            "KICKED_OUT",
            teamId,
            team.getFullName(),
            team.getLogo(),
            "您已被移出球队"
        );

        // 发送透传消息
        transparentMessageService.sendTransparentByKickedOut(userId, teamId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean dissolveTeam(Long teamId, Long currentUserId) throws Exception {
        // 检查当前用户是否为队长
        if (!isCaptain(teamId, currentUserId)) {
            throw new Exception("只有队长才能解散球队");
        }

        // 获取球队信息
        TeamApp team = getById(teamId);
        if (team == null) {
            throw new Exception("球队不存在");
        }

        // 获取所有球队成员
        List<TeamUserApp> teamUsers = teamUserAppService.getByTeamId(teamId);

        // 通知所有成员球队解散
        for (TeamUserApp teamUser : teamUsers) {
            if (!teamUser.getUserId().equals(currentUserId)) {
                appMessageService.sendTeamMessage(
                    teamUser.getUserId(),
                    "TEAM_DISSOLVED",
                    teamId,
                    team.getFullName(),
                    team.getLogo(),
                    "球队已被解散"
                );
            }
        }

        // 删除所有球队成员关系
        LambdaQueryWrapper<TeamUserApp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TeamUserApp::getTeamId, teamId);
        teamUserAppService.remove(wrapper);

        // 删除球队（逻辑删除）
        removeById(teamId);

        // 发送透传消息
        transparentMessageService.sendTransparentByTeamDissolve(teamId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean quitTeam(Long teamId, Long currentUserId) throws Exception {
        // 检查用户是否为球队成员
        if (!isMember(teamId, currentUserId)) {
            throw new Exception("您不是该球队成员");
        }

        // 获取球队信息
        TeamApp team = getById(teamId);
        if (team == null) {
            throw new Exception("球队不存在");
        }

        // 检查是否为队长，队长不能直接退出
        if (isCaptain(teamId, currentUserId)) {
            throw new Exception("队长不能直接退出球队，请先转让队长或解散球队");
        }

        // 删除球队成员关系
        LambdaQueryWrapper<TeamUserApp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TeamUserApp::getTeamId, teamId)
               .eq(TeamUserApp::getUserId, currentUserId);
        teamUserAppService.remove(wrapper);

        // 通知队长有成员退出
        Long captainId = team.getCaptainUserId() != null ? team.getCaptainUserId() : team.getCreateUserId();
        appMessageService.sendTeamMessage(
            captainId,
            "MEMBER_QUIT",
            teamId,
            team.getFullName(),
            team.getLogo(),
            "有成员退出球队"
        );

        // 发送透传消息
        transparentMessageService.sendTransparentByMemberQuit(captainId, teamId, currentUserId);

        return true;
    }

    @Override
    public boolean isCaptain(Long teamId, Long userId) {
        TeamApp team = getById(teamId);
        if (team == null) {
            return false;
        }

        // 如果设置了队长，检查是否为队长
        if (team.getCaptainUserId() != null) {
            return team.getCaptainUserId().equals(userId);
        }

        // 如果没有设置队长，检查是否为创建者
        return team.getCreateUserId().equals(userId);
    }

    @Override
    public boolean isMember(Long teamId, Long userId) {
        LambdaQueryWrapper<TeamUserApp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TeamUserApp::getTeamId, teamId)
               .eq(TeamUserApp::getUserId, userId);
        return teamUserAppService.count(wrapper) > 0;
    }

    /**
     * 转换为VO列表
     */
    private List<TeamAppVO> convertToVOList(List<TeamApp> teamList, Long currentUserId) {
        List<TeamAppVO> voList = new ArrayList<>();

        for (TeamApp team : teamList) {
            TeamAppVO vo = new TeamAppVO();
            BeanUtils.copyProperties(team, vo);

            // 计算成员数量
            List<TeamUserApp> members = teamUserAppService.getByTeamId(team.getId());
            vo.setMemberCount(members.size());

            // 设置用户相关标识
            vo.setIsCreatedByMe(team.getCreateUserId().equals(currentUserId));
            vo.setIsCaptain(isCaptain(team.getId(), currentUserId));
            vo.setIsMember(isMember(team.getId(), currentUserId));

            voList.add(vo);
        }

        return voList;
    }
}
