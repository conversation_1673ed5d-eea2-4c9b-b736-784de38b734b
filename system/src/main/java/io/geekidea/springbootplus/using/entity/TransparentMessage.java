package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TransparentMessage对象")
@TableName(autoResultMap = true)
public class TransparentMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("接收人")
    private Long receiveUserId;

    @ApiModelProperty("发送的消息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON message;

    @ApiModelProperty("PAD||APP")
    private String type;

    @ApiModelProperty("发送状态")
    private Boolean status;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    private Date gmtModified;

}
