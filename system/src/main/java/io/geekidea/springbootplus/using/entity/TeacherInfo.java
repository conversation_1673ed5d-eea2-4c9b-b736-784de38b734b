package io.geekidea.springbootplus.using.entity;

import java.math.BigDecimal;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TeacherInfo对象")
@TableName(autoResultMap = true)
public class TeacherInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "学校id不能为空")
    @ApiModelProperty("学校id")
    private Long schoolId;

    @NotNull(message = "用户id不能为空")
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别 0女 1男")
    private Integer sex;

    @ApiModelProperty("身高（厘米）")
    private Integer height;

    @ApiModelProperty("体重（kg）")
    private BigDecimal weight;

    @ApiModelProperty("简介")
    private String introduction;

    @ApiModelProperty("证书")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List certificate;

    @ApiModelProperty("导航文字")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray navigationText;

    @ApiModelProperty("大数据网站url")
    private String bigUrl;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("出生年月时间戳")
    private Date ageTime;

    @ApiModelProperty("执教年龄")
    private Integer teachingAge;

    @ApiModelProperty("头像")
    private String headImg;

    @ApiModelProperty("职务")
    private String duty;

    @ApiModelProperty("是否有学校权限")
    private Boolean schoolAuth;

    @ApiModelProperty("该老师下是否有班级")
    @TableField(exist = false)
    private Boolean isPossessTeam;

    @ApiModelProperty("创建的班级数量")
    @TableField(exist = false)
    private Integer classCount;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Integer deleted;

}
