package io.geekidea.springbootplus.using.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.geekidea.springbootplus.using.entity.BallPark;
import io.geekidea.springbootplus.using.param.BallParkPageParam;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.io.Serializable;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Repository
public interface BallParkMapper extends BaseMapper<BallPark> {

    List<BallPark> historicBallPark(@Param("courseId") Long courseId, @Param("studentId") Long studentId,@Param("search")String search);

}
