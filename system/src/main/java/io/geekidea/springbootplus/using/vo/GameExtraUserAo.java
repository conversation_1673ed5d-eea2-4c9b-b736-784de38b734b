package io.geekidea.springbootplus.using.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "GameExtraUserAo")
public class GameExtraUserAo {
    @ApiModelProperty("记时/计数id")
    private Long id;

    @ApiModelProperty("记时/计数名称")
    private String name;

    @ApiModelProperty("1记时 2计数")
    private Integer type;

    @ApiModelProperty("图片Url")
    private String iconUrl;

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("记时开始时间")
    private Long startTime;

    @ApiModelProperty("记时结束时间")
    private Long stopTime;

    @ApiModelProperty("排名")
    private Integer ranking;

    @ApiModelProperty("记时or计数的值 手机端使用根据type判断计时还是计数")
    private Long commonValue;

    @ApiModelProperty("创建时间")
    private Date createDate;

    @ApiModelProperty("更新时间")
    private Date updateDate;
}
