package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.service.DemoMacQrcodeService;
import io.geekidea.springbootplus.using.service.HardwareService;
import io.geekidea.springbootplus.using.service.StudentInfoService;
import io.geekidea.springbootplus.using.vo.BatchExchangeBindAo;
import io.geekidea.springbootplus.using.vo.DemoMacQrcodeAo;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.*;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@RestController
@RequestMapping("/using/hardware")
@Module("teambox")
@Api(value = "设备相关", tags = {"设备相关"})
public class HardwareController extends BaseController {

    @Autowired
    private HardwareService hardwareService;

    @Autowired
    private DemoMacQrcodeService demoMacQrcodeService;

    @Autowired
    private StudentInfoService studentInfoService;

    @PostMapping("/addDemoMacQrcode")
    @OperationLog(name = "绑定二维码入库", type = OperationLogType.ADD)
    @ApiOperation(value = "绑定二维码入库", response = ApiResult.class)
    public ApiResult<Boolean> addDemoMacQrcode(@RequestBody DemoMacQrcode demoMacQrcode) throws Exception {
        boolean flag = demoMacQrcodeService.saveDemoMacQrcode(demoMacQrcode,getCurrentId());
        return ApiResult.result(flag);
    }


    /**
     * @desc: 读取文本批量录入mac
     * @author: DH
     * @date: 2022/3/9 15:21
     */
    @PostMapping(value = "/readerTxtInsert")
    @Transactional
    public ApiResult<Boolean> readerTxtInsert(@RequestParam(required = false) List<MultipartFile> files) {
        for (MultipartFile multfile : files) {
            try (InputStream inputStream = multfile.getInputStream();
                 BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, "utf-8"))) {
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    if (line.trim().isEmpty()) {
                        System.out.println("\t");
                    } else {
                        String trimmedLine = line.trim();
                        String qrcode = trimmedLine.substring(0, 24).trim();
                        String mac = trimmedLine.substring(24).trim();
                        DemoMacQrcode demoMacQrcode = new DemoMacQrcode();
                        demoMacQrcode.setDemoUserId(1L);
                        demoMacQrcode.setMac(mac);
                        demoMacQrcode.setQrCode(qrcode);
                        demoMacQrcode.setBox(demoMacQrcode.getQrCode().substring(10, 14));
                        demoMacQrcode.setBoxCode(demoMacQrcode.getBox());
                       // System.out.println(demoMacQrcode);
                       demoMacQrcodeService.saveOrUpdate(demoMacQrcode);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return ApiResult.ok();
    }

    @GetMapping("/findMacByBox/{boxCode}")
    @OperationLog(name = "根据箱子二维码找到设备信息", type = OperationLogType.INFO)
    @ApiOperation(value = "根据箱子二维码找到设备信息", response = DemoMacQrcodeAo.class, notes = "返回是集合 code 20043:二维码不存在")
    public ApiResult<List<DemoMacQrcodeAo>> findMacBybox(@PathVariable("boxCode") String boxCode) {
        List<DemoMacQrcodeAo> map = demoMacQrcodeService.findMacBybox(boxCode);
        if (map == null || map.size()==0) {
            return ApiResult.result(ApiCode.CODE_NOTEXIST, map);
        }
        return ApiResult.ok(map);
    }

    @GetMapping("/findMacByBoxNum/{boxNum}")
    @OperationLog(name = "根据箱子号找到设备信息", type = OperationLogType.INFO)
    @ApiOperation(value = "根据箱子号找到设备信息", response = DemoMacQrcodeAo.class, notes = "返回是集合 code 20040:box不存在")
    public ApiResult<List<DemoMacQrcodeAo>> findMacByboxNum(@PathVariable("boxNum") String boxNum) {
        List<DemoMacQrcodeAo> map = demoMacQrcodeService.findMacByboxNum(boxNum);
        if (map == null || map.size()==0) {
            return ApiResult.result(ApiCode.BOX_NOTEXIST, map);
        }
        return ApiResult.ok(map);
    }


    @GetMapping("/findMac/{qrCode}")
    @OperationLog(name = "根据teambox二维码找到设备信息", type = OperationLogType.INFO)
    @ApiOperation(value = "根据teambox二维码找到设备信息", response = DemoMacQrcodeAo.class, notes = "code 20043:二维码不存在")
    public ApiResult<DemoMacQrcodeAo> findMac(@PathVariable("qrCode") String qrCode) {
        DemoMacQrcodeAo map = demoMacQrcodeService.findMac(qrCode);
        if (map == null) {
            return ApiResult.result(ApiCode.CODE_NOTEXIST, map);
        }
        return ApiResult.ok(map);
    }

    @GetMapping("/findByMac/{mac}")
    @OperationLog(name = "根据Mac找到设备信息", type = OperationLogType.INFO)
    @ApiOperation(value = "根据Mac找到设备信息", response = DemoMacQrcodeAo.class, notes = "code 20042:MAC不存在")
    public ApiResult<DemoMacQrcodeAo> findByMac(@PathVariable("mac") String mac) {
        DemoMacQrcodeAo map = demoMacQrcodeService.findByMac(mac);
        if (map == null) {
            return ApiResult.result(ApiCode.MAC_NOTEXIST, map);
        }
        return ApiResult.ok(map);
    }

    @GetMapping("/findById/{id}")
    @OperationLog(name = "根据ID找到设备信息", type = OperationLogType.INFO)
    @ApiOperation(value = "根据ID找到设备信息", response = Hardware.class)
    public ApiResult<Hardware> findById(@PathVariable("id") Long id) {
        Hardware hardware = hardwareService.getById(id);
        return ApiResult.ok(hardware);
    }

    @PostMapping("/add")
    @OperationLog(name = "添加（学生与设备绑定）", type = OperationLogType.ADD)
    @ApiOperation(value = "添加（学生与设备绑定）", response = Hardware.class, notes =
            "code 20042:MAC不存在 20041:球鞋已被绑定 已绑定data会返回hardware对象 ")
    public ApiResult addHardware(@RequestBody Hardware hardware) throws Exception {
        StudentInfo studentInfo = studentInfoService.getById(hardware.getStudentId());
        Hardware hardware1 = hardwareService.getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getMac, hardware.getMac()).eq(Hardware::getSchoollId, studentInfo.getSchoollId()).eq(Hardware::getTeamId, studentInfo.getTeamId()).eq(Hardware::getUnbind, false));
        if (hardware1 != null && !hardware1.getStudentId().equals(hardware.getStudentId())) { //是否有其他成员绑定
            hardware1.setStudentInfo(studentInfoService.getById(hardware1.getStudentId()));
            return ApiResult.result(ApiCode.HARDWARE_BIND, hardware1);
        }
        if (hardware.getBattery() != null || hardware.getBattery() != -1) {
            hardware.setBatteryTime(new Date());
        }
        hardware1 = hardwareService.getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getMac, hardware.getMac()).eq(Hardware::getStudentId, hardware.getStudentId()).eq(Hardware::getUnbind, true));
        if (hardware1 != null) { //是否 自己原来绑定过的重新绑定
            List<Hardware> hardwares = new ArrayList<>();
            Hardware hardware2 = hardwareService.getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getStudentId, hardware.getStudentId()).eq(Hardware::getUnbind, 0));
            if (hardware2 != null) {
                hardware2.setUnbind(true);
                hardwares.add(hardware2);
            }
            hardware.setId(hardware1.getId());
            hardware.setUnbind(false);
            hardwares.add(hardware);
            hardwareService.updateHardware(hardwares);
            return ApiResult.ok(hardware);
        }

        hardware1 = hardwareService.getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getMac, hardware.getMac()).eq(Hardware::getStudentId, hardware.getStudentId()).eq(Hardware::getUnbind, false));
        if (hardware1 != null) { //重复绑定
            return ApiResult.ok(hardware1);
        }

        hardware1 = hardwareService.getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getStudentId, hardware.getStudentId()).eq(Hardware::getUnbind, false));
        if (hardware1 != null) { //换绑
            hardware1.setUnbind(true);
            hardwareService.saveOrUpdate(hardware1);
        }
        hardware.setSchoollId(studentInfo.getSchoollId());
        hardware.setTeamId(studentInfo.getTeamId());
        hardware.setUnbind(false);
        hardware.setHardType(null);
        hardwareService.saveOrUpdate(hardware);
        return ApiResult.ok(hardware);
    }

    @GetMapping("/mayBindHards/{groupId}/{teamId}")
    @OperationLog(name = "设备组剩余可绑定设备", type = OperationLogType.ADD)
    @ApiOperation(value = "设备组剩余可绑定设备", response = FacilityHardware.class, notes = "groupId:设备组id teamId:班级id  返回是数组类型")
    public ApiResult mayBindHards(@PathVariable("groupId") Long groupId, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(hardwareService.mayBindHards(groupId, teamId));
    }

    @PostMapping("/addHardwares/{teamId}")
    @OperationLog(name = "学生与设备组设备绑定", type = OperationLogType.ADD)
    @ApiOperation(value = "学生与设备组设备绑定", response = Hardware.class, notes = "body需要 studentId,mac,box,num,longNum")
    public ApiResult addHardwares(@RequestBody List<Hardware> hardwares, @PathVariable("teamId") Long teamId) {
        Long schoolId = getCurrent().getSchoolInfo().getId();
        return ApiResult.ok(hardwareService.addHardwares(hardwares, schoolId, teamId));
    }

    @PostMapping("/exchangeBind/{groupId}/{studentId}/{exchangeStuId}/{exchangeNum}/{continue}")
    @OperationLog(name = "学生与设备组设备换绑", type = OperationLogType.ADD)
    @ApiOperation(value = "学生与设备组设备换绑", response = Object.class, notes =
            "groupId:设备组id studentId:被换绑的学生id（有设备的） " +
            "exchangeStuId:要换绑的学生id（没设备的 --如果根据编号来换绑exchangeStuId传-1）" +
            " exchangeNum:要换绑的编号 没编号时传-1 " +
            "continue: 拆分模式情况，要换绑的设备已经被其他班级绑定 继续绑定 给true 其他情况给false \n" +
            "错误码 拆分模式情况 20047:该设备已被设备组其他班级绑定 \n"+
            "返回的是数据数组  如果是两个球员直接换，则返回两个球员交换的后的数据， 如果是按编号，则返回这个球员的数据\n" +
            "返回结构参考：班级下学生列表")
    public ApiResult exchangeBind(@PathVariable("groupId") Long groupId, @PathVariable("studentId") Long studentId, @PathVariable("exchangeStuId") Long exchangeStuId,@PathVariable("exchangeNum")Integer exchangeNum,@PathVariable("continue")Boolean ct) {
      Object o = hardwareService.exchangeBind(groupId,studentId,exchangeStuId,exchangeNum,ct);
      if(o!=null){
          return ApiResult.ok(o);
      }else{
          return ApiResult.result(ApiCode.HARDWAREGROUP_BIND);
      }
    }

    @PostMapping("/batchExchangeBind")
    @OperationLog(name = "学生与设备组设备换绑(批量)", type = OperationLogType.ADD)
    @ApiOperation(value = "学生与设备组设备换绑(批量)", response = Object.class, notes =
            "groupId:设备组id studentId:被换绑的学生id（有设备的） " +
                    "exchangeStuId:要换绑的学生id（没设备的 --如果根据编号来换绑exchangeStuId传-1）" +
                    " exchangeNum:要换绑的编号 没编号时传-1 " +
                    "continue: 拆分模式情况，要换绑的设备已经被其他班级绑定 继续绑定 给true 其他情况给false \n" +
                    "错误码 拆分模式情况 20047:该设备已被设备组其他班级绑定 \n"+
                    "返回的是数据数组  如果是两个球员直接换，则返回两个球员交换的后的数据， 如果是按编号，则返回这个球员的数据\n" +
                    "返回结构：班级下学生列表")
    public ApiResult batchExchangeBind(@RequestBody List<BatchExchangeBindAo> batchExchangeBindAos) {
        List<StudentInfo> studentInfos = new ArrayList<>();
        Object o = hardwareService.batchExchangeBind(batchExchangeBindAos,studentInfos);
        if(o==null){
            return ApiResult.ok(studentInfos);
        }else{
            return ApiResult.result(ApiCode.HARDWAREGROUP_BIND,o);
        }
    }

    @PostMapping("/update")
    @OperationLog(name = "修改--解绑修改unbind 参数", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改--解绑修改unbind 参数", response = ApiResult.class)
    public ApiResult<Boolean> updateHardware(@RequestBody Hardware hardware) throws Exception {
        if (hardware.getBattery() != null || hardware.getBattery() != -1) {
            hardware.setBatteryTime(new Date());
        }
        List<Hardware> hardwares = new ArrayList<>();
        hardwares.add(hardware);
        boolean flag = hardwareService.updateHardware(hardwares);
        return ApiResult.result(flag);
    }

    @PostMapping("/updateBattery")
    @OperationLog(name = "批量修改电量", type = OperationLogType.UPDATE)
    @ApiOperation(value = "批量修改电量", response = ApiResult.class,notes = "body只需要id(Hardware对象ID)和battery  [{\"id\":1,\"battery\":100}]  没有上传过电量时，返回 -1 ")
    public ApiResult<Boolean> updateBattery(@RequestBody List<Hardware> hardwares) throws Exception {
        boolean flag = hardwareService.updateBattery(hardwares);
        return ApiResult.result(flag);
    }

    @PostMapping("/updateVersion")
    @OperationLog(name = "批量修改版本", type = OperationLogType.UPDATE)
    @ApiOperation(value = "批量修改版本", response = ApiResult.class,notes = "body只需要id(Hardware对象ID)和version  [{\"id\":1,\"version\":\"100\"}] ")
    public ApiResult<Boolean> updateVersion(@RequestBody List<Hardware> hardwares) throws Exception {
        boolean flag = hardwareService.updateVersion(hardwares);
        return ApiResult.result(flag);
    }

    @PostMapping("/startHardwares/{drillId}/{teamId}")
    @OperationLog(name = "启动设备")
    @ApiOperation(value = "启动设备", response = Boolean.class, notes = "drillId:训练id body 集合可以只给 id")
    public ApiResult startHardwares(@RequestBody List<Hardware> hardwares, @PathVariable("drillId") Long drillId,@PathVariable("teamId") Long teamId) throws Exception {
        boolean flag = hardwareService.startHardwares(hardwares, drillId,teamId);
        return ApiResult.result(flag);
    }

    @PostMapping("/startHardwaresMonitorId/{monitorId}/{teamId}")
    @OperationLog(name = "监测启动设备")
    @ApiOperation(value = "监测启动设备", response = Boolean.class, notes = "monitorId:监测id body 集合可以只给 id")
    public ApiResult startHardwaresMonitorId(@RequestBody List<Hardware> hardwares, @PathVariable("monitorId") Long monitorId,@PathVariable("teamId") Long teamId) throws Exception {
        boolean flag = hardwareService.startHardwaresMonitorId(hardwares, monitorId,teamId);
        return ApiResult.result(flag);
    }

    @GetMapping(value = "/getTimestamp")
    @OperationLog(name = "获得本机时间戳")
    @ApiOperation(value = "获得本机时间戳", response = JSONObject.class, notes = "data 参数timestamp:时间搓")
    public ApiResult getTimestamp() {
        JSONObject object = new JSONObject();
        object.put("timestamp", System.currentTimeMillis());
        return ApiResult.ok(object);
    }

    @GetMapping(value = "/getPrintStuHard/{teamId}")
    @OperationLog(name = "打印学生和设备用的信息")
    @ApiOperation(value = "打印学生和设备用的信息", response = JSONObject.class, notes = "data返回 teamName:班级名字 stuHard:[{\"name\":\"学生名字\",\"num\":设备编号,\"teamName\":班级名字}]")
    public ApiResult getPrintStuHard(@PathVariable("teamId")Long teamId) { 
        return ApiResult.ok(hardwareService.getPrintStuHard(teamId));
    }

    @PostMapping(value = "/uploadFile")
    @OperationLog(name = "txt文件批量上传球鞋")
    @ApiOperation(value = "txt文件批量上传球鞋", response = JSONObject.class, notes = "格式 FC:37:25:20:42:15 007060010400020100000021 下一条数据换行 txt类型")
    public ApiResult uploadFile(@RequestParam("file") MultipartFile multfile) {
        // 获取文件后缀名
        String suffix = multfile.getOriginalFilename().substring(multfile.getOriginalFilename().lastIndexOf("."));
        final File file;
        List<DemoMacQrcode> demoMacQrcodes = new ArrayList<>();
        try {
            file = File.createTempFile(UUID.randomUUID().toString(), suffix);
            multfile.transferTo(file);
            FileInputStream fin = new FileInputStream(file);
            InputStreamReader reader = new InputStreamReader(fin);
            BufferedReader buffReader = new BufferedReader(reader);
            String strTmp = "";
            while ((strTmp = buffReader.readLine()) != null) {
                DemoMacQrcode demoMacQrcode = new DemoMacQrcode();
                strTmp = strTmp.trim();
                String mac = strTmp.substring(0, 17);
                String qrCode = strTmp.substring(17, strTmp.length());
                demoMacQrcode.setMac(mac.trim());
                demoMacQrcode.setQrCode(qrCode.trim());
                demoMacQrcodes.add(demoMacQrcode);
            }
            buffReader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        demoMacQrcodeService.saveOrUpdateBatch(demoMacQrcodes);
        return ApiResult.ok(demoMacQrcodes);
    }

    /**
     * @desc: 读取文本批量查询mac
     * @author: DH
     * @date: 2022/3/9 15:21
     */
    @PostMapping(value = "/readerTxtMac")
    public ApiResult readerTxtMac(@RequestParam(required = false) List<MultipartFile> files) {
        for (MultipartFile multfile : files) {
            try (InputStream inputStream = multfile.getInputStream();
                 BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, "utf-8"))) {
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    if (line.trim().isEmpty()) {
                        System.out.println("\t");
                    } else {
                        String trimmedLine = line.trim();
                        String mac1 = "无";
                        String mac2 = "无";
                        DemoMacQrcodeAo map1 = demoMacQrcodeService.findMac(trimmedLine.trim());
                        if (map1 != null) {
                            mac1 = map1.getMac();
                        }

                        System.out.println(mac1);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return ApiResult.ok();
    }

    /**
     * @desc: 读取文本批量查询二维码
     * @author: DH
     * @date: 2022/3/9 15:21
     */
    @PostMapping(value = "/readerTxtCode")
    public ApiResult readerTxtCode(@RequestParam(required = false) List<MultipartFile> files) {
        for (MultipartFile multfile : files) {
            try (InputStream inputStream = multfile.getInputStream();
                 BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, "utf-8"))) {
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    if (line.trim().isEmpty()) {
                        System.out.println("\t");
                    } else {
                        String trimmedLine = line.trim();
                        String mac1 = "无";
                        String mac2 = "无";
                        DemoMacQrcodeAo map1 = demoMacQrcodeService.findByMac(trimmedLine.substring(0, 17).trim());
                        DemoMacQrcodeAo map2 = demoMacQrcodeService.findByMac(trimmedLine.substring(17).trim());
                        if (map1 != null) {
                            mac1 = map1.getQrCode();
                            //hardwareService.update(new LambdaUpdateWrapper<Hardware>().eq(Hardware::getMac,map1.getMac()).set(Hardware::getUnbind,true).set(Hardware::getUpdateTime,new Date()));
                            demoMacQrcodeService.remove(new LambdaQueryWrapper<DemoMacQrcode>().eq(DemoMacQrcode::getMac,map1.getMac()));
                        }
                        if (map2 != null) {
                            mac2 = map2.getQrCode();
                        }
                        System.out.println(mac1 + "\t" + mac2);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return ApiResult.ok();
    }
}

