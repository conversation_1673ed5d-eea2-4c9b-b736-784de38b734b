package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.using.entity.FieldGroup;
import io.geekidea.springbootplus.using.service.FieldGroupService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.FieldGroupPageParam;
import io.geekidea.springbootplus.framework.common.controller.BaseController;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.common.param.IdParam;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Slf4j
@RestController
@RequestMapping("/fieldGroup")
@Module("${cfg.module}")
public class FieldGroupController extends BaseController {

    @Autowired
    private FieldGroupService fieldGroupService;

    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class)
    public ApiResult<Boolean> addFieldGroup(@Validated(Add.class) @RequestBody FieldGroup fieldGroup) throws Exception {
        boolean flag = fieldGroupService.saveFieldGroup(fieldGroup);
        return ApiResult.result(flag);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    public ApiResult<Boolean> updateFieldGroup(@Validated(Update.class) @RequestBody FieldGroup fieldGroup) throws Exception {
        boolean flag = fieldGroupService.updateFieldGroup(fieldGroup);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteFieldGroup(@PathVariable("id") Long id) throws Exception {
        boolean flag = fieldGroupService.deleteFieldGroup(id);
        return ApiResult.result(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = FieldGroup.class)
    public ApiResult<FieldGroup> getFieldGroup(@PathVariable("id") Long id) throws Exception {
        FieldGroup fieldGroup = fieldGroupService.getById(id);
        return ApiResult.ok(fieldGroup);
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = FieldGroup.class)
    public ApiResult<Paging<FieldGroup>> getFieldGroupPageList(@Validated @RequestBody FieldGroupPageParam fieldGroupPageParam) throws Exception {
        Paging<FieldGroup> paging = fieldGroupService.getFieldGroupPageList(fieldGroupPageParam);
        return ApiResult.ok(paging);
    }

}

