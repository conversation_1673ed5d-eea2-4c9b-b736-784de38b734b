package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.aop.annotation.TextCensorUserDefined;
import io.geekidea.springbootplus.using.entity.TeamApp;
import io.geekidea.springbootplus.using.service.TeamAppService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.TeamAppPageParam;
import io.geekidea.springbootplus.framework.common.controller.BaseController;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.common.param.IdParam;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Slf4j
@RestController
@RequestMapping("/using/teamApp")
@Module("${cfg.module}")
@Api(value = "APP球队模块", tags = {"APP球队模块"})
public class TeamAppController extends BaseController {

    @Autowired
    private TeamAppService teamAppService;

    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = Long.class)
    @TextCensorUserDefined
    public ApiResult<Long> addTeamApp(@Validated(Add.class) @RequestBody TeamApp teamApp) throws Exception {
        boolean flag = teamAppService.saveTeamApp(teamApp);
        return ApiResult.ok(teamApp.getId());
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    @TextCensorUserDefined
    public ApiResult<Boolean> updateTeamApp(@Validated(Update.class) @RequestBody TeamApp teamApp) throws Exception {
        boolean flag = teamAppService.updateTeamApp(teamApp);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteTeamApp(@PathVariable("id") Long id) throws Exception {
        boolean flag = teamAppService.deleteTeamApp(id);
        return ApiResult.result(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = TeamApp.class, notes = "包含球员数量、赛事总场次、胜负平场次等统计信息")
    public ApiResult<TeamApp> getTeamApp(@PathVariable("id") Long id) throws Exception {
        TeamApp teamApp = teamAppService.getTeamAppWithStats(id);
        return ApiResult.ok(teamApp);
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = TeamApp.class)
    public ApiResult<List<TeamApp>> getTeamAppPageList(@Validated @RequestBody TeamAppPageParam teamAppPageParam) throws Exception {
        Paging<TeamApp> paging = teamAppService.getTeamAppPageList(teamAppPageParam);
        return ApiResult.ok(paging.getRecords());
    }

    @GetMapping("/startedCount/{teamId}/{app}")
    @OperationLog(name = "班级中 是否有进行中状态的比赛", type = OperationLogType.PAGE)
    @ApiOperation(value = "班级中 是否有进行中状态的比赛", response = ApiResult.class,notes = "参数app是否查询app,data的值是 boolean值")
    public ApiResult startedCount(@PathVariable("teamId") Long teamId,@PathVariable("app") Boolean app) throws Exception {
        return ApiResult.ok(teamAppService.startedCount("GAME",teamId,app));

    }

}

