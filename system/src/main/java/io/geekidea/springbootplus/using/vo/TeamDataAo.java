package io.geekidea.springbootplus.using.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import io.geekidea.springbootplus.using.entity.Drill;
import io.geekidea.springbootplus.using.entity.Game;
import io.geekidea.springbootplus.using.entity.KindergartenMonitor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(autoResultMap = true)
public class TeamDataAo {
    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 球赛id
     */
    private Long matchId;

    @ApiModelProperty("球赛类型  DRILL:训练 GAME:比赛")
    private String matchType;

    /**
     * 训练信息
     */
    private Drill drillInfo;

    /**
     * 训练信息
     */
    private Game gameInfo;

    /**
     * 监测信息
     */
    private KindergartenMonitor kindergartenMonitor;

    /**
     * 团队曲线
     */
    private JSON curves;

    /**
     * 运动时间（秒）
     */
    private long exerciseTime;

    /**
     * MVP
     */
    private JSONObject mvp;

    /**
     * 总跑动 (m)
     */
    private long sumRun;

    /**
     * 总卡路里
     */
    private Integer sumCalorie;

    /**
     * 总跑动最多学生
     */
    private String runBestStu;

    /**
     * 总跑动最多学生的跑动值（m）
     */
    private long bestStuRun;

    /**
     * 缺勤人数
     */
    private int absent;

    /**
     * 同步数据人数
     */
    private int uploaded;

    /**
     * 未同步数据人数
     */
    private int noUploaded;

    /**
     * 启动设备人数
     */
    private Long hardware;

    /**
     * 历史平均消耗
     */
    private double avgConsume;

    /**
     * 速度数据
     */
   private Map<String, Object> speedDataMap;


    /**
     * app查询者本人是否同步 （APP用）
     */
    private boolean isUpload = false;

    /**
     * 球队logo
     */
    private String logo;

    /**
     * 学校log
     */
    private String schoolLogo;

    /**
     * 学校Name
     */
    private String schoolName;

    /**
     * 学校公众号
     */
    private String schoolPub;

    /**
     * 球队全称
     */
    private String fullName;

    /**
     * 球队简称
     */
    private String shortName;

    // 记时/计数/场记 数据
    JSONObject extra;

    /**
     * 步伐
     */
    private JSONObject pace;

    private JSONObject runningForm;

    private JSONObject touchdownWay;

    /**
     * 总步数
     */
    private Integer sumStep;

    private Integer jumpAvgHeight;

    private Integer maxDuration;

    private Integer avgDuration;

    private Integer maxTakeOffDistance;

    private Integer maxTakeOffHeight;

    private JSONArray runDistances = new JSONArray();

    private JSONArray maxSprintSpeeds = new JSONArray();

    private JSONArray overallSpeeds = new JSONArray();

    private JSONArray  overallSpeedCounts = new JSONArray();

    private JSONArray leftOrRightTurnings = new JSONArray();

    private JSONArray startingsOrBrakes = new JSONArray();

    private JSONArray breakOuts = new JSONArray();

    /**
     * 深度数据
     */
    private JSONObject teamAvgDepthData;

    private JSONObject teamAvgOverallSpeed;

    private JSONObject teamAvgOverallSpeedCount;

    /**
     * 最大冲刺速度(km/h)
     */
    private Double teamAvgMaxSprintSpeed;

}
