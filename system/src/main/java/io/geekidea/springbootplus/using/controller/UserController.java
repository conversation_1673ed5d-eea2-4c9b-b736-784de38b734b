package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.constant.CacheConsts;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.util.EmailUtil;
import io.geekidea.springbootplus.framework.util.MobileUtils;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.entity.TeacherInfo;
import io.geekidea.springbootplus.using.entity.User;
import io.geekidea.springbootplus.using.param.UpdateBigUrlParam;
import io.geekidea.springbootplus.using.service.TeacherInfoService;
import io.geekidea.springbootplus.using.service.UserAppService;
import io.geekidea.springbootplus.using.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/using/user")
@Module("teambox")
@Api(value = "用户相关", tags = {"用户相关"})
public class UserController extends BaseController{
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private MobileUtils mobileUtils;
    @Autowired
    private EmailUtil emailUtil;
    @Autowired
    private UserService userService;
    @Autowired
    private UserAppService userAppService;
    @Autowired
    private TeacherInfoService teacherInfoService;

    @PostMapping("/writeOff")
    @OperationLog(name = "注销 pad端使用")
    @ApiOperation(value = "注销 pad端使用", response = ApiResult.class ,notes = "")
    public ApiResult writeOff() {
        User user = getCurrent();
        //修改用户名 逻辑删除
        Boolean b = userService.logoutUser(user);
        if (b) {
            delCurrent();
        }
        return ApiResult.ok(b);
    }

    @PostMapping("/updateTeacher")
    @OperationLog(name = "修改老师信息")
    @ApiOperation(value = "修改老师信息", response = ApiResult.class ,notes = "修改头像或者图片信息 先调用 文件上传Using模块的接口获取url再提交")
    public ApiResult updateTeacher(@RequestBody TeacherInfo teacher) {
        teacherInfoService.saveOrUpdate(teacher);
        return ApiResult.ok(teacher);
    }

    @PostMapping("/updateTeacherBigUrl/{id}")
    @OperationLog(name = "修改老师大图URL")
    @ApiOperation(value = "修改老师大图URL", response = ApiResult.class, notes = "根据老师id修改bigUrl字段")
    public ApiResult updateTeacherBigUrl(@RequestBody TeacherInfo teacher) {
        try {
            boolean flag = teacherInfoService.updateBigUrl(teacher.getId(), teacher.getBigUrl());
            return ApiResult.result(flag);
        } catch (Exception e) {
            log.error("修改老师大图URL失败", e);
            return ApiResult.fail("修改失败");
        }
    }

    @PostMapping("/sendPhoneVerificationCode/{phoneNumber}")
    @OperationLog(name = "用户手机发送验证")
    @ApiOperation(value = "用户手机发送验证", response = ApiResult.class ,notes = "--返回参数参考pad协议 --参考pad协议 --参考pad协议" +
            "错误code")
    public ApiResult sendPhoneVerificationCode(@PathVariable("phoneNumber")String phoneNumber) {

        String b = userService.sendPhoneVerificationCode(phoneNumber);
        if (b.equals("0000")) {
            return ApiResult.ok(redisUtils.get(CacheConsts.MOBILE_PHONE_MESSAGE_PREFIX + phoneNumber));
        } else if (b.equals("-1")) {
            return ApiResult.result(ApiCode.PHONE_CODE_SEND_ERROR);
        } else if (b.equals("20016")) {
            return ApiResult.result(ApiCode.SMS_ASTRICT_MINUTE);
        } else if (b.equals("20017")) {
            return ApiResult.result(ApiCode.SMS_ASTRICT_HOUR);
        } else if (b.equals("20018")) {
            return ApiResult.result(ApiCode.SMS_ASTRICT_DAY);
        } else {
            return ApiResult.result(ApiCode.PHONE_CODE_SEND_ERROR);
        }
    }

    /**
     * @desc: 验证手机验证码
     * @author: DH
     * @date: 2020/10/22 16:07
     */
    @PostMapping("/checkVerificationCode")
    @OperationLog(name = "验证手机验证码 ")
    @ApiOperation(value = "验证手机验证码", response = ApiResult.class ,notes = "--返回参数参考pad协议 --参考pad协议 --参考pad协议" +
            "错误code 20006：验证码错误 20009：验证码过期")
    public ApiResult checkVerificationCode(@RequestBody JSONObject paramJson) {
        String verificationCode = paramJson.getString("verificationCode");
        String phone = paramJson.getString("phone");
        ApiCode checkVerificationCode = userService.checkVerificationCode(phone, verificationCode,false);
        if (checkVerificationCode != null) {
            return ApiResult.result(checkVerificationCode);
        }
        return ApiResult.ok();
    }

    @PostMapping("/checkPhoneExist/{phoneNumber}")
    @OperationLog(name = "验证手机是否存在 ")
    @ApiOperation(value = "验证手机是否存在", response = ApiResult.class ,notes = "data: true:存在,false:不存在")
    public ApiResult checkPhoneExist(@PathVariable("phoneNumber")String phoneNumber) {
        return ApiResult.ok(userAppService.checkPhoneExist(phoneNumber));
    }


/*
    @PostMapping("/checkPhoneExist/{phoneNumber}")
    @OperationLog(name = "验证手机是否存在 (手机端)")
    @ApiOperation(value = "验证手机是否存在 (手机端)", response = ApiResult.class ,notes = "data: true:存在,false:不存在")
    public ApiResult checkPhoneExist(@PathVariable("phoneNumber")String phoneNumber) {
        return ApiResult.ok(userService.checkPhoneExist(phoneNumber));
    }*/
}
