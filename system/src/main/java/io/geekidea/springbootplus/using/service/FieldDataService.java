package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.geekidea.springbootplus.using.entity.FieldData;
import io.geekidea.springbootplus.using.param.FieldDataPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
public interface FieldDataService extends BaseService<FieldData> {

    /**
     * 保存
     *
     * @param fieldData
     * @return
     * @throws Exception
     */
    boolean saveFieldData(FieldData fieldData) throws Exception;

    /**
     * 修改
     *
     * @param fieldData
     * @return
     * @throws Exception
     */
    boolean updateFieldData(FieldData fieldData) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteFieldData(Long id) throws Exception;


    boolean deleteByApp(Long fieldId,Long groupId,Long stuId);

    /**
     * 获取分页对象
     *
     * @param fieldDataQueryParam
     * @return
     * @throws Exception
     */
    Paging<FieldData> getFieldDataPageList(FieldDataPageParam fieldDataPageParam) throws Exception;

    /**
     * 查询场记学员
     * @param groupId
     * @param fieldId
     * @return
     */
    List<FieldData> getStuByGroupIdAndFieldId(Long groupId,Long fieldId);

//    /**
//     * 查看单个学生的场记数据
//     * @param fieldId
//     * @param stuId
//     * @return
//     */
//    FieldData getByStuId(Long fieldId,Long stuId);


//    /**
//     * 计算小组的场记数据
//     * @param fieldId
//     * @return
//     */
//    FieldData getDataList(Long fieldId);

    /**
     * 个人场记列表
     * @param fieldDataPageParam
     * @return
     */
    List<FieldData> getStuField(FieldDataPageParam fieldDataPageParam,Long stuId,Long drillId);

    /**
     * 个人场记列表比赛
     * @param fieldDataPageParam
     * @return
     */
    List<FieldData> getStuField(FieldDataPageParam fieldDataPageParam);

    /**
     * 个人场记数据
     * @param stuId
     * @param fieldId
     * @param groupId
     * @return
     */
    FieldData findListByIds(Long stuId,Long fieldId,Long teamId);

    FieldData findListByIdsGame(Long stuId,Long fieldId,Long groupId);

    /**
     * 个人最新篮球数据
     * @param stuId
     * @param drillId
     * @return
     */
    Object getPersonal(Long stuId,Long drillId);

    /**
     * 个人最新篮球数据 比赛
     * @param stuId
     * @param gameId
     * @return
     */
    Object getPersonalGame(Long stuId,Long gameId);

    /**
     * 团队最新篮球数据
     * @param teamId
     * @param drillId
     * @return
     */
    Object getTeam(Long teamId,Long drillId);

    /**
     * 团队最新篮球数据 比赛
     * @param teamId
     * @param gameId
     * @return
     */
    Object getTeamGame(Long teamId, Long gameId);

    FieldDataPageParam createFieldDataPageParam(Long userId,String matchType, Long matchId, Long pageIndex, Long pageSize);

    Object goalField(JSONObject jsonObject,Long fieldId);

    List<FieldData> getGroupIdAndStuId(Long fnId);

}
