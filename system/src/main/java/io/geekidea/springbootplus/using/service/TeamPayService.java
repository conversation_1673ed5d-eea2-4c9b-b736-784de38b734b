package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TeamPay;
import io.geekidea.springbootplus.using.param.FacilityGroupPayPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface TeamPayService extends BaseService<TeamPay> {

    /**
     * 保存
     *
     * @param teamPay
     * @return
     * @throws Exception
     */
    boolean saveFacilityGroupPay(TeamPay teamPay) throws Exception;

    /**
     * 修改
     *
     * @param teamPay
     * @return
     * @throws Exception
     */
    boolean updateFacilityGroupPay(TeamPay teamPay) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteFacilityGroupPay(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param facilityGroupPayQueryParam
     * @return
     * @throws Exception
     */
    Paging<TeamPay> getFacilityGroupPayPageList(FacilityGroupPayPageParam facilityGroupPayPageParam) throws Exception;

}
