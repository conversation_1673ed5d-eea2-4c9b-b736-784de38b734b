package io.geekidea.springbootplus.using.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.geekidea.springbootplus.using.entity.UserApp;
import io.geekidea.springbootplus.using.param.UserAppPageParam;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.io.Serializable;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Repository
public interface UserAppMapper extends BaseMapper<UserApp> {

    UserApp getById(@Param("id") Long id);
}
