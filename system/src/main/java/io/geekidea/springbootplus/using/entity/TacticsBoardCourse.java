package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 战术板综合信息表
 *
 * <AUTHOR>
 * @since 2025-03-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TacticsBoardCourse对象")
@TableName(autoResultMap = true)
public class TacticsBoardCourse extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("战术板主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("课程名")
    private String courseName;

    @ApiModelProperty("课件训练时长（每分钟）")
    private Integer duration;

    @ApiModelProperty("战术板关联id")
    private Long tacticsId;

    @ApiModelProperty("几人制")
    private Integer people;

    @ApiModelProperty("内容")
    private String contentText;

    @ApiModelProperty("课件使用年龄（1:4-6,  2:7-12,  3:13-15,  4:16-18,  5:18以上）")
    private Integer age;

    @ApiModelProperty("战术分类（1：进攻2：防守 3：任意球 4：角球）")
    private Integer action;

    @ApiModelProperty("难度（1:1星 2:2星 3:3星）")
    private Integer difficulty;

    @ApiModelProperty("战术板内容")
    @TableField(exist = false)
    private TacticsBoard tacticsBoard;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @ApiModelProperty("是否收藏 false-未收藏 true-已收藏")
    @TableField(exist = false)
    private Boolean isFavorite;

}