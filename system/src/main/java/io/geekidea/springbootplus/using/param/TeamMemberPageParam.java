package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 *  球队成员分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TeamMemberPageParam球队成员分页参数")
public class TeamMemberPageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("球队ID")
    private Long teamId;

    @ApiModelProperty("性别筛选：1-男，2-女")
    private Integer sex;

    @ApiModelProperty("是否队长筛选")
    private Boolean isCaptain;
}
