package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.service.DemoMacQrcodeService;
import io.geekidea.springbootplus.using.service.HardwareAppService;
import io.geekidea.springbootplus.using.service.HardwareService;
import io.geekidea.springbootplus.using.vo.DemoMacQrcodeAo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Random;

@RestController
@RequestMapping("/using/test")
public class TestController {
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    DemoMacQrcodeService demoMacQrcodeService;
    @Autowired
    HardwareService hardwareService;
    @Autowired
    HardwareAppService hardwareAppService;

    @PostMapping("/test")
    public ApiResult<Boolean> test() {
        demoMacQrcodeService.test();
        hardwareService.test();
        hardwareAppService.test();
        return ApiResult.ok();
    }
}
