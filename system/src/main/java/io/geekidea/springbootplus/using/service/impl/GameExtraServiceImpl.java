package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.mapper.GameExtraMapper;
import io.geekidea.springbootplus.using.param.DrillExtraPageParam;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.param.GameExtraPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.vo.GameExtraUserAo;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Slf4j
@Service
public class GameExtraServiceImpl extends BaseServiceImpl<GameExtraMapper, GameExtra> implements GameExtraService {

    @Autowired
    private GameExtraMapper gameExtraMapper;
    @Autowired
    private GameExtraGroupService gameExtraGroupService;
    @Autowired
    private GameService gameService;
    @Autowired
    private UserAppService userAppService;
    @Autowired
    private TeamAppService teamAppService;
    @Autowired
    private TeamUserAppService teamUserAppService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private FieldNotesService fieldNotesService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveGameExtra(GameExtra gameExtra) throws Exception {
        super.saveOrUpdate(gameExtra);
        return gameExtra.getId();
    }

    @Override
    @Transactional
    public boolean copyGameExtra(Long gameExtraId) throws Exception {
        GameExtra gameExtra = getById(gameExtraId);
        List<GameExtraGroup> gameExtraGroups = gameExtraGroupService.getGameExtraGroup(gameExtra.getId());
        gameExtra.setId(null);
        gameExtra.setFinish(false);
        saveOrUpdate(gameExtra);
        gameExtraGroups.forEach(e -> {
            e.setId(null);
            e.setGameExtraId(gameExtra.getId());
            JSONArray users = e.getUsers();
            JSONArray users1 = new JSONArray();
            users.forEach(s -> {
                JSONObject o = (JSONObject) s;
                JSONObject o1 = new JSONObject();
                o1.put("id", o.getIntValue("id"));
                users1.add(o1);
            });
            e.setUsers(users1);
        });
        return gameExtraGroupService.saveOrUpdateBatch(gameExtraGroups);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateGameExtra(GameExtra gameExtra) throws Exception {
        return super.updateById(gameExtra);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteGameExtra(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<GameExtra> getGameExtraPageList(GameExtraPageParam gameExtraPageParam) throws Exception {
        Page<GameExtra> page = new PageInfo<>(gameExtraPageParam, OrderItem.desc(getLambdaColumn(GameExtra::getCreateTime)));
        LambdaQueryWrapper<GameExtra> wrapper = new LambdaQueryWrapper<>();
        IPage<GameExtra> iPage = gameExtraMapper.selectPage(page, wrapper);
        return new Paging<GameExtra>(iPage);
    }

    @Override
    public List<GameExtra> getGameExtraByGameId(GameExtraPageParam gameExtraPageParam) throws Exception {
        Page<GameExtra> page = new PageInfo<>(gameExtraPageParam, OrderItem.desc(getLambdaColumn(GameExtra::getCreateTime)));
        IPage<GameExtra> gameExtraIPage = getBaseMapper().selectPage(page, new LambdaQueryWrapper<GameExtra>()
                .eq(GameExtra::getGameId, gameExtraPageParam.getGameId())
                .eq(gameExtraPageParam.getFinish() != 2, GameExtra::getFinish, gameExtraPageParam.getFinish())
                .eq(gameExtraPageParam.getType() != -1, GameExtra::getType, gameExtraPageParam.getType()));

        List<GameExtra> gameExtras = gameExtraIPage.getRecords();
        if (gameExtras.size() == 0) {
            return new ArrayList<>();
        }

        Game game = gameService.getById(gameExtraPageParam.getGameId());
        List<UserApp> userApps = userAppService.getByTeamId(gameExtraPageParam.getTeamId());

        Map<Long, UserApp> stuMap = userApps.stream().collect(Collectors.toMap(UserApp::getId, Function.identity(), (key1, key2) -> key2));

        if(game.getApp()) {
            gameExtras.forEach(e -> {
                e.setGroup(gameExtraGroupService.getGameExtraGroup(e.getId()));
                List<GameExtraGroup> groups = e.getGroup();
                JSONArray allStu = new JSONArray();
                groups.forEach(g -> {
                    JSONArray stus = g.getUsers();
                    stus.forEach(stuO -> {
                        JSONObject j = (JSONObject) stuO;
                        UserApp s = stuMap.get(j.getLong("id"));
                        if (s != null) {
                            j.put("name", s.getName());
                            j.put("headImg", s.getHeadImg());
                        }else{
                            s = userAppService.getById(j.getLong("id"));
                            j.put("name", s.getName());
                            j.put("headImg", s.getHeadImg());
                        }
                    });
                    allStu.addAll(stus);
                });
                groupOrderBy(e.getType(), allStu);
                e.setGroupAll(allStu);
            });
        }
        return gameExtras;
    }

    @Override
    public JSONObject getGameExtrasCount(Long teamId,Long gameId, Boolean prot) {
        Game game = gameService.getById(gameId);
        JSONObject o = new JSONObject();
        o.put("1", count(new LambdaQueryWrapper<GameExtra>().eq(GameExtra::getGameId, gameId).eq(GameExtra::getType, 1)));
        o.put("2", count(new LambdaQueryWrapper<GameExtra>().eq(GameExtra::getGameId, gameId).eq(GameExtra::getType, 2)));
        List<FieldNotes> fieldNotes = fieldNotesService.list(new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType,"GAME").eq(FieldNotes::getMatchId, gameId));
        o.put("3", fieldNotes.size());
        if (prot) {
            Long runningTime = 0l;
            if(game.getFinishStartTime() != null){
                runningTime = System.currentTimeMillis() - game.getFinishStartTime().getTime();
            }
            o.put("4", game.getCourseId());
            o.put("5", runningTime);
            TeamApp team = teamAppService.getById(teamId);
            if(team!=null) {
                o.put("6", team.getShortName());
            }
        }
        if(fieldNotes.size()>0){
            o.put("7", fieldNotes.get(fieldNotes.size()-1).getId());
        }else{
            o.put("7", -1);
        }
        return o;
    }

    @Override
    public GameExtra getGameExtra(Long teamId,Long gameExtraId) {
        GameExtra gameExtra = getById(gameExtraId);
        if (gameExtra == null) {
            return null;
        }
        Game game = gameService.getById(gameExtra.getGameId());
        List<UserApp> userApps = userAppService.getByTeamId(teamId);
        Map<Long, UserApp> stuMap = userApps.stream().collect(Collectors.toMap(UserApp::getId, Function.identity(), (key1, key2) -> key2));

        gameExtra.setGroup(gameExtraGroupService.getGameExtraGroup(gameExtra.getId()));
        List<GameExtraGroup> groups = gameExtra.getGroup();
        JSONArray allStu = new JSONArray();
        groups.forEach(g -> {
            JSONArray stus = g.getUsers();
            stus.forEach(stuO -> {
                JSONObject j = (JSONObject) stuO;
                UserApp s = stuMap.get(j.getLong("id"));
                if (s != null) {
                    j.put("name", s.getName());
                    j.put("headImg", s.getHeadImg());
                }else{
                    s = userAppService.getById(j.getLong("id"));
                    j.put("name", s.getName());
                    j.put("headImg", s.getHeadImg());
                }
            });
            allStu.addAll(stus);
        });
        groupOrderBy(gameExtra.getType(), allStu);
        gameExtra.setGroupAll(allStu);
        gameExtra.setDuration(game.getDuration());
        Long now = System.currentTimeMillis();
        if (game.getFinishStartTime() != null) {
            Long runningTime = now - game.getFinishStartTime().getTime();
            gameExtra.setRunningTime(runningTime);
        } else {
            gameExtra.setRunningTime(0l);
        }
        return gameExtra;
    }

    @Override
    public List<GameExtraUserAo> getGameExtraByUser(GameExtraPageParam gameExtraPageParam) {
        Page<GameExtra> page = new PageInfo<>(gameExtraPageParam, OrderItem.desc(getLambdaColumn(GameExtra::getCreateTime)));
        List<GameExtra> gameExtras =
                getBaseMapper().selectPage(page, new LambdaQueryWrapper<GameExtra>()
                        .eq(GameExtra::getGameId, gameExtraPageParam.getGameId())
                        .eq(gameExtraPageParam.getFinish().intValue() != 2, GameExtra::getFinish, gameExtraPageParam.getFinish())
                        .eq(gameExtraPageParam.getType().intValue() != -1, GameExtra::getType, gameExtraPageParam.getType())
                        .orderByDesc(GameExtra::getUpdateTime))
                        .getRecords();
        Game game = gameService.getById(gameExtraPageParam.getGameId());
        if(!game.getApp()) {
            gameExtras.forEach(e -> {
                e.setGroup(gameExtraGroupService.getGameExtraGroup(e.getId()));
                List<GameExtraGroup> groups = e.getGroup();
                JSONArray allStu = new JSONArray();
                groups.forEach(g -> {
                    JSONArray stus = g.getUsers();
                    allStu.addAll(stus);
                });
                groupOrderBy(e.getType(), allStu);
                e.setGroupAll(allStu);
            });
        }
        List<GameExtraUserAo> gameExtraUserAos = new ArrayList<>();
        gameExtras.forEach(e -> {
            if (!gameExtraPageParam.isAstrict() || (gameExtraPageParam.isAstrict() && gameExtraUserAos.size() < 3)) {
                GameExtraUserAo gameExtraStudentAo = new GameExtraUserAo();
                gameExtraStudentAo.setId(e.getId());
                gameExtraStudentAo.setName(e.getName());
                gameExtraStudentAo.setType(e.getType());
                gameExtraStudentAo.setIconUrl(e.getIconUrl());
                gameExtraStudentAo.setCreateDate(e.getCreateTime());
                gameExtraStudentAo.setUpdateDate(e.getUpdateTime());
                JSONArray jsonArray = e.getGroupAll();
                if (!game.getApp()) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        if (jsonArray.getJSONObject(i).getLongValue("id") == gameExtraPageParam.getUserId().longValue()) {
                            if (e.getType() != 1) {
                                gameExtraStudentAo.setCount(jsonArray.getJSONObject(i).getIntValue("count"));
                            } else {
                                gameExtraStudentAo.setStartTime(jsonArray.getJSONObject(i).getLongValue("startTime"));
                                gameExtraStudentAo.setStopTime(jsonArray.getJSONObject(i).getLongValue("stopTime"));
                            }
                            gameExtraStudentAo.setRanking(i + 1);
                            gameExtraUserAos.add(gameExtraStudentAo);
                            break;
                        }
                    }
                }else{
                    gameExtraStudentAo.setCommonValue(e.getCommonValue());
                    gameExtraStudentAo.setRanking(1);
                    gameExtraUserAos.add(gameExtraStudentAo);
                }
            }
        });
        return gameExtraUserAos;
    }

    @Override
    public GameExtraPageParam createGameExtraPageParam(Long gameId, Long studentId, Integer finish, Integer type, Boolean astrict, Long pi, Long ps) {
        GameExtraPageParam gameExtraPageParam = new GameExtraPageParam();
        gameExtraPageParam.setUserId(studentId);
        gameExtraPageParam.setGameId(gameId);
        gameExtraPageParam.setFinish(finish);
        gameExtraPageParam.setType(type);
        gameExtraPageParam.setAstrict(astrict);
        gameExtraPageParam.setPageIndex(pi);
        gameExtraPageParam.setPageSize(ps);
        return gameExtraPageParam;
    }

    public void groupOrderBy(Integer type, JSONArray group) {
        group.sort((o1, o2) -> {
            JSONObject v1 = JSONObject.parseObject(JSON.toJSONString(o1));
            JSONObject v2 = JSONObject.parseObject(JSON.toJSONString(o2));
            Long l1 = 0l;
            Long l2 = 0l;
            if (type == 1) {
                if (v1.getLong("stopTime") != null) {
                    l1 = v1.getLong("stopTime") - v1.getLong("startTime");
                    l2 = v2.getLong("stopTime") - v2.getLong("startTime");
                }
                return Long.compare(l1, l2);
            } else {
                if (v1.getLong("count") != null) {
                    l1 = v1.getLong("count");
                    l2 = v2.getLong("count");
                }
                return Long.compare(l2, l1);
            }
        });
    }
}
