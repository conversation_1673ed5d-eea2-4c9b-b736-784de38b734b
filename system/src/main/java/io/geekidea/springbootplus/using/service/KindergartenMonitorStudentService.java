package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.KindergartenMonitor;
import io.geekidea.springbootplus.using.entity.KindergartenMonitorStudent;
import io.geekidea.springbootplus.using.param.KindergartenMonitorStudentPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
public interface KindergartenMonitorStudentService extends BaseService<KindergartenMonitorStudent> {

    /**
     * 保存
     *
     * @param kindergartenMonitorStudent
     * @return
     * @throws Exception
     */
    boolean saveKindergartenMonitorStudent(KindergartenMonitorStudent kindergartenMonitorStudent) throws Exception;

    /**
     * 修改
     *
     * @param kindergartenMonitorStudent
     * @return
     * @throws Exception
     */
    boolean updateKindergartenMonitorStudent(KindergartenMonitorStudent kindergartenMonitorStudent) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteKindergartenMonitorStudent(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param kindergartenMonitorStudentQueryParam
     * @return
     * @throws Exception
     */
    Paging<KindergartenMonitorStudent> getKindergartenMonitorStudentPageList(KindergartenMonitorStudentPageParam kindergartenMonitorStudentPageParam) throws Exception;

    KindergartenMonitorStudent getOneByMonitorIdAndStudentId(Long monitorId,Long studentId);

    List<KindergartenMonitorStudent> getList(Long monitorId, Long teamId);


    boolean setMoitorCancel(List<String> macs);

    long cancelCount(Long monitorId,Long teamId);
}
