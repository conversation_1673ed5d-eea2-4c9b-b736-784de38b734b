package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.PersonData;
import io.geekidea.springbootplus.using.param.PersonDataPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface PersonDataService extends BaseService<PersonData> {

    /**
     * 保存
     *
     * @param personData
     * @return
     * @throws Exception
     */
    boolean savePersonData(PersonData personData) throws Exception;

    /**
     * 修改
     *
     * @param personData
     * @return
     * @throws Exception
     */
    boolean updatePersonData(PersonData personData) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deletePersonData(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param personDataQueryParam
     * @return
     * @throws Exception
     */
    Paging<PersonData> getPersonDataPageList(PersonDataPageParam personDataPageParam) throws Exception;

    PersonData findOfMatchIdAndStudentId(String matchType,Long matchId,Long studentId);

    List<PersonData> findOfMatchId(Long matchId,Long teamId,String matchType);

    List<PersonData> findOfMatchIdByHist(Long teamId,Long matchId,String matchType);

    List<PersonData> findOfMatchIdByHistStu(Long stuId,Long matchId,String matchType);

    List<String> exerciseTime(List<Long> teamIds,Long studentId, String start, String stop,String matchType);

    List<ExerciseConditionAo> findByMonth(Long teamId, String year, String month);

    List<ExerciseConditionAo> findByMonthStu(Long stuId, String year, String month);

    List<ExerciseConditionAo> findByDay(Long teamId, String year, String month,String day);

    List<ExerciseConditionAo> findByDayStu(Long studentId, String year, String month, String day);

    List<ExerciseConditionAo> findByMonitor(Long monitorId);

    List<ExerciseConditionAo> findByMonitor(Long teamId,String startTime,String stopTime);

    Object getMotionSteps(Long teamId,String createTime,String matchType);

    Object teamSteps(Long teamId,String startTime,String stopTime);

     Object getDailyData(Long teamId, String date);

     List<PersonData> getDailyDataPersonDataCollect(Long teamId, String date);

    List<Map<String,Object>> findSynthesizeCurve(Long teamId, String start, String stop,String matchType);

    Date getEcentData(Long teamId,String matchType);

    List<StuPersonDataAo> runList(Long teamId,String data,String matchType);

    List<StuPersonDataAo> heightList(Long teamId,String data,String matchType);

    List<StuPersonDataAo> runteamList(Long teamId,Long matchId,String matchType);

    List<StuPersonDataAo> heightteamList(Long teamId,Long drillId,String matchType);

    StuPersonDataAo sumCalculation(Long drillId,String matchType);

    StuPersonDataAo allRunList(Long stuId,String data);

    List<Long> getUploadIdsByStu(String matchType,Long studentId);

    StuPersonDataAo getMotionByMatchId(Long teamId,Long matchId,String matchType);

    StuPersonDataAo allRunByMatchId(Long stuId,Long matchId,String matchType);

    StuPersonDataAo allHeightByMatchId(Long stuId,Long matchId,String matchType);

    List<Long> getUploadIdsByMatchIdAndTeamId(String matchType,Long matchId,Long teamId);

    boolean likeIncr(String matchType,Long matchId,Long studentId);

    int getMatchLike(String matchType, Long matchId, Long studentId);

    List<DataStatisticsAo> dataStatistics(Long teamId, Long studentId, String start,String stop, Integer type);

    List<TeamExerciseAo> sportsRanking(Long schoolId,String start, String stop, Long teamId, Integer type);

    List<DataStatisticsNewAo> dataStatisticsNew(Long teamId, Long studentId, String start, String stop,Integer type);

    List<PersonData> getCurveList(String start,String stop,Long studentId);

    List<TeamExerciseAo> personExerciseTime(String start, String stop,Long studentId, Long teamId, Integer type);

    List<TeamExerciseAo> teamExerciseTime(String start, String stop, Long teamId, Integer type);

    List<PersonColumnarAo> personColumnar(String start, String stop,Long studentId, Long teamId, Integer type);

    TeamExerciseAo teamRunCalorieSum(Long teamId,Integer type,String start,String stop);

    List<PersonData> getListNoData(String matchType,Long teamId,Long matchId);

    PersonData getArrow(Long studentId,Long matchId,String matchType);

}
