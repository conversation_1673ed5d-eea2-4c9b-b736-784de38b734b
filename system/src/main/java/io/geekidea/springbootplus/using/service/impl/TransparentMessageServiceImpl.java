package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.util.UPushUtil;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.mapper.SchoolInfoMapper;
import io.geekidea.springbootplus.using.mapper.TransparentMessageMapper;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.using.vo.SchoolPersonCountAo;
import org.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
@Slf4j
@Service
@Lazy
public class TransparentMessageServiceImpl extends BaseServiceImpl<TransparentMessageMapper, TransparentMessage> implements TransparentMessageService {

    @Autowired
    private SchoolInfoService schoolInfoService;
    @Autowired
    private TeacherInfoService teacherInfoService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private UserService userService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private DrillStudentService drillStudentService;
    @Autowired
    private DrillService drillService;
    @Autowired
    private GameStudentService gameStudentService;
    @Autowired
    private UserAppService userAppService;
    @Autowired
    private TeamUserAppService teamUserAppService;
    @Autowired
    private TeamAppService teamAppService;
    @Autowired
    UPushUtil pushUtil;



    @Override
    @Async
    public void sendSchoolAllPersonCount(Long schoolId) {
        List<Long> teacherUserIds = teacherInfoService.list(new LambdaQueryWrapper<TeacherInfo>().eq(TeacherInfo::getSchoolId, schoolId).eq(TeacherInfo::getDeleted, false)).stream().map(TeacherInfo::getUserId).collect(Collectors.toList());
        List<User> users = userService.list(new LambdaQueryWrapper<User>().eq(User::getId, teacherUserIds));
        SchoolPersonCountAo schoolPersonCountAo = schoolInfoService.getSchoolPersonCount(schoolId);
        users.forEach(user -> {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "person_count");
            message.put("deviceToken", user.getDeviceTokens());
            message.put("receiveUserId", user.getId());
            message.put("schoolId", schoolId);
            message.put("count", schoolPersonCountAo.getCount());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicast(user.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e) {
                e.printStackTrace();
            }
            saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(user.getId()).setStatus(b));
        });
    }

    @Override
    @Async
    public void sedTransparent(JSONObject message) {
        List<User> users = userService.list().stream().filter(e -> e.getDeviceTokens() != null).collect(Collectors.toList());
        List<StudentInfo> studentInfos = studentInfoService.list().stream().filter(e -> e.getDeviceTokens() != null).collect(Collectors.toList());
        users.forEach(e -> {
            boolean b = true;
            try {
                b = pushUtil.sendAndroidUnicast(e.getDeviceTokens(), message);
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        });
        studentInfos.forEach(e -> {
            boolean b = true;
            try {
                b = pushUtil.sendAndroidUnicastApp(e.getDeviceTokens(), message);
                //pushUtil.sendIOSListcast(e.getDeviceTokens(), message.toString());
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(e.getId()).setStatus(b));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        });
    }

    @Override
    @Async
    public void sendTransparentByDrillPast(Drill drill) {
        if (drill.getStudentId() != null && drill.getStudentId() != -1) { //发给学生
            StudentInfo studentInfo = studentInfoService.getStudentInfo(drill.getStudentId());
            if (studentInfo != null) {

                Map<String, Object> message = new HashMap<>();
                message.put("action", "drill_past");
                message.put("deviceToken", studentInfo.getDeviceTokens());
                message.put("receiveUserId", studentInfo.getId());
                message.put("drillId", drill.getId());
                boolean b = false;
                try {
                    b = pushUtil.sendAndroidUnicastApp(studentInfo.getDeviceTokens(), new JSONObject(message));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(studentInfo.getId()).setStatus(b));
            }
        } else {//发给教练
            Long teacherId = teamService.getById(drill.getTeamId()).getTeacherId();
            TeacherInfo teacherInfo = teacherInfoService.getById(teacherId);
            User e = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, teacherInfo.getUserId()));
            Map<String, Object> message = new HashMap<>();
            message.put("action", "drill_past");
            message.put("deviceToken", e.getDeviceTokens());
            message.put("receiveUserId", e.getId());
            message.put("drillId", drill.getId());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicast(e.getDeviceTokens(), new JSONObject(message));
            } catch (Exception er) {
                er.printStackTrace();
            }
            saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));
        }
    }

    @Override
    public void sendTransparentByGamePast(Game game) throws Exception {
        List<GameStudent> gameStudents = gameStudentService.list(new LambdaQueryWrapper<GameStudent>().eq(GameStudent::getGameId, game.getId()).eq(GameStudent::getCancel, false));
        List<Long> userIds = gameStudents.stream().map(GameStudent::getStudentId).collect(Collectors.toList());
        List<UserApp> users = userAppService.listByIds(userIds);
        users.forEach(e -> {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "game_past");
            message.put("deviceToken", e.getDeviceTokens());
            message.put("receiveUserId", e.getId());
            message.put("gameId", game.getId());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicastApp(e.getDeviceTokens(), new JSONObject(message));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(e.getId()).setStatus(b));
        });
    }

    @Override
    @Async
    public void sendTransparentByDrillCancel(Long drillId) throws Exception {
        Drill drill = drillService.getById(drillId);
        if (drill.getTeamId() != null && drill.getTeamId() != 0) {
            Long teacherId = teamService.getById(drill.getTeamId()).getTeacherId();
            TeacherInfo teacherInfo = teacherInfoService.getById(teacherId);
            User e = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, teacherInfo.getId()));
            Map<String, Object> message = new HashMap<>();
            message.put("action", "drill_cancel");
            message.put("deviceToken", e.getDeviceTokens());
            message.put("receiveUserId", e.getId());
            message.put("drillId", drill.getId());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicast(e.getDeviceTokens(), new JSONObject(message));
            } catch (Exception er) {
                er.printStackTrace();
            }
            saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));
        }
        List<DrillStudent> drillStudents = drillStudentService.getListByDrillId(drill.getId(), drill.getTeamId());
        drillStudents.forEach(e1 -> {
            StudentInfo studentInfo = studentInfoService.getById(e1.getStudentId());
            if (studentInfo != null) {
                Map<String, Object> message1 = new HashMap<>();
                message1.put("action", "drill_cancel");
                message1.put("deviceToken", studentInfo.getDeviceTokens());
                message1.put("receiveUserId", e1.getId());
                message1.put("drillId", drill.getId());
                boolean b1 = false;
                try {
                    b1 = pushUtil.sendAndroidUnicast(studentInfo.getDeviceTokens(), new JSONObject(message1));
                } catch (Exception er) {
                    er.printStackTrace();
                }
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message1))).setType("APP").setReceiveUserId(e1.getId()).setStatus(b1));
            }
        });
    }

    @Override
    @Async
    public void sendTransparentByGameCancel(Long gameId) {
        List<GameStudent> gameStudents = gameStudentService.getListByGameId(gameId);
        gameStudents.forEach(e1 -> {
            UserApp userApp = userAppService.getById(e1.getStudentId());
            if (userApp != null) {
                Map<String, Object> message1 = new HashMap<>();
                message1.put("action", "drill_cancel");
                message1.put("deviceToken", userApp.getDeviceTokens());
                message1.put("receiveUserId", e1.getId());
                message1.put("gameId", gameId);
                boolean b1 = false;
                try {
                    b1 = pushUtil.sendAndroidUnicast(userApp.getDeviceTokens(), new JSONObject(message1));
                } catch (Exception er) {
                    er.printStackTrace();
                }
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message1))).setType("APP").setReceiveUserId(e1.getId()).setStatus(b1));
            }
        });
    }

    @Override
    public void sendTransparentByHardware(Long studentId) throws Exception {
        StudentInfo studentInfo = studentInfoService.getById(studentId);
        if (studentInfo != null) {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "hardware_change");
            message.put("deviceToken", studentInfo.getDeviceTokens());
            message.put("receiveUserId", studentInfo.getId());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicastApp(studentInfo.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(studentInfo.getId()).setStatus(b));
        }
    }

    @Override
    public void sendTransparentByTeacherHardware(Hardware hardware) throws Exception {
        Long teacherId = teamService.getById(hardware.getTeamId()).getTeacherId();
        TeacherInfo teacherInfo = teacherInfoService.getById(teacherId);
        User e = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, teacherInfo.getId()));
        Map<String, Object> message = new HashMap<>();
        message.put("action", "student_hardware_change");
        message.put("deviceToken", e.getDeviceTokens());
        message.put("receiveUserId", e.getId());
        message.put("teamId", hardware.getTeamId());
        message.put("studentId", hardware.getStudentId());
        message.put("box", hardware.getBox());
        message.put("num", hardware.getNum());
        boolean b = false;
        try {
            b = pushUtil.sendAndroidUnicast(e.getDeviceTokens(), new JSONObject(message));
        } catch (Exception er) {
            er.printStackTrace();
        }
        saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));
    }

    @Override
    @Async
    public void sendTransparentByDrillUpload(List<PersonData> personDatas) {
        personDatas.forEach(e -> {
            StudentInfo studentInfo = studentInfoService.getById(e.getStudentId());
            if (studentInfo != null) {
                Map<String, Object> message = new HashMap<>();
                message.put("action", "drill_upload");
                message.put("deviceToken", studentInfo.getDeviceTokens());
                message.put("receiveUserId", studentInfo.getId());
                message.put("drillId", e.getMatchId());
                boolean b = false;
                try {
                    b = pushUtil.sendAndroidUnicastApp(studentInfo.getDeviceTokens(), new JSONObject(message));
                } catch (Exception e1) {
                    e1.printStackTrace();
                    saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(studentInfo.getId()).setStatus(b));
                }
            }
        });
    }

    @Override
    @Async
    public void sendTransparentByGameUpload(List<PersonData> personDatas) {
        personDatas.forEach(e -> {
            StudentInfo studentInfo = studentInfoService.getById(e.getStudentId());
            if (studentInfo != null) {
                Map<String, Object> message = new HashMap<>();
                message.put("action", "game_upload");
                message.put("deviceToken", studentInfo.getDeviceTokens());
                message.put("receiveUserId", studentInfo.getId());
                message.put("gameId", e.getMatchId());
                boolean b = false;
                try {
                    b = pushUtil.sendAndroidUnicastApp(studentInfo.getDeviceTokens(), new JSONObject(message));
                } catch (Exception e1) {
                    e1.printStackTrace();
                    saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(studentInfo.getId()).setStatus(b));
                }
            }
        });
    }

    @Override
    @Async
    public void sendTransparentByDrillDelete(Long drillId) {
        Drill drill = drillService.getById(drillId);
        List<DrillStudent> drillStudents = drillStudentService.getListByDrillId(drillId, drill.getTeamId());
        drillStudents.forEach(e -> {
            StudentInfo studentInfo = studentInfoService.getById(e.getStudentId());
            if (studentInfo != null) {
                Map<String, Object> message = new HashMap<>();
                message.put("action", "drill_delete");
                message.put("deviceToken", studentInfo.getDeviceTokens());
                message.put("receiveUserId", studentInfo.getId());
                boolean b = false;
                try {
                    b = pushUtil.sendAndroidUnicastApp(studentInfo.getDeviceTokens(), new JSONObject(message));
                } catch (Exception e1) {
                    e1.printStackTrace();
                    saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(studentInfo.getId()).setStatus(b));
                }
            }
        });
    }

    @Override
    @Async
    public void sendTransparentByStudentDelete(Long studentId, Long teamId) {
        StudentInfo studentInfo = studentInfoService.getById(studentId);
        if (studentInfo != null) {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "student_delete");
            message.put("deviceToken", studentInfo.getDeviceTokens());
            message.put("receiveUserId", studentInfo.getId());
            message.put("teamId", teamId);
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicastApp(studentInfo.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e1) {
                e1.printStackTrace();
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(studentInfo.getId()).setStatus(b));
            }
        }
    }

    @Override
    @Async
    public void sendTransparentByJoinTeam(Long studentId, Long teamId) {
        StudentInfo studentInfo = studentInfoService.getById(studentId);
        if (studentInfo != null) {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "join_team");
            message.put("deviceToken", studentInfo.getDeviceTokens());
            message.put("receiveUserId", studentInfo.getId());
            message.put("teamId", teamId);
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicastApp(studentInfo.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e1) {
                e1.printStackTrace();
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(studentInfo.getId()).setStatus(b));
            }
        }
    }

    @Override
    public void sendTransparentByTeacherScanQRCode(JSONObject message) {
        StudentInfo studentInfo = studentInfoService.getById(message.getLong("studentId"));
        Long teacherId = teamService.getById(studentInfo.getTeamId()).getTeacherId();
        TeacherInfo teacherInfo = teacherInfoService.getById(teacherId);
        User e = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, teacherInfo.getUserId()));
        boolean b = false;
        try {
            message.put("action", "teacher_scan_code");
            message.put("deviceToken", e.getDeviceTokens());
            message.put("receiveUserId", e.getId());
            message.put("teamId", studentInfo.getTeamId());
            message.put("studentId", studentInfo.getId());
            message.put("teacherId", teacherId);
            message.put("schoolId", teacherInfo.getSchoolId());
            b = pushUtil.sendAndroidUnicast(e.getDeviceTokens(), message);
        } catch (Exception er) {
            er.printStackTrace();
        }
        saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));
    }

    @Override
    @Async
    public void sendTransparentByTeacherTime(JSONObject message) {
        StudentInfo studentInfo = studentInfoService.getById(message.getLong("studentId"));
        Long teacherId = teamService.getById(studentInfo.getTeamId()).getTeacherId();
        TeacherInfo teacherInfo = teacherInfoService.getById(teacherId);
        User e = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, teacherInfo.getUserId()));
        message.put("action", "teacher_time");
        message.put("deviceToken", e.getDeviceTokens());
        message.put("receiveUserId", e.getId());
        message.put("teamId", studentInfo.getTeamId());
        message.put("studentId", studentInfo.getId());
        message.put("teacherId", teacherId);
        message.put("schoolId", teacherInfo.getSchoolId());
        //{action:teacher_time,deviceToken:xxx,receiveUserId:1,schoolId:学校id,teacherId:老师id, turns:圈数, type:start|stop|over,stopTime:结束时间戳}
        boolean b = false;
        try {
            b = pushUtil.sendAndroidUnicast(e.getDeviceTokens(), message);
        } catch (Exception er) {
            er.printStackTrace();
        }
        saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));
    }

    @Override
    public void sendTransparentByGroupPast(Long teamId) {
        Team team = teamService.getById(teamId);
        if(team!=null){
        TeacherInfo teacherInfo = teacherInfoService.getById(team.getTeacherId());
        User e = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, teacherInfo.getUserId()));
        if (teacherInfo != null && team != null) {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "group_past");
            message.put("deviceToken", e.getDeviceTokens());
            message.put("receiveUserId", e.getId());
            message.put("teamId", teamId);
            message.put("schoolId", teacherInfo.getSchoolId());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicast(e.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e1) {
                e1.printStackTrace();
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));
            }
        }
        }
    }

    @Override
    public void sendTransparentByGroupDelayed(Long teamId) {
        Team team = teamService.getById(teamId);
        TeacherInfo teacherInfo = teacherInfoService.getById(team.getTeacherId());
        User e = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, teacherInfo.getUserId()));
        if (teacherInfo != null && team != null) {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "group_delayed");
            message.put("deviceToken", e.getDeviceTokens());
            message.put("receiveUserId", e.getId());
            message.put("teamId", teamId);
            message.put("schoolId", teacherInfo.getSchoolId());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicast(e.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e1) {
                e1.printStackTrace();
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));
            }
        }
    }

    @Override
    @Async
    public void sendTrasparentByHdVersionUpdate(Long userId, String hdVersion) {
        SchoolInfo schoolInfo = schoolInfoService.getByUserId(userId);
        if (schoolInfo == null) {
            return;
        }
        List<TeacherInfo> teacherInfos = teacherInfoService.getBySchoolId(schoolInfo.getId());
        TeacherInfo teacherInfo = new TeacherInfo();
        teacherInfo.setUserId(userId);
        teacherInfo.setSchoolId(schoolInfo.getId());
        teacherInfo.setId(0l);
        teacherInfos.add(teacherInfo);
        teacherInfos.forEach(e -> {
            User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, e.getUserId()));
            if (user != null) {
                Map<String, Object> message = new HashMap<>();
                message.put("action", "hd_version_update");
                message.put("deviceToken", user.getDeviceTokens());
                message.put("receiveUserId", user.getId());
                message.put("hd_version", hdVersion);
                message.put("teacherId", e.getId());
                message.put("schoolId", e.getSchoolId());
                boolean b = false;
                try {
                    b = pushUtil.sendAndroidUnicast(user.getDeviceTokens(), new JSONObject(message));
                } catch (Exception e1) {
                    e1.printStackTrace();
                    saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));
                }
            }
        });
    }

    @Override
    public void sendTrasparentByRemark(JSONObject message) {
        UserApp e = userAppService.getOne(new LambdaQueryWrapper<UserApp>().eq(UserApp::getHdStudentId, message.getLong("studentId")));
        if(e==null){
            return;
        }
        message.put("action", "teacher_remark");
        message.put("deviceToken", e.getDeviceTokens());
        message.put("receiveUserId", e.getId());
        boolean b = false;
        try {
            b = pushUtil.sendAndroidUnicast(e.getDeviceTokens(), message);
        } catch (Exception er) {
            er.printStackTrace();
        }
        saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(e.getId()).setStatus(b));

    }


    @Override
    public  boolean sendTrasparentBykindergartenMonitor(JSONObject message) {
        SchoolInfo schoolInfo = schoolInfoService.getById(message.getLong("schoolId"));
        if (schoolInfo == null) {
            return false;
        }
        List<TeacherInfo> teacherInfos = teacherInfoService.getBySchoolId(schoolInfo.getId());
        teacherInfos.forEach(e ->{
            User user = userService.getById(e.getUserId());
            if (user != null) {
                message.put("deviceToken", user.getDeviceTokens());
                message.put("receiveUserId", user.getId());
                boolean b = false;
                try {
                    b = pushUtil.sendAndroidUnicast(user.getDeviceTokens(), message);
                } catch (Exception e1) {
                    e1.printStackTrace();
                    saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(user.getId()).setStatus(b));
                }
            }
        });
        return true;
    }

    @Override
    @Async
    public void sendTransparentByMonitorUpload(List<PersonData> personDatas) {
        personDatas.forEach(e -> {
            UserApp userApp = userAppService.getById(e.getStudentId());
            if (userApp != null) {
                Map<String, Object> message = new HashMap<>();
                message.put("action", "monitor_upload");
                message.put("deviceToken", userApp.getDeviceTokens());
                message.put("receiveUserId", userApp.getId());
                message.put("monitorId", e.getMatchId());
                boolean b = false;
                try {
                    b = pushUtil.sendAndroidUnicastApp(userApp.getDeviceTokens(), new JSONObject(message));
                } catch (Exception e1) {
                    e1.printStackTrace();
                    saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(userApp.getId()).setStatus(b));
                }
            }
        });
    }

//    @Override
//    public void sendFieldNotesByStudent(JSONObject message) {
//        List<FieldData> fieldDataList = fieldDataService.list(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId,message.getLong("fieldNotesId")));
//        if(fieldDataList.size()>0){
//            for(FieldData fieldData:fieldDataList){
//                StudentInfo studentInfo = studentInfoService.getById(fieldData.getStuId());
//                FieldNotes fieldNotes = fieldNotesService.getById(fieldData.getFieldId());
//                message.put("action","fieldNotes_update");
//                message.put("deviceToken",studentInfo.getDeviceTokens());
//                message.put("receiveUserId",studentInfo.getId());
//                message.put("schoolId",studentInfo.getSchoollId());
//                message.put("drillId",fieldNotes.getDrillId());
//                message.put("fieldNodesId",fieldNotes.getId());
//                boolean b = false;
//                try {
//                    b = pushUtil.sendAndroidUnicast(studentInfo.getDeviceTokens(),message);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("PAD").setReceiveUserId(studentInfo.getId()).setStatus(b));
//            }
//        }
//    }

    @Override
    @Async
    public void sendTransparentByTeamDissolve(Long teamId) {
        // 获取球队信息
        TeamApp team = teamAppService.getById(teamId);
        if (team == null) {
            return;
        }

        // 获取球队所有成员
        List<TeamUserApp> teamUsers = teamUserAppService.getByTeamId(teamId);
        teamUsers.forEach(teamUser -> {
            UserApp userApp = userAppService.getById(teamUser.getUserId());
            if (userApp != null && userApp.getDeviceTokens() != null) {
                Map<String, Object> message = new HashMap<>();
                message.put("action", "team_dissolve");
                message.put("deviceToken", userApp.getDeviceTokens());
                message.put("receiveUserId", userApp.getId());
                message.put("teamId", teamId);
                message.put("teamName", team.getFullName());
                boolean b = false;
                try {
                    b = pushUtil.sendAndroidUnicastApp(userApp.getDeviceTokens(), new JSONObject(message));
                } catch (Exception e1) {
                    e1.printStackTrace();
                }
                saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(userApp.getId()).setStatus(b));
            }
        });
    }

    @Override
    @Async
    public void sendTransparentByKickedOut(Long userId, Long teamId) {
        UserApp userApp = userAppService.getById(userId);
        TeamApp team = teamAppService.getById(teamId);

        if (userApp != null && userApp.getDeviceTokens() != null && team != null) {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "kicked_out");
            message.put("deviceToken", userApp.getDeviceTokens());
            message.put("receiveUserId", userApp.getId());
            message.put("teamId", teamId);
            message.put("teamName", team.getFullName());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicastApp(userApp.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(userApp.getId()).setStatus(b));
        }
    }

    @Override
    @Async
    public void sendTransparentByMemberQuit(Long captainUserId, Long teamId, Long quitUserId) {
        UserApp captainUser = userAppService.getById(captainUserId);
        UserApp quitUser = userAppService.getById(quitUserId);
        TeamApp team = teamAppService.getById(teamId);

        if (captainUser != null && captainUser.getDeviceTokens() != null && quitUser != null && team != null) {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "member_quit");
            message.put("deviceToken", captainUser.getDeviceTokens());
            message.put("receiveUserId", captainUser.getId());
            message.put("teamId", teamId);
            message.put("teamName", team.getFullName());
            message.put("quitUserId", quitUserId);
            message.put("quitUserName", quitUser.getName());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicastApp(captainUser.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(captainUser.getId()).setStatus(b));
        }
    }

    @Override
    @Async
    public void sendTransparentByApplyJoin(Long captainUserId, Long teamId, Long applicantUserId) {
        UserApp captainUser = userAppService.getById(captainUserId);
        UserApp applicantUser = userAppService.getById(applicantUserId);
        TeamApp team = teamAppService.getById(teamId);

        if (captainUser != null && captainUser.getDeviceTokens() != null && applicantUser != null && team != null) {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "apply_join");
            message.put("deviceToken", captainUser.getDeviceTokens());
            message.put("receiveUserId", captainUser.getId());
            message.put("teamId", teamId);
            message.put("teamName", team.getFullName());
            message.put("applicantUserId", applicantUserId);
            message.put("applicantName", applicantUser.getName());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicastApp(captainUser.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(captainUser.getId()).setStatus(b));
        }
    }

    @Override
    @Async
    public void sendTransparentByMemberJoined(Long teamId, Long newMemberUserId) {
        // 获取球队信息和新成员信息
        TeamApp team = teamAppService.getById(teamId);
        UserApp newMember = userAppService.getById(newMemberUserId);

        if (team == null || newMember == null) {
            return;
        }

        // 获取球队所有成员
        List<TeamUserApp> teamUsers = teamUserAppService.getByTeamId(teamId);
        teamUsers.forEach(teamUser -> {
            // 排除新加入的成员本身
            if (!teamUser.getUserId().equals(newMemberUserId)) {
                UserApp userApp = userAppService.getById(teamUser.getUserId());
                if (userApp != null && userApp.getDeviceTokens() != null) {
                    Map<String, Object> message = new HashMap<>();
                    message.put("action", "member_joined");
                    message.put("deviceToken", userApp.getDeviceTokens());
                    message.put("receiveUserId", userApp.getId());
                    message.put("teamId", teamId);
                    message.put("teamName", team.getFullName());
                    message.put("newMemberUserId", newMemberUserId);
                    message.put("newMemberName", newMember.getName());
                    boolean b = false;
                    try {
                        b = pushUtil.sendAndroidUnicastApp(userApp.getDeviceTokens(), new JSONObject(message));
                    } catch (Exception e1) {
                        e1.printStackTrace();
                    }
                    saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(userApp.getId()).setStatus(b));
                }
            }
        });
    }

    @Override
    @Async
    public void sendTransparentByJoinRejected(Long applicantUserId, Long teamId) {
        UserApp applicantUser = userAppService.getById(applicantUserId);
        TeamApp team = teamAppService.getById(teamId);

        if (applicantUser != null && applicantUser.getDeviceTokens() != null && team != null) {
            Map<String, Object> message = new HashMap<>();
            message.put("action", "join_rejected");
            message.put("deviceToken", applicantUser.getDeviceTokens());
            message.put("receiveUserId", applicantUser.getId());
            message.put("teamId", teamId);
            message.put("teamName", team.getFullName());
            boolean b = false;
            try {
                b = pushUtil.sendAndroidUnicastApp(applicantUser.getDeviceTokens(), new JSONObject(message));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(applicantUser.getId()).setStatus(b));
        }
    }
}

