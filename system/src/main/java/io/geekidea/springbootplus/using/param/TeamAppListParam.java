package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * 球队列表查询参数
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TeamAppListParam球队列表查询参数")
public class TeamAppListParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("是否查询用户创建的球队：true-仅查询用户创建的，false-仅查询非用户创建的，null-查询所有")
    private Boolean createdByMe;

    @ApiModelProperty("运动类型筛选：2篮球 3足球 4数字体育课，传-1查全部")
    private Long courseId;
}
