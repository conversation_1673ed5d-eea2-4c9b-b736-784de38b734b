package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 * 战术板综合信息表 分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "战术板综合信息表分页参数")
public class TacticsBoardPageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("战术板名称")
    private String boardName;

    @ApiModelProperty("是否收藏 不传查询全部")
    private Boolean isFavorite;

    @ApiModelProperty("用户id 传了就是我的课件 传0就是官方课件 我的收藏就是不传")
    private Long userId;

    @ApiModelProperty("几人制 不传查询全部")
    private Integer people;

    @ApiModelProperty("年龄 不传查询全部")
    private Integer age;

    @ApiModelProperty("动作 不传查询全部")
    private String action;

    @ApiModelProperty("难度 不传查询全部")
    private String difficulty;

    @ApiModelProperty("战术板类型：1-静态，2-动态 不传就是查询全部")
    private Integer boardType;
}