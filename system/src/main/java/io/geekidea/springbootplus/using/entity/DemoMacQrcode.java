package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DemoMacQrcode对象")
public class DemoMacQrcode extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("添加不用管")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "不能为空")
    @TableField("demoUserId")
    @ApiModelProperty("添加不用管")
    private Long demoUserId;

    @ApiModelProperty("mac地址")
    private String mac;

    @ApiModelProperty("teambox长编号")
    private String longNum;

    @ApiModelProperty("二維碼序列號")
    @TableField("qr_code")
    private String qrCode;

    @ApiModelProperty("箱子二维码")
    @TableField("box_code")
    private String boxCode;

    @ApiModelProperty("箱子号")
    @TableField("box")
    private String box;

    @ApiModelProperty("teambox名字")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Boolean deleted;

    @ApiModelProperty("1.标准版 2.专业版")
    private Integer hardType;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;
}
