package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 * 战术板线条表，存储各种线条信息 分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "战术板线条表，存储各种线条信息分页参数")
public class TacticsBoardLinePageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;
}
