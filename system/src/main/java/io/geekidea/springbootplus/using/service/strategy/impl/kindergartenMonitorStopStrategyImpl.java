package io.geekidea.springbootplus.using.service.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.KindergartenMonitor;
import io.geekidea.springbootplus.using.entity.SchoolInfo;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.service.KindergartenMonitorService;
import io.geekidea.springbootplus.using.service.SchoolInfoService;
import io.geekidea.springbootplus.using.service.TransparentMessageService;
import io.geekidea.springbootplus.using.service.strategy.TaskStrategy;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

@Service("kindergartenMonitorDelayQueueStop")
@Slf4j
public class kindergartenMonitorStopStrategyImpl implements TaskStrategy {

    @Autowired
    TransparentMessageService transparentMessageService;
    @Autowired
    KindergartenMonitorService kindergartenMonitorService;
    volatile JSONObject message;
    @Autowired
    RedisUtils redisUtils;

    @Override
    public void dispose(Object task) {
        SchoolInfo schoolInfo = (SchoolInfo) task;
        KindergartenMonitor kindergartenMonitor = kindergartenMonitorService.getOne(new LambdaQueryWrapper<KindergartenMonitor>().eq(KindergartenMonitor::getSchoolId, schoolInfo.getId()).orderByDesc(KindergartenMonitor::getId).last(" limit 1"));
        if (kindergartenMonitor != null && kindergartenMonitor.getStatus().equals(MatchStatusEnum.STARTED)) {
            message = new JSONObject();
            message.put("schoolId", schoolInfo.getId());
            message.put("startTime", kindergartenMonitor.getStartTime().getTime());
            message.put("stopTime", kindergartenMonitor.getStopTime().getTime());
            message.put("action", "kindergartenMonitor_stop");
            message.put("monitorId", kindergartenMonitor.getId());
            transparentMessageService.sendTrasparentBykindergartenMonitor(message);
            kindergartenMonitor.setStatus(MatchStatusEnum.FINISHED);
            kindergartenMonitorService.saveOrUpdate(kindergartenMonitor);
        }
        redisUtils.set(CacheConsts.KINDERGARTENMONITORDELAYQUEUESTOP + schoolInfo.getId(), 1);
    }
}
