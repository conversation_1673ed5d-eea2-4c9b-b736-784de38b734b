package io.geekidea.springbootplus.using.controller;


import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.using.entity.DemoMacQrcode;
import io.geekidea.springbootplus.using.service.DemoMacQrcodeService;
import io.geekidea.springbootplus.using.vo.DemoMacQrcodeAo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/using/qrOperation")
@Module("teambox")
@Api(value = "系统设备管理", tags = {"系统设备管理"})
public class QrOperationController extends BaseController {
    @Autowired
    private DemoMacQrcodeService demoMacQrcodeService;
    /**
     * @desc: 登录
     * @author: DH
     * @date: 2023/12/4 18:28
     */

    @GetMapping("/findMacByBox/{boxCode}")
    @OperationLog(name = "根据箱子二维码找到设备信息", type = OperationLogType.INFO)
    @ApiOperation(value = "根据箱子二维码找到设备信息", response = DemoMacQrcodeAo.class, notes = "返回是集合 code 20043:二维码不存在")
    public ApiResult<List<DemoMacQrcodeAo>> findMacBybox(@PathVariable("boxCode") String boxCode) {
        List<DemoMacQrcodeAo> map = demoMacQrcodeService.findMacBybox(boxCode);
        if (map == null || map.size() == 0) {
            return ApiResult.result(ApiCode.CODE_NOTEXIST, map);
        }
        return ApiResult.ok(map);
    }

    @PostMapping("/addDemoMacQrcode")
    @OperationLog(name = "绑定二维码入库", type = OperationLogType.ADD)
    @ApiOperation(value = "绑定二维码入库", response = ApiResult.class)
    public ApiResult<Boolean> addDemoMacQrcode(@RequestBody DemoMacQrcode demoMacQrcode) throws Exception {
        boolean flag = demoMacQrcodeService.saveDemoMacQrcode(demoMacQrcode,getCurrentId());
        return ApiResult.result(flag);
    }

    @PostMapping("/updateDemoMacQrcode/{type}")
    @OperationLog(name = "修改", type = OperationLogType.ADD)
    @ApiOperation(value = "修改", response = ApiResult.class, notes = "match_version_update_1:修改版本为标准版 match_version_update_2:修改版本为专业版")
    public ApiResult<Boolean> updateDemoMacQrcode(@RequestBody List<DemoMacQrcode> demoMacQrcodes, @PathVariable("type") String type) throws Exception {
        boolean flag = demoMacQrcodeService.updateDemoMacQrcode(demoMacQrcodes, getCurrentId(), type);
        return ApiResult.result(flag);
    }
    @PostMapping("/deleteDemoMacQrcode")
    @OperationLog(name = "删除", type = OperationLogType.ADD)
    @ApiOperation(value = "删除", response = ApiResult.class, notes = "")
    public ApiResult<Boolean> deleteDemoMacQrcode(@RequestBody List<DemoMacQrcode> demoMacQrcodes) throws Exception {
        boolean flag = demoMacQrcodeService.deleteDemoMacQrcode(demoMacQrcodes,getCurrentId());
        return ApiResult.result(flag);
    }



}
