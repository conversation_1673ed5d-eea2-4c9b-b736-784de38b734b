package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.DrillExtra;
import io.geekidea.springbootplus.using.param.DrillExtraPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.vo.DrillExtraStudentAo;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
public interface DrillExtraService extends BaseService<DrillExtra> {

    /**
     * 保存
     *
     * @param drillExtra
     * @return
     * @throws Exception
     */
    Long saveDrillExtra(DrillExtra drillExtra) throws Exception;

    /**
     * 保存
     *
     * @param drillExtra
     * @return
     * @throws Exception
     */
    Boolean saveDrillExtra1(DrillExtra drillExtra) throws Exception;

    /**
     * 复制
     */

    boolean copyDrillExtra(Long drillExtraId) throws Exception;

    /**
     * 修改
     *
     * @param drillExtra
     * @return
     * @throws Exception
     */
    boolean updateDrillExtra(DrillExtra drillExtra) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteDrillExtra(Long id) throws Exception;

    /**
     * 根据训练id获取计数/计时
     *
     * @param id
     * @return
     * @throws Exception
     */
    List<DrillExtra> getDrillExtraByDrillId(DrillExtraPageParam drillExtraPageParam) throws Exception;

    /**
     * @desc: 查看记时计数数量
     * @author: DH
     * @date: 2022/7/26 14:16
     */
    JSONObject getDrillExtrasCount(Long drillId,Boolean prot);

    /**
     * @desc: 查询学生训练的记时记数
     * @author: DH
     * @date: 2022/7/15 14:36
     */
    List<DrillExtraStudentAo> getDrillExtraByStu(DrillExtraPageParam drillExtraPageParam);

    /**
     * @desc: 根据id获取计数/记时详细数据
     * @author: DH
     * @date: 2022/7/22 18:24
     */
    DrillExtra getDrillExtra(Long drillExtraId);


    /**
     * @desc: 修改学生扫码状态
     * @author: DH
     * @date: 2023/3/15 17:51
     */
    boolean updatescanQRcodes(Long drillExtraId,Long studentId);

    DrillExtraPageParam createDrillExtraPageParam(Long drillId, Long studentId, Integer finish, Integer type,Boolean astrict,Long pi,Long ps);
}
