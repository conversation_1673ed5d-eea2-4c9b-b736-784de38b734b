package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.constant.CacheConsts;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.entity.FacilityGroup;
import io.geekidea.springbootplus.using.entity.FacilityHardware;
import io.geekidea.springbootplus.using.service.FacilityGroupService;
import io.geekidea.springbootplus.using.service.FacilityHardwareService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.FacilityGroupPageParam;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Slf4j
@RestController
@RequestMapping("/using/facilityGroup")
@Module("facilityGroupController")
@Api(value = "设备组相关", tags = {"设备组相关"})
public class FacilityGroupController extends BaseController {

    @Autowired
    private FacilityGroupService facilityGroupService;
    @Autowired
    private FacilityHardwareService facilityHardwareService;
    @Autowired
    private RedisUtils redisUtils;


    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class, notes = "错误码 20046 设备组箱号重复 20048 设备组被其他学校绑定  data返回设备组id data:12")
    public ApiResult addFacilityGroup(@RequestBody FacilityGroup facilityGroup) throws Exception {
        FacilityGroup facilityGroup1 = facilityGroupService.getOne(new LambdaQueryWrapper<FacilityGroup>().eq(FacilityGroup::getBox, facilityGroup.getBox()));
        if (facilityGroup1 != null && !facilityGroup1.getSchoolId().equals(facilityGroup.getSchoolId())) {
            return ApiResult.result(ApiCode.HARDWAREGROUP_BIND_S);
        }
        facilityGroup1 = facilityGroupService.getOne(new LambdaQueryWrapper<FacilityGroup>().eq(FacilityGroup::getSchoolId, facilityGroup.getSchoolId()).eq(FacilityGroup::getBox, facilityGroup.getBox()));
        if (facilityGroup1 != null) {
            return ApiResult.result(ApiCode.HARDWAREGROUP_REP);
        }
        facilityGroupService.saveFacilityGroup(facilityGroup);
        return ApiResult.ok(facilityGroup.getId());
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    public ApiResult<Boolean> updateFacilityGroup(@RequestBody FacilityGroup facilityGroup) throws Exception {
        boolean flag = facilityGroupService.updateFacilityGroup(facilityGroup);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteFacilityGroup(@PathVariable("id") Long id) throws Exception {
        boolean flag = facilityGroupService.deleteFacilityGroup(id);
        return ApiResult.result(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = FacilityGroup.class)
    public ApiResult<FacilityGroup> getFacilityGroup(@PathVariable("id") Long id) throws Exception {
        FacilityGroup facilityGroup = facilityGroupService.getFacilityGroup(id);
        return ApiResult.ok(facilityGroup);
    }

    @PostMapping("/updateMore/{more}")
    @OperationLog(name = "修改默认设备组类型", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改默认设备组类型", response = ApiResult.class, notes = "more 修改的设备组类型 true|false")
    public ApiResult<Boolean> updateMore(@PathVariable("more") Boolean more) {
        Long id = getCurrentId();
        redisUtils.set(CacheConsts.SCHOOL_MORE + id, more);
        return ApiResult.result(true);
    }

    @GetMapping("/residue/groupId")
    @OperationLog(name = "获取设备组剩余可绑定设备")
    @ApiOperation(value = "获取设备组剩余可绑定设备", response = ApiResult.class, notes = "返回字段参考 获取设备组设备信息")
    public ApiResult residue(@PathVariable("groupId") Long groupId) {
        return ApiResult.ok(facilityGroupService.residue(groupId));
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = FacilityGroup.class)
    public ApiResult<Paging<FacilityGroup>> getFacilityGroupPageList(@RequestBody FacilityGroupPageParam facilityGroupPageParam) throws Exception {
        facilityGroupPageParam.setSchoolId(getCurrent().getSchoolInfo().getId());
        Paging<FacilityGroup> paging = facilityGroupService.getFacilityGroupPageList(facilityGroupPageParam);
        return ApiResult.ok(paging);
    }

    /**
     * @desc: 开通新班级
     * @author: DH
     * @date: 2022/8/4 16:10
     */
    @PutMapping("/dredge/{groupId}/{teamId}/{originalGroupId}")
    @OperationLog(name = "开通新班级")
    @ApiOperation(value = "开通新班级", response = ApiResult.class, notes = "code20050 设备组绑定超出限制数量  originalGroupId：原来的设备组id 用户换绑使用 如果没有绑定设备组就传-1")
    public ApiResult<Boolean> dredge(@PathVariable("groupId") Long groupId, @PathVariable("teamId") Long teamId, @PathVariable("originalGroupId") Long originalGroupId) {
        Boolean b = facilityGroupService.dredge(groupId, teamId, originalGroupId);
        if (b == null) {
            return ApiResult.result(ApiCode.HARDWAREGROUP_TEAM_EXCEED);
        }
        return ApiResult.ok(b);
    }

    @PostMapping("/addFacilityHard")
    @OperationLog(name = "添加全组设备")
    @ApiOperation(value = "添加全组设备", response = FacilityHardware.class, notes = "添加完后会返回上传的body 会有id的值")
    public ApiResult<Object> addFacilityHard(@RequestBody List<FacilityHardware> facilityGroup) throws Exception {
        return ApiResult.ok(facilityHardwareService.saveFacilityHardware(facilityGroup));
    }

    @PostMapping("/getFacilityHardwares/{groupId}")
    @OperationLog(name = "获取设备组设备信息")
    @ApiOperation(value = "获取设备组设备信息", response = FacilityHardware.class, notes = "返回是数组")
    public ApiResult<List<FacilityHardware>> getFacilityHardwares(@PathVariable("groupId") Long groupId) throws Exception {
        return ApiResult.ok(facilityHardwareService.getFacilityHardwareList(groupId));
    }

    @PostMapping("/unBind/{groupId}/{teamId}")
    @OperationLog(name = "设备组解绑班级")
    @ApiOperation(value = "设备组解绑班级", response = Boolean.class, notes = "")
    public ApiResult<Boolean> unBind(@PathVariable("teamId") Long teamId, @PathVariable("groupId") Long groupId) {
        return ApiResult.ok(facilityGroupService.unBind(teamId, groupId));
    }

    @PostMapping("/groupHardUpdate")
    @OperationLog(name = "设备组设备修改")
    @ApiOperation(value = "设备组设备修改", response = Boolean.class, notes = "body是集合并且id要传 获取设备组设备信息接口返回的id字段")
    public ApiResult<Boolean> groupHardUpdate(@RequestBody List<FacilityHardware> facilityHardwares) {
        return ApiResult.ok(facilityHardwareService.groupHardUpdate(facilityHardwares));
    }

    @PostMapping("/groupHardReplace/{mac}")
    @OperationLog(name = "设备组设备替换")
    @ApiOperation(value = "设备组设备替换", response = Boolean.class, notes = "参数mac：被替换的mac body就是新设备信息 code 20049 设备已被其他设备组绑定")
    public ApiResult groupHardReplace(@PathVariable("mac") String mac, @RequestBody FacilityHardware facilityHardware) {
        Object o = facilityHardwareService.groupHardReplace(mac, facilityHardware);
        if (o == null) {
            return ApiResult.result(ApiCode.HARDWAREGROUP_REPLACE);
        }
        return ApiResult.ok(o);
    }

    @GetMapping("/getFacilityGroupId/{teamId}")
    @OperationLog(name = "根据班级id，获取班级的设备组id")
    @ApiOperation(value = "根据班级id，获取班级的设备组id", response = Long.class, notes = "没有绑定，返回-1")
    public ApiResult getFacilityGroupId(@PathVariable("teamId") Long teamId) {
        return ApiResult.ok(facilityGroupService.getFacilityGroupId(teamId));
    }


}

