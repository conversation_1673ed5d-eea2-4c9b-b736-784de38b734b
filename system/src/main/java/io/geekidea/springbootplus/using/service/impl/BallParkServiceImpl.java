package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.BallPark;
import io.geekidea.springbootplus.using.mapper.BallParkMapper;
import io.geekidea.springbootplus.using.service.BallParkService;
import io.geekidea.springbootplus.using.param.BallParkPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Slf4j
@Service
public class BallParkServiceImpl extends BaseServiceImpl<BallParkMapper, BallPark> implements BallParkService {

    @Autowired
    private BallParkMapper ballParkMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveBallPark(BallPark ballPark) throws Exception {
        return super.save(ballPark);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateBallPark(BallPark ballPark) throws Exception {
        return super.updateById(ballPark);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteBallPark(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<BallPark> getBallParkPageList(BallParkPageParam ballParkPageParam) throws Exception {
        Page<BallPark> page = new PageInfo<>(ballParkPageParam, OrderItem.desc(getLambdaColumn(BallPark::getCreateTime)));
        LambdaQueryWrapper<BallPark> wrapper = new LambdaQueryWrapper<>();
        IPage<BallPark> iPage = ballParkMapper.selectPage(page, wrapper);
        return new Paging<BallPark>(iPage);
    }

    @Override
    public List<BallPark> historicBallPark(Long courseId, Long studentId, String search) {
        if (search.equals("null")) {
            search = "";
        }
        return getBaseMapper().historicBallPark(courseId, studentId, search);
    }

}
