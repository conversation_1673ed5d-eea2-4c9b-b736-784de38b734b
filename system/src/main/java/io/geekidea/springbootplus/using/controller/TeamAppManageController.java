package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.using.controller.BaseController;
import io.geekidea.springbootplus.using.entity.UserApp;
import io.geekidea.springbootplus.using.param.TeamAppListParam;
import io.geekidea.springbootplus.using.param.TeamMemberPageParam;
import io.geekidea.springbootplus.using.service.TeamAppService;
import io.geekidea.springbootplus.using.service.TeamJoinApplicationService;
import io.geekidea.springbootplus.using.vo.TeamAppVO;
import io.geekidea.springbootplus.using.vo.TeamMemberVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 球队APP管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/using/teamAppManage")
@Module("球队APP管理")
@Api(value = "球队APP管理模块", tags = {"球队APP管理模块"})
public class TeamAppManageController extends BaseController {

    @Autowired
    private TeamAppService teamAppService;

    @Autowired
    private TeamJoinApplicationService teamJoinApplicationService;

    /**
     * 获取主队球队列表
     */
    @PostMapping("/home-teams")
    @OperationLog(name = "主队球队列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "主队球队列表", response = TeamAppVO.class)
    public ApiResult getHomeTeamsList(@Validated @RequestBody TeamAppListParam param) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        Paging<TeamAppVO> paging = teamAppService.getHomeTeamsList(param, currentUser.getId());
        return ApiResult.ok(paging);
    }

    /**
     * 获取用户加入的球队列表
     */
    @PostMapping("/joined-teams")
    @OperationLog(name = "用户加入的球队列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "用户加入的球队列表", response = TeamAppVO.class)
    public ApiResult getJoinedTeamsList(@Validated @RequestBody TeamAppListParam param) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        Paging<TeamAppVO> paging = teamAppService.getJoinedTeamsList(param, currentUser.getId());
        return ApiResult.ok(paging);
    }

    /**
     * 获取球队成员分页列表
     */
    @PostMapping("/members/page")
    @OperationLog(name = "球队成员分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "球队成员分页列表", response = TeamMemberVO.class, notes = "返回分页数据")
    public ApiResult getTeamMembersPageList(@Validated @RequestBody TeamMemberPageParam param) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }

        if (param.getTeamId() == null) {
            return ApiResult.fail("球队ID不能为空");
        }

        Paging<TeamMemberVO> paging = teamAppService.getTeamMembersPageList(param, currentUser.getId());
        return ApiResult.ok(paging);
    }

    /**
     * 删除球队成员
     */
    @DeleteMapping("/{teamId}/members/{userId}")
    @OperationLog(name = "删除球队成员", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除球队成员", response = Boolean.class)
    public ApiResult removeMember(
            @ApiParam("球队ID") @PathVariable("teamId") Long teamId,
            @ApiParam("用户ID") @PathVariable("userId") Long userId) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        boolean result = teamAppService.removeMember(teamId, userId, currentUser.getId());
        return ApiResult.ok(result);
    }

    /**
     * 解散球队
     */
    @DeleteMapping("/{teamId}/dissolve")
    @OperationLog(name = "解散球队", type = OperationLogType.DELETE)
    @ApiOperation(value = "解散球队", response = Boolean.class)
    public ApiResult dissolveTeam(
            @ApiParam("球队ID") @PathVariable("teamId") Long teamId) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        boolean result = teamAppService.dissolveTeam(teamId, currentUser.getId());
        return ApiResult.ok(result);
    }

    /**
     * 退出球队
     */
    @PostMapping("/{teamId}/quit")
    @OperationLog(name = "退出球队", type = OperationLogType.UPDATE)
    @ApiOperation(value = "退出球队", response = Boolean.class)
    public ApiResult quitTeam(
            @ApiParam("球队ID") @PathVariable("teamId") Long teamId) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        boolean result = teamAppService.quitTeam(teamId, currentUser.getId());
        return ApiResult.ok(result);
    }

    /**
     * 申请加入球队
     */
    @PostMapping("/{teamId}/apply")
    @OperationLog(name = "申请加入球队", type = OperationLogType.ADD)
    @ApiOperation(value = "申请加入球队", response = Long.class)
    public ApiResult applyJoinTeam(
            @ApiParam("球队ID") @PathVariable("teamId") Long teamId,
            @ApiParam("申请理由") @RequestParam(value = "reason", required = false) String reason) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        Long applicationId = teamJoinApplicationService.applyJoinTeam(teamId, currentUser.getId(), reason);
        return ApiResult.ok(applicationId);
    }
}
