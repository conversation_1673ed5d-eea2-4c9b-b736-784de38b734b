package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.QrOperation;
import io.geekidea.springbootplus.using.mapper.QrOperationMapper;
import io.geekidea.springbootplus.using.service.QrOperationService;
import io.geekidea.springbootplus.using.param.QrOperationPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Slf4j
@Service
public class QrOperationServiceImpl extends BaseServiceImpl<QrOperationMapper, QrOperation> implements QrOperationService {

    @Autowired
    private QrOperationMapper qrOperationMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveQrOperation(QrOperation qrOperation) throws Exception {
        return super.save(qrOperation);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateQrOperation(QrOperation qrOperation) throws Exception {
        return super.updateById(qrOperation);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteQrOperation(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<QrOperation> getQrOperationPageList(QrOperationPageParam qrOperationPageParam) throws Exception {
        Page<QrOperation> page = new PageInfo<>(qrOperationPageParam, OrderItem.desc(getLambdaColumn(QrOperation::getCreateTime)));
        LambdaQueryWrapper<QrOperation> wrapper = new LambdaQueryWrapper<>();
        IPage<QrOperation> iPage = qrOperationMapper.selectPage(page, wrapper);
        return new Paging<QrOperation>(iPage);
    }

    @Override
    public QrOperation create(String box, String type, Long userId) {
        QrOperation qrOperation = new QrOperation();
        qrOperation.setBox(box);
        qrOperation.setType(type);
        qrOperation.setUserId(userId);
        return qrOperation;
    }

}
