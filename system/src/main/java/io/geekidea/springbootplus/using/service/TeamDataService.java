package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TeamData;
import io.geekidea.springbootplus.using.param.TeamDataPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
public interface TeamDataService extends BaseService<TeamData> {

    /**
     * 保存
     *
     * @param teamData
     * @return
     * @throws Exception
     */
    boolean saveTeamData(TeamData teamData) throws Exception;

    /**
     * 修改
     *
     * @param teamData
     * @return
     * @throws Exception
     */
    boolean updateTeamData(TeamData teamData) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTeamData(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param teamDataQueryParam
     * @return
     * @throws Exception
     */
    Paging<TeamData> getTeamDataPageList(TeamDataPageParam teamDataPageParam) throws Exception;

    TeamData findOfTeam(Long mactchId,Long teamId,String matchType);

    List<TeamData> findOfTeamList(Long teamId,String matchType);
}
