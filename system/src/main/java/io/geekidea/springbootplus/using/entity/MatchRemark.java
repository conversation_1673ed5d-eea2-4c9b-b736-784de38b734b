package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * <AUTHOR>
 * @since 2023-08-12
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MatchRemark对象")
@TableName(autoResultMap = true)
public class MatchRemark extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("赛事id")
    private Long matchId;

    @ApiModelProperty("GAME|DRILL")
    private String matchType;

    @ApiModelProperty("学生id")
    private Long studentId;

    @ApiModelProperty("点评标签id集合" +
            "足球的：\n" +
            "1.传球精准\n" +
            "2.防守出色\n" +
            "3.进攻迅猛\n" +
            "4.球技精湛\n" +
            "5.球风独特\n" +
            "6.全能中场\n" +
            "7.重炮手\n" +
            "8.足球达人\n" +
            "9.球队核心 " +
            "篮球的：\n" +
            "1.传球精准 \n" +
            "2.防守出色\n" +
            "3.进攻迅猛\n" +
            "4.球技精湛  \n" +
            "5.配合默契\n" +
            "6.快速反击\n" +
            "7.精准投篮\n" +
            "8.严防死守\n" +
            "9.战术执行\n" +
            "10.团队合作\n" +
            "11.进攻流畅\n" +
            "12.防守稳固\n" +
            "13.全场紧逼\n"+" [1,2,3,4,5]")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray remarkTag;

    @ApiModelProperty("点评文字")
    private String remarkText;

    @ApiModelProperty("点评老师id")
    private Long remarkTeacherId;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
