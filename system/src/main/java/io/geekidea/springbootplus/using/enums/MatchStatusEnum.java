package io.geekidea.springbootplus.using.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @desc:
 * @author: ZXB
 * @date: 2021/1/18 17:46
 */
@Getter
public enum MatchStatusEnum {
    /**
     * @desc: 未开始:NOTSTARTED,已开始:STARTED,已结束:FINISHED,UNFINISHED 未完成
     * @author: ZXB
     * @date: 2021/1/18 17:52
     */
    NOTSTARTED("NOTSTARTED"),STARTED("STARTED"),FINISHED("FINISHED"),UNFINISHED("UNFINISHED");

    @EnumValue
    private final String status;

    MatchStatusEnum(String status) {
        this.status = status;
    }

    public static MatchStatusEnum getValue(String status) {
        for (MatchStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus() == status) {
                return statusEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "MatchStatusEnum{" +
                "status='" + status + '\'' +
                '}';
    }
}
