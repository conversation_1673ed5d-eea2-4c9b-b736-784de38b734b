package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TacticsBoardTool;
import io.geekidea.springbootplus.using.param.TacticsBoardToolPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 * 战术板工具表，存储各种工具信息 服务类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface TacticsBoardToolService extends BaseService<TacticsBoardTool> {

    /**
     * 保存
     *
     * @param tacticsBoardTool
     * @return
     * @throws Exception
     */
    boolean saveTacticsBoardTool(TacticsBoardTool tacticsBoardTool) throws Exception;

    /**
     * 修改
     *
     * @param tacticsBoardTool
     * @return
     * @throws Exception
     */
    boolean updateTacticsBoardTool(TacticsBoardTool tacticsBoardTool) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTacticsBoardTool(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param tacticsBoardToolQueryParam
     * @return
     * @throws Exception
     */
    Paging<TacticsBoardTool> getTacticsBoardToolPageList(TacticsBoardToolPageParam tacticsBoardToolPageParam) throws Exception;

}
