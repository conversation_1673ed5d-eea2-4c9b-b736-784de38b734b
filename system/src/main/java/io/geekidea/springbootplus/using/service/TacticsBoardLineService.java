package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TacticsBoardLine;
import io.geekidea.springbootplus.using.param.TacticsBoardLinePageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 * 战术板线条表，存储各种线条信息 服务类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface TacticsBoardLineService extends BaseService<TacticsBoardLine> {

    /**
     * 保存
     *
     * @param tacticsBoardLine
     * @return
     * @throws Exception
     */
    boolean saveTacticsBoardLine(TacticsBoardLine tacticsBoardLine) throws Exception;

    /**
     * 修改
     *
     * @param tacticsBoardLine
     * @return
     * @throws Exception
     */
    boolean updateTacticsBoardLine(TacticsBoardLine tacticsBoardLine) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTacticsBoardLine(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param tacticsBoardLineQueryParam
     * @return
     * @throws Exception
     */
    Paging<TacticsBoardLine> getTacticsBoardLinePageList(TacticsBoardLinePageParam tacticsBoardLinePageParam) throws Exception;

}
