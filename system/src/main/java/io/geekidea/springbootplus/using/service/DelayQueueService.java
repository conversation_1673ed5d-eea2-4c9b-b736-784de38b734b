package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.Drill;
import io.geekidea.springbootplus.using.entity.Game;
import io.geekidea.springbootplus.using.entity.KindergartenMonitor;

public interface DelayQueueService {

    void addDrillDelayQueue(Drill drill);

    void removeDrillDelayQueue(Long drillId);

    void addGameDelayQueue(Game game);

    void removeGameDelayQueue(Long gameId);

    void addMoreRestrictDelayQueue(Long teamId,Long delayTime);

    void removeGroupMoreRestrictDelayQueue(Long teamId);

   void addkindergartenMonitor(KindergartenMonitor kindergartenMonitor);

    void removekindergartenMonitor(String key,Long id);

}
