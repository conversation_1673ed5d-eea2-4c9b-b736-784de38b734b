package io.geekidea.springbootplus.using.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.entity.HardwareApp;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.entity.UserApp;
import io.geekidea.springbootplus.using.service.DemoMacQrcodeService;
import io.geekidea.springbootplus.using.service.HardwareAppService;
import io.geekidea.springbootplus.using.service.StudentInfoService;
import io.geekidea.springbootplus.using.service.UserAppService;
import io.geekidea.springbootplus.using.vo.DemoMacQrcodeAo;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Slf4j
@RestController
@RequestMapping("/using/hardwareApp")
@Module("teambox")
@Api(value = "APP设备相关", tags = {"APP设备相关"})
public class HardwareAppController extends BaseController {

    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private DemoMacQrcodeService demoMacQrcodeService;
    @Autowired
    private HardwareAppService hardwareService;
    @Autowired
    private UserAppService userAppService;

    @GetMapping("/findMac/{qrCode}")
    @OperationLog(name = "根据teambox二维码找到设备信息", type = OperationLogType.INFO)
    @ApiOperation(value = "根据teambox二维码找到设备信息", response = DemoMacQrcodeAo.class, notes = "code 20043:二维码不存在")
    public ApiResult<DemoMacQrcodeAo> findMac(@PathVariable("qrCode") String qrCode) {
        DemoMacQrcodeAo map = demoMacQrcodeService.findMac(qrCode);
        if (map == null) {
            return ApiResult.result(ApiCode.CODE_NOTEXIST, map);
        }
        return ApiResult.ok(map);
    }

    @GetMapping("/findByMac/{mac}")
    @OperationLog(name = "根据Mac找到设备信息", type = OperationLogType.INFO)
    @ApiOperation(value = "根据Mac找到设备信息", response = DemoMacQrcodeAo.class, notes = "code 20042:MAC不存在")
    public ApiResult<DemoMacQrcodeAo> findByMac(@PathVariable("mac") String mac) {
        DemoMacQrcodeAo map = demoMacQrcodeService.findByMac(mac);
        if (map == null) {
            return ApiResult.result(ApiCode.MAC_NOTEXIST, map);
        }
        return ApiResult.ok(map);
    }

    @GetMapping("/findById/{id}")
    @OperationLog(name = "根据ID找到设备信息", type = OperationLogType.INFO)
    @ApiOperation(value = "根据ID找到设备信息", response = HardwareApp.class)
    public ApiResult<HardwareApp> findById(@PathVariable("id") Long id) {
        HardwareApp hardware = hardwareService.getById(id);
        return ApiResult.ok(hardware);
    }

    @PostMapping("/add")
    @OperationLog(name = "添加（学生与设备绑定）", type = OperationLogType.ADD)
    @ApiOperation(value = "添加（学生与设备绑定）", response = HardwareApp.class, notes =
            "code 20042:MAC不存在 20041:球鞋已被绑定 已绑定data会返回hardware对象 ")
    public ApiResult addHardwareApp(@RequestBody HardwareApp hardware) throws Exception {
        HardwareApp hardware1 = hardwareService.getOne(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getMac, hardware.getMac()).eq(HardwareApp::getUnbind, false));
        if (hardware1 != null && !hardware1.getStudentId().equals(hardware.getStudentId())) { //是否有其他成员绑定
            hardware1.setStudentInfo(studentInfoService.getById(hardware1.getStudentId()));
            return ApiResult.result(ApiCode.HARDWARE_BIND, hardware1);
        }
        if (hardware.getBattery() != null || hardware.getBattery() != -1) {
            hardware.setBatteryTime(new Date());
        }
        hardware1 = hardwareService.getOne(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getMac, hardware.getMac()).eq(HardwareApp::getStudentId, hardware.getStudentId()).eq(HardwareApp::getUnbind, true));
        if (hardware1 != null) { //是否 自己原来绑定过的重新绑定
            List<HardwareApp> hardwareApps = new ArrayList<>();
            HardwareApp hardware2 = hardwareService.getOne(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getStudentId, hardware.getStudentId()).eq(HardwareApp::getUnbind, 0));
            if (hardware2 != null) {
                hardware2.setUnbind(true);
                hardwareApps.add(hardware2);
            }
            hardware.setId(hardware1.getId());
            hardware.setUnbind(false);
            hardwareApps.add(hardware);
            hardwareService.updateHardwareApp(hardwareApps);
            return ApiResult.ok(hardware);
        }

        hardware1 = hardwareService.getOne(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getMac, hardware.getMac()).eq(HardwareApp::getStudentId, hardware.getStudentId()).eq(HardwareApp::getUnbind, false));
        if (hardware1 != null) { //重复绑定
            return ApiResult.ok(hardware1);
        }

        hardware1 = hardwareService.getOne(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getStudentId, hardware.getStudentId()).eq(HardwareApp::getUnbind, false));
        if (hardware1 != null) { //换绑
            hardware1.setUnbind(true);
            hardwareService.saveOrUpdate(hardware1);
        }
        UserApp userApp = userAppService.getById(hardware.getStudentId());
        if (userApp.getHdStudentId() != null) {
            StudentInfo studentInfo = studentInfoService.getById(userApp.getHdStudentId());
            hardware.setSchoollId(studentInfo.getSchoollId());
            hardware.setTeamId(studentInfo.getTeamId());
        }
        hardware.setUnbind(false);
        hardware.setHardType(null);
        hardwareService.saveOrUpdate(hardware);
        return ApiResult.ok(hardware);
    }


    @PostMapping("/update")
    @OperationLog(name = "修改--解绑修改unbind 参数", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改--解绑修改unbind 参数", response = ApiResult.class)
    public ApiResult<Boolean> updateHardware(@RequestBody HardwareApp hardware) throws Exception {
        if (hardware.getBattery() != null || hardware.getBattery() != -1) {
            hardware.setBatteryTime(new Date());
        }
        List<HardwareApp> hardwareApps = new ArrayList<>();
        hardwareApps.add(hardware);
        boolean flag = hardwareService.updateHardwareApp(hardwareApps);
        return ApiResult.result(flag);
    }

    @PostMapping("/startHardwares/{drillId}/{teamId}")
    @OperationLog(name = "手机端指定训练启动设备")
    @ApiOperation(value = "手机端指定训练启动设备", response = Boolean.class, notes = "teamId球队id 没有就传0 drillId:训练id body 集合可以只给 id")
    public ApiResult startHardwares(@RequestBody List<HardwareApp> hardwares, @PathVariable("drillId") Long drillId, @PathVariable("teamId") Long teamId) throws Exception {
        boolean flag = hardwareService.startHardwares(hardwares, drillId, teamId);
        return ApiResult.result(flag);
    }

    @PostMapping("/startHardwaresGame/{gameId}/{teamId}")
    @OperationLog(name = "手机端比赛启动设备")
    @ApiOperation(value = "手机端比赛启动设备", response = Boolean.class, notes = "gameId:比赛id body 集合可以只给 id")
    public ApiResult startHardwaresGame(@RequestBody List<HardwareApp> hardwares, @PathVariable("gameId") Long gameId, @PathVariable("teamId") Long teamId) throws Exception {
        boolean flag = hardwareService.startHardwaresGame(hardwares, gameId, teamId);
        return ApiResult.result(flag);
    }


}

