package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.DemoMacQrcode;
import io.geekidea.springbootplus.using.entity.FacilityGroup;
import io.geekidea.springbootplus.using.entity.FacilityHardware;
import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.mapper.FacilityHardwareMapper;
import io.geekidea.springbootplus.using.service.DemoMacQrcodeService;
import io.geekidea.springbootplus.using.service.FacilityGroupService;
import io.geekidea.springbootplus.using.service.FacilityHardwareService;
import io.geekidea.springbootplus.using.param.FacilityHardwarePageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.service.HardwareService;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Slf4j
@Service
public class FacilityHardwareServiceImpl extends BaseServiceImpl<FacilityHardwareMapper, FacilityHardware> implements FacilityHardwareService {

    @Autowired
    private FacilityHardwareMapper facilityHardwareMapper;
    @Autowired
    private FacilityGroupService facilityGroupService;
    @Autowired
    private DemoMacQrcodeService demoMacQrcodeService;
    @Autowired
    private HardwareService hardwareService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<FacilityHardware> saveFacilityHardware(List<FacilityHardware> facilityHardware) throws Exception {
        facilityHardware.forEach(e -> {
            FacilityHardware facilityHardware1 = getOne(new LambdaQueryWrapper<FacilityHardware>().eq(FacilityHardware::getMac, e.getMac()));
            if (facilityHardware1 != null) {
                e.setId(facilityHardware1.getId());
            }
        });
        super.saveOrUpdateBatch(facilityHardware);
        return facilityHardware;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateFacilityHardware(FacilityHardware facilityHardware) throws Exception {
        return super.updateById(facilityHardware);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteFacilityHardware(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public List<FacilityHardware> getFacilityHardwareList(Long groupId) {
        return list(new LambdaQueryWrapper<FacilityHardware>().eq(FacilityHardware::getGroupId, groupId));
    }

    @Override
    public int getFacilityHardwareCount(Long groupId) {
        return count(new LambdaQueryWrapper<FacilityHardware>().eq(FacilityHardware::getGroupId, groupId));
    }

    @Override
    @Transactional
    public Boolean groupHardUpdate(List<FacilityHardware> facilityHardwares) {
        facilityHardwares.forEach(e ->{
            if(e.getVersion()!=null){
                FacilityHardware facilityHardware = getById(e.getId());
                List<Hardware> hardwares = hardwareService.list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getMac,facilityHardware.getMac()).eq(Hardware::getUnbind,0));
                if(hardwares!=null && hardwares.size()>0){
                    hardwares.forEach(h ->{
                        h.setVersion(e.getVersion());
                    });
                    hardwareService.saveOrUpdateBatch(hardwares);
                }
            }
        });
        return saveOrUpdateBatch(facilityHardwares);
    }

    @Override
    public Boolean groupHardReplace(String mac, FacilityHardware facilityHardware) {
        DemoMacQrcode demoMacQrcode = demoMacQrcodeService.getOne(new LambdaQueryWrapper<DemoMacQrcode>().eq(DemoMacQrcode::getMac, facilityHardware.getMac()));
        if (demoMacQrcode != null && !demoMacQrcode.getBox().equals(facilityHardware.getBox())) {
            return null;
        }
        FacilityHardware facilityHardware1 = getOne(new LambdaQueryWrapper<FacilityHardware>().eq(FacilityHardware::getMac, mac));
        facilityHardware.setId(facilityHardware1.getId());
        facilityHardware.setNum(facilityHardware1.getNum());
        return saveOrUpdate(facilityHardware);
    }

}
