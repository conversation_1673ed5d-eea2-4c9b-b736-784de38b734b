package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.using.entity.User;
import io.geekidea.springbootplus.using.service.OstrichService;
import io.geekidea.springbootplus.using.service.SchoolInfoService;
import io.geekidea.springbootplus.using.service.UserService;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/using/ostrich")
@Module("teambox")
@Api(value = "鸵鸟专供", tags = {"鸵鸟专供"})
public class OstrichController extends BaseController {
    @Autowired
    OstrichService ostrichService;
    @Autowired
    UserService userService;
    @Autowired
     SchoolInfoService schoolInfoService;

    @PostMapping("/ostrichAllDataByGame")
    public ApiResult ostrichAllDataByGame(@RequestBody JSONObject object) throws Exception {
        if (StringUtils.isNotBlank(object.getString("username")) && StringUtils.isNotBlank(object.getString("password"))) {
            User user = userService.getByName(object.getString("username"));
            if (user == null) {
                return ApiResult.fail(ApiCode.USERNAME_ERROR);
            }
            if (userService.checkPassword(object.getString("password"),user.getPassword())) {
                return ApiResult.fail(ApiCode.PASSWORD_ERROR);
            }
            return ApiResult.ok(ostrichService.allDataByGame(schoolInfoService.getByUserId(user.getId()).getId(),object.getString("startTime"),object.getString("startTime")));
        } else {
            return ApiResult.fail(ApiCode.USERNAME_PASSWORD_ISNULL);
        }
    }

    @PostMapping("/ostrichAllDataByDrill")
    public ApiResult ostrichAllDataByDrill(@RequestBody JSONObject object) throws Exception {
        if (StringUtils.isNotBlank(object.getString("username")) && StringUtils.isNotBlank(object.getString("password"))) {
            User user = userService.getByName(object.getString("username"));
            if (user == null) {
                return ApiResult.fail(ApiCode.USERNAME_ERROR);
            }
            if (userService.checkPassword(object.getString("password"),user.getPassword())) {
                return ApiResult.fail(ApiCode.PASSWORD_ERROR);
            }
            return ApiResult.ok(ostrichService.allDataByDrill(schoolInfoService.getByUserId(user.getId()).getId(),object.getString("date"),object.getString("startTime")));
        } else {
            return ApiResult.fail(ApiCode.USERNAME_PASSWORD_ISNULL);
        }
    }
}
