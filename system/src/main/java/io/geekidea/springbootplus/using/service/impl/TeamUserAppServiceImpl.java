package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.Game;
import io.geekidea.springbootplus.using.entity.TeamUserApp;
import io.geekidea.springbootplus.using.mapper.TeamUserAppMapper;
import io.geekidea.springbootplus.using.service.TeamUserAppService;
import io.geekidea.springbootplus.using.param.TeamUserAppPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Slf4j
@Service
public class TeamUserAppServiceImpl extends BaseServiceImpl<TeamUserAppMapper, TeamUserApp> implements TeamUserAppService {

    @Autowired
    private TeamUserAppMapper teamUserAppMapper;


    @Override
    public List<TeamUserApp> getByUserId(Long userId) {
        return list(new LambdaQueryWrapper<TeamUserApp>().eq(TeamUserApp::getUserId,userId));
    }

    @Override
    public List<Long> getTeamIdsByUserId(Long userId) {
        List<TeamUserApp> teamUserApps = getByUserId(userId);
        return teamUserApps.stream().map(TeamUserApp::getTeamId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<TeamUserApp> getByTeamId(Long teamId) {
        return list(new LambdaQueryWrapper<TeamUserApp>().eq(TeamUserApp::getTeamId,teamId));
    }

    @Override
    public Long getByUserIdAndGameId(Long userId, Game game) {
        List<TeamUserApp> teamUserApps = getByUserId(userId);
        for (TeamUserApp teamUserApp : teamUserApps) {
            if (teamUserApp.getTeamId().equals(game.getTeamId())) {
                return teamUserApp.getTeamId();
            } else if (teamUserApp.getTeamId().equals(game.getOpponentId())) {
                return teamUserApp.getTeamId();
            }
        }
        return 0l;
    }
}
