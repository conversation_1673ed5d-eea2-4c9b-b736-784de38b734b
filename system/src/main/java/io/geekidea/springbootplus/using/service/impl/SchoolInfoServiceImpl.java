package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.mapper.SchoolInfoMapper;
import io.geekidea.springbootplus.using.service.SchoolInfoService;
import io.geekidea.springbootplus.using.param.SchoolInfoPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.service.StudentInfoService;
import io.geekidea.springbootplus.using.service.TeacherInfoService;
import io.geekidea.springbootplus.using.service.TeamService;
import io.geekidea.springbootplus.using.vo.SchoolPersonCountAo;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import springfox.documentation.annotations.Cacheable;

import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class SchoolInfoServiceImpl extends BaseServiceImpl<SchoolInfoMapper, SchoolInfo> implements SchoolInfoService {

    @Autowired
    private SchoolInfoMapper schoolInfoMapper;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private TeacherInfoService teacherInfoService;
    @Autowired
    private TeamService teamService;
    @Autowired
    RedisUtils redisUtils;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveSchoolInfo(SchoolInfo schoolInfo) throws Exception {
        return super.save(schoolInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateSchoolInfo(SchoolInfo schoolInfo) throws Exception {
      if(schoolInfo.getMonitorTime()!=null&&schoolInfo.getMonitorTime().getTime()>0){
          redisUtils.del(CacheConsts.KINDERGARTENMONITORDELAYQUEUESTART+schoolInfo.getId());
          redisUtils.del(CacheConsts.KINDERGARTENMONITORDELAYQUEUESTOP+schoolInfo.getId());
      }
        redisUtils.del(CacheConsts.GETSCHOOLINFOLIST);
        return super.updateById(schoolInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteSchoolInfo(Long id) throws Exception {
        redisUtils.del(CacheConsts.GETSCHOOLINFOLIST);
        return super.removeById(id);
    }

    @Override
    public Paging<SchoolInfo> getSchoolInfoPageList(SchoolInfoPageParam schoolInfoPageParam) throws Exception {
        Page<SchoolInfo> page = new PageInfo<>(schoolInfoPageParam, OrderItem.desc(getLambdaColumn(SchoolInfo::getCreateTime)));
        LambdaQueryWrapper<SchoolInfo> wrapper = new LambdaQueryWrapper<>();
        IPage<SchoolInfo> iPage = schoolInfoMapper.selectPage(page, wrapper);
        return new Paging<SchoolInfo>(iPage);
    }

    @Override
    public SchoolInfo getByUserId(Long userId) {
        return getOne(new LambdaQueryWrapper<SchoolInfo>().eq(SchoolInfo::getUserId, userId));
    }

    @Override
    public SchoolPersonCountAo getSchoolPersonCount(Long id) {
        SchoolPersonCountAo schoolPersonCountAo = new SchoolPersonCountAo();
        int teacher = teacherInfoService.count(new LambdaQueryWrapper<TeacherInfo>().eq(TeacherInfo::getSchoolId, id));
        int boy = studentInfoService.count(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getSchoollId, id).eq(StudentInfo::getSex, 1));
        int gril = studentInfoService.count(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getSchoollId, id).eq(StudentInfo::getSex, 0));
        int team = teamService.count(new LambdaQueryWrapper<Team>().eq(Team::getSchoolId, id));
        schoolPersonCountAo.setBoy(boy).setGirl(gril).setTeacher(teacher).setTeam(team).setCount(gril + boy);
        return schoolPersonCountAo;
    }

    @Override
    public JSONArray getNavigationText(Long id) {
        TeacherInfo teacherInfo = teacherInfoService.getById(id);
        return teacherInfo.getNavigationText();
    }

    @Override
    public Boolean setNavigationText(Long id, JSONArray jsonArray) {
        TeacherInfo teacherInfo = teacherInfoService.getById(id);
        teacherInfo.setNavigationText(jsonArray);
        return teacherInfoService.saveOrUpdate(teacherInfo);
    }

    @Override
    //@Cacheable("getSchoolInfoList")
    public List<SchoolInfo> getSchoolInfoList() {
        Object o = redisUtils.get(CacheConsts.GETSCHOOLINFOLIST);
        List<SchoolInfo> schoolInfos = null;
        if (o != null) {
            schoolInfos = (List<SchoolInfo>) o;
        } else {
            schoolInfos = schoolInfoMapper.getSchoolInfoList();
            redisUtils.set(CacheConsts.GETSCHOOLINFOLIST, schoolInfos);
        }
        return schoolInfos;
    }

    @Override
    public JSONObject getSchollNameAndTeamName(Long teamId) {
        Team team = teamService.getById(teamId);
        SchoolInfo schoolInfo = getById(team.getSchoolId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("teamName",team.getFullName());
        jsonObject.put("SchoolName",schoolInfo.getName());
        jsonObject.put("SchoolLog",schoolInfo.getLogo());
        return jsonObject;
    }

}
