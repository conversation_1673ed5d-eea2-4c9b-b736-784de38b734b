package io.geekidea.springbootplus.using.arg;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * @desc:
 * @author: ZXB
 * @date: 2020/9/17 18:05
 */
@Component
@Data
public class SystemArg {
    @Value("${system.url}")
    private String domain_algorithm;

    @Value("${system.vsteamUrlPrefix}")
    private String vsteam_url_prefix;

    @Value("${system.microteamUrlPrefix}")
    private String microteam_url_prefix;
}
