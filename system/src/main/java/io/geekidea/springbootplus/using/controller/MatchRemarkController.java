package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.aop.annotation.TextCensorUserDefined;
import io.geekidea.springbootplus.using.entity.MatchRemark;
import io.geekidea.springbootplus.using.service.MatchRemarkService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.MatchRemarkPageParam;
import io.geekidea.springbootplus.framework.common.controller.BaseController;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.common.param.IdParam;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2023-08-12
 */
@Slf4j
@RestController
@RequestMapping("/using/matchRemark")
@Module("${cfg.module}")
@Api(value = "赛后点评", tags = {"赛后点评"})
public class MatchRemarkController extends BaseController {

    @Autowired
    private MatchRemarkService matchRemarkService;

    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class,notes = "返回点评id")
    @TextCensorUserDefined
    public ApiResult addMatchRemark(@Validated(Add.class) @RequestBody MatchRemark matchRemark) throws Exception {
        boolean flag = matchRemarkService.saveMatchRemark(matchRemark);
        return ApiResult.ok(matchRemark.getId());
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    @TextCensorUserDefined
    public ApiResult<Boolean> updateMatchRemark(@Validated(Update.class) @RequestBody MatchRemark matchRemark) throws Exception {
        boolean flag = matchRemarkService.updateMatchRemark(matchRemark);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{stuId}/{matchId}/{matchType}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteMatchRemark(@PathVariable("matchType") String matchType,@PathVariable("stuId") Long stuId,@PathVariable("matchId") Long matchId) throws Exception {
        boolean flag = matchRemarkService.deleteMatchRemark(matchType,stuId,matchId);
        return ApiResult.result(flag);
    }
}

