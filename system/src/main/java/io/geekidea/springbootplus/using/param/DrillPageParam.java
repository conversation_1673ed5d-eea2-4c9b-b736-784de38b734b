package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <pre>
 *  分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DrillPageParam分页参数")
public class DrillPageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;

    public void setStudentId(Long studentId) {
        if (studentId!=0l) {
            this.studentId = studentId;
        }else{
            this.studentId = -1l;
        }
    }

    @ApiModelProperty("学生id")
    private Long studentId;
    @ApiModelProperty("班级id")
    private Long teamId;
    @ApiModelProperty("0未结束 1已结束 -1全部")
    private Integer finish;
    @ApiModelProperty("是否只查询同步过的训练")
    private Boolean upload = false;
    @ApiModelProperty("是否学生创建 个人版个人训练需要传true")
    private Boolean isStu;
    @ApiModelProperty("课程类型id（跑动|篮球...）查询全部就不传 或者-1 2篮球 3足球 4 查询数字体育课")
    private Long courseId;
    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date befor;
    @ApiModelProperty("结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date after;
}
