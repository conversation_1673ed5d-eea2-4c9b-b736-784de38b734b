package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.using.entity.TacticsBoard;
import io.geekidea.springbootplus.using.service.TacticsBoardService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.TacticsBoardPageParam;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 战术板主表，存储战术板基本信息 控制器
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@RestController
@RequestMapping("/using/tacticsBoard")
@Module("${cfg.module}")
@Api(value = "战术板内容", tags = {"战术板内容"})
public class TacticsBoardController extends BaseController {

    @Autowired
    private TacticsBoardService tacticsBoardService;


    @PostMapping("/add")
    @OperationLog(name = "添加战术板内容基本信息", type = OperationLogType.ADD)
    @ApiOperation(value = "添加战术板内容基本信息", response = ApiResult.class)
    public ApiResult addTacticsBoard(@RequestBody TacticsBoard tacticsBoard) throws Exception {
        Long flag = tacticsBoardService.saveTacticsBoard(tacticsBoard);
        return ApiResult.ok(flag);
    }

    @PostMapping("/update")
    @OperationLog(name = "修改战术板内容基本信息", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改战术板内容基本信息", response = ApiResult.class)
    public ApiResult<Boolean> updateTacticsBoard(@Validated(Update.class) @RequestBody TacticsBoard tacticsBoard) throws Exception {
        boolean flag = tacticsBoardService.updateTacticsBoard(tacticsBoard);
        return ApiResult.result(flag);
    }

    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteTacticsBoard(@PathVariable("id") Long id) throws Exception {
        boolean flag = tacticsBoardService.deleteTacticsBoard(id);
        return ApiResult.result(flag);
    }


    @GetMapping("/info/{id}")
    @OperationLog(name = "战术板详情", type = OperationLogType.INFO)
    @ApiOperation(value = "战术板详情", response = TacticsBoard.class)
    public ApiResult<TacticsBoard> getTacticsBoard(@PathVariable("id") Long id) throws Exception {
        TacticsBoard tacticsBoard = tacticsBoardService.getTacticsBoardById(id);
        return ApiResult.ok(tacticsBoard);
    }


    @PostMapping("/getPageList")
    @OperationLog(name = "战术板分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "战术板分页列表", response = TacticsBoard.class)
    public ApiResult<Paging<TacticsBoard>> getTacticsBoardPageList(@Validated @RequestBody TacticsBoardPageParam tacticsBoardPageParam) throws Exception {
        Paging<TacticsBoard> paging = tacticsBoardService.getTacticsBoardPageList(tacticsBoardPageParam);
        return ApiResult.ok(paging);
    }

}

