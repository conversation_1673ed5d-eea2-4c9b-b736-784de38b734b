package io.geekidea.springbootplus.using.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.nio.charset.StandardCharsets;

@Data
@Accessors(chain = true)
@ApiModel(value = "DemoMacQrcodeAo")
public class DemoMacQrcodeAo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("MAC")
    private String mac;
    @ApiModelProperty("teambox名字")
    private String name;
    @ApiModelProperty("箱子序号")
    private String box;
    @ApiModelProperty("箱子内teambox序号")
    private Integer num;
    @ApiModelProperty("长编号")
    private String longNum;
    @ApiModelProperty("颜色")
    private Integer colour;
    @ApiModelProperty("qrCode")
    private String qrCode;
    @ApiModelProperty("1.标准版 2.专业版")
    private Integer hardType;
}
