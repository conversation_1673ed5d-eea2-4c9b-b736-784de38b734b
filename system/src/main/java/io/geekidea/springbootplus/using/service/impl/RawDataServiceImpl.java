package io.geekidea.springbootplus.using.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.geekidea.springbootplus.using.entity.RawData;
import io.geekidea.springbootplus.using.mapper.RawDataMapper;
import io.geekidea.springbootplus.using.service.RawDataService;
import io.geekidea.springbootplus.using.param.RawDataPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class RawDataServiceImpl extends BaseServiceImpl<RawDataMapper, RawData> implements RawDataService {

    @Autowired
    private RawDataMapper rawDataMapper;

    @Override
    public List<RawData> findOfMatch(String matchType,Long matchId) {
        return list(new QueryWrapper<RawData>().eq("match_type",matchType).eq("match_id", matchId));
    }

    @Override
    public List<RawData> findOfMatch(String matchType, Long matchId, Long teamId) {
        return list(new QueryWrapper<RawData>().eq("match_type",matchType).eq("team_id",teamId).eq("match_id", matchId));
    }

}
