package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.UserFavoriteTactics;
import io.geekidea.springbootplus.using.mapper.UserFavoriteTacticsMapper;
import io.geekidea.springbootplus.using.service.UserFavoriteTacticsService;
import io.geekidea.springbootplus.using.param.UserFavoriteTacticsPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 用户收藏战术板记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Slf4j
@Service
public class UserFavoriteTacticsServiceImpl extends BaseServiceImpl<UserFavoriteTacticsMapper, UserFavoriteTactics> implements UserFavoriteTacticsService {

    @Autowired
    private UserFavoriteTacticsMapper userFavoriteTacticsMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveUserFavoriteTactics(UserFavoriteTactics userFavoriteTactics) throws Exception {
        return super.saveOrUpdate(userFavoriteTactics);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateUserFavoriteTactics(UserFavoriteTactics userFavoriteTactics) throws Exception {
        return super.updateById(userFavoriteTactics);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteUserFavoriteTactics(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<UserFavoriteTactics> getUserFavoriteTacticsPageList(UserFavoriteTacticsPageParam userFavoriteTacticsPageParam) throws Exception {
        Page<UserFavoriteTactics> page = new PageInfo<>(userFavoriteTacticsPageParam, OrderItem.desc(getLambdaColumn(UserFavoriteTactics::getCreateTime)));
        LambdaQueryWrapper<UserFavoriteTactics> wrapper = new LambdaQueryWrapper<>();
        IPage<UserFavoriteTactics> iPage = userFavoriteTacticsMapper.selectPage(page, wrapper);
        return new Paging<UserFavoriteTactics>(iPage);
    }

}
