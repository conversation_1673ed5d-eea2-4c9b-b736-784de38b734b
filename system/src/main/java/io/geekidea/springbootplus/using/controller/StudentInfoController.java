package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.LogUtils;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.util.DateUtil;
import io.geekidea.springbootplus.framework.util.MD5Utils;
import io.geekidea.springbootplus.framework.util.MobileUtils;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.service.GameService;
import io.geekidea.springbootplus.using.service.StudentInfoService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.StudentInfoPageParam;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@RestController
@RequestMapping("/using/studentInfo")
@Module("teambox")
@Api(value = "学生信息", tags = {"学生信息"})
public class StudentInfoController extends BaseController {
    @Autowired
    private StudentInfoService studentInfoService;

    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class, notes = "错误code 20044 学号重复 20045 球衣重复 20035手机号重复")
    public ApiResult<Boolean> addStudentInfo(@RequestBody StudentInfo studentInfo) throws Exception {
        ApiCode apiCode = studentInfoService.onlyValidation(studentInfo.getPhone(), studentInfo.getCard(), studentInfo.getSchoollId());
        if (apiCode != null) {
            return ApiResult.result(apiCode);
        }
        apiCode = studentInfoService.onlyValidationShirt(studentInfo);
        if (apiCode != null) {
            return ApiResult.result(apiCode);
        }
        boolean flag = studentInfoService.saveStudentInfo(studentInfo);
        return ApiResult.result(flag);
    }

    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class, notes = "错误code 20044 学号重复 20045 篮球球衣重复 20035手机号重复 20051 足球球衣重复")
    public ApiResult<Boolean> updateStudentInfo(@RequestBody StudentInfo studentInfo) throws Exception {
        StudentInfo studentInfo1 = studentInfoService.getById(studentInfo.getId());
        ApiCode apiCode = null;
        if (studentInfo.getPhone() != null && !studentInfo.getPhone().equals(studentInfo1.getPhone())) {
            apiCode = studentInfoService.updaValidation(studentInfo.getPhone(), studentInfo.getId());
        } else if (studentInfo.getCard() != null && !studentInfo.getCard().equals(studentInfo1.getCard())) {
            apiCode = studentInfoService.onlyValidation(null, studentInfo.getCard(), studentInfo.getSchoollId());
        }
        if (apiCode != null) {
            return ApiResult.result(apiCode);
        } else {
            apiCode = studentInfoService.onlyValidationShirt(studentInfo);
            if (apiCode != null) {
                return ApiResult.result(apiCode);
            }
        }

        boolean flag = studentInfoService.updateStudentInfo(studentInfo);
        return ApiResult.result(flag);
    }

    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteStudentInfo(@PathVariable("id") Long id) throws Exception {
        boolean flag = studentInfoService.deleteStudentInfo(id);
        return ApiResult.ok(flag);
    }

    @PostMapping("/deletes")
    @OperationLog(name = "批量删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "批量删除", response = ApiResult.class, notes = "body传入学生id集合[{“id”:1}]")
    public ApiResult<Boolean> deleteStudentInfo(@RequestBody List<StudentInfo> studentInfos) throws Exception {
        boolean flag = studentInfoService.deleteStudentInfos(studentInfos.stream().map(StudentInfo::getId).collect(Collectors.toList()));
        return ApiResult.ok(flag);
    }

    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = StudentInfo.class)
    public ApiResult<StudentInfo> getStudentInfo(@PathVariable("id") Long id) throws Exception {
        StudentInfo studentInfo = studentInfoService.getStudentInfo(id);
        return ApiResult.ok(studentInfo);
    }

    @PostMapping("/getStudentInfoPageList")
    @OperationLog(name = "获取 班级下学生列表 类似PAD查看球鞋状态", type = OperationLogType.PAGE)
    @ApiOperation(value = "获取 班级下学生列表 类似PAD查看球鞋状态", response = StudentInfo.class, notes = "缺勤的学生列表也可以用 body参数 pageSorts和keyword可以不用传  drillId需要训练相关字段再传 返回是数组")
    public ApiResult<Object> getStudentInfoPageList(@Validated @RequestBody StudentInfoPageParam studentInfoPageParam) throws Exception {
        Paging<StudentInfo> paging = studentInfoService.getStudentInfoPageList(studentInfoPageParam);
        return ApiResult.ok(paging.getRecords());
    }


    @PostMapping("/getStudentInfoPageListByFieldGroup")
    @OperationLog(name = "获取训练的 分别两个分组的成员、所有成员", type = OperationLogType.PAGE)
    @ApiOperation(value = "获取训练的 分别两个分组的成员、所有成员", response = StudentInfo.class, notes = "[{\n" +
            "            \"groupName\": \"小组1\",\n" +
            "            \"groupId\": 1354,\n" +
            "            \"studentIds\": [参考getStudentInfoPageList]\n" +
            "}]")
    public ApiResult<Object> getStudentInfoPageListByFieldGroup(@Validated @RequestBody StudentInfoPageParam studentInfoPageParam) throws Exception {
        return ApiResult.ok(studentInfoService.getStudentInfoPageListByFieldGroup(studentInfoPageParam));
    }


    @GetMapping("/getStuHardDrillStatus/{drillId}/{studentId}/{teamId}")
    @ApiOperation(value = "获取某一场训练的某一学生的设备启动状态", response = ApiResult.class, notes = "")
    public ApiResult getStuHardDrillStatus(@PathVariable("drillId") Long drillId, @PathVariable("studentId") Long studentId, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(studentInfoService.getStuHardDrillStatus(drillId, studentId, teamId));
    }

    @PostMapping(value = "/uploadFile/{schoollId}/{teamId}")
    @OperationLog(name = "txt文件批量上传学生")
    @ApiOperation(value = "txt文件批量上传学生", response = JSONObject.class, notes = "格式 李四 下一条数据换行 txt类型")
    public ApiResult uploadFile(@RequestParam("file") MultipartFile multfile, @PathVariable("schoollId") Long schoollId, @PathVariable("teamId") Long teamId) {
        // 获取文件后缀名
        String suffix = multfile.getOriginalFilename().substring(multfile.getOriginalFilename().lastIndexOf("."));
        final File file;
        List<StudentInfo> studentInfos = new ArrayList<>();
        try {
            file = File.createTempFile(UUID.randomUUID().toString(), suffix);
            multfile.transferTo(file);
            FileInputStream fin = new FileInputStream(file);
            InputStreamReader reader = new InputStreamReader(fin);
            BufferedReader buffReader = new BufferedReader(reader);
            String strTmp = "";
            while ((strTmp = buffReader.readLine()) != null) {
                strTmp = strTmp.trim();
                StudentInfo studentInfo = new StudentInfo();
                studentInfo.setSchoollId(schoollId);
                studentInfo.setTeamId(teamId);
                studentInfo.setAge(10);
                studentInfo.setName(strTmp);
                studentInfo.setHeight(140.0);
                studentInfo.setWeight(80.0);
                studentInfos.add(studentInfo);
            }
            buffReader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        studentInfoService.saveOrUpdateBatch(studentInfos);
        return ApiResult.ok(studentInfos);
    }

    @PostMapping(value = "/addFileStu/{schoollId}/{teamId}")
    @OperationLog(value = "excel录入学生")
    @ApiOperation(value = "excel录入学生", response = JSONObject.class, notes = "excel格式下载查看  返回码：30000：学号重复  30001：手机号重复  30002：导入失败")
    @Transactional
    public ApiResult addFileStu(@RequestParam("file") MultipartFile multfile, @PathVariable("schoollId") Long schoollId, @PathVariable("teamId") Long teamId) {
        String fileName = multfile.getOriginalFilename();
        String prefix = fileName.substring(fileName.lastIndexOf("."));
        ApiCode apiCode = null;
        int size = 0;
        try {
            File file = File.createTempFile(fileName, prefix);
            multfile.transferTo(file);
            InputStream inputStream = new FileInputStream(file);
            if (prefix.equals(".xlsx")) {
                try {
                    XSSFWorkbook sheets = new XSSFWorkbook(inputStream);
                    XSSFSheet sheet = sheets.getSheetAt(0);
                    for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
                        StudentInfo studentInfo = new StudentInfo();
                        XSSFRow row = sheet.getRow(i);
                        if (row == null) {
                            continue;
                        }
                        Map map = new HashMap();
                        for (int index = 0; index < row.getLastCellNum(); index++) {
                            XSSFCell cell = row.getCell(index);
                            if (cell == null) {
                                continue;
                            }
                            cell.setCellType(CellType.STRING);
                            if (cell.getStringCellValue().equals("")) {
                                map.put(index + 1, null);
                            } else {
                                map.put(index + 1, cell.getStringCellValue());
                            }
                        }
                        if (map.get(1)==null) {
                            continue;
                        }
                        studentInfo.setName(map.get(1).toString());
                        studentInfo.setCard(map.get(2).toString());
                        if (studentInfo.getCard() != null) {
                            apiCode = studentInfoService.onlyValidation(null, studentInfo.getCard(), schoollId);
                            if (apiCode != null) {
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return ApiResult.fail(30000, map.get(4) + studentInfo.getName() + "的学号" + studentInfo.getCard() + "重复");
                            }
                        }
                        Date da = HSSFDateUtil.getJavaDate(Double.parseDouble(map.get(3).toString()));
                        studentInfo.setBirthday(da);
                        studentInfo.setHeight(Double.valueOf(map.get(5).toString()));
                        studentInfo.setWeight(Double.valueOf(map.get(6).toString()));
                        studentInfo.setPhone(map.get(7).toString().trim());
                        if (studentInfo.getPhone() != null) {
                            String regex = "^\\d{11}$";
                            Pattern p = Pattern.compile(regex);
                            Matcher m = p.matcher(studentInfo.getPhone());
                            boolean isMath = m.matches();
                            if (!isMath) {
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return ApiResult.fail(30002, studentInfo.getPhone() + "手机号格式错误");
                            }
                            apiCode = studentInfoService.onlyValidation(studentInfo.getPhone(), null, schoollId);
                            if (apiCode != null) {
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return ApiResult.fail(30001, map.get(4) + studentInfo.getName() + "的手机号" + studentInfo.getPhone() + "重复");
                            }
                        }
                        studentInfo.setPasswd(MD5Utils.getSaltMD5(map.get(8).toString()));
                        if (map.get(9).toString().trim().equals("男")) {
                            studentInfo.setSex(1);
                        } else {
                            studentInfo.setSex(0);
                        }
                        Date date = new Date();
                        Date bridate = new Date();
                        try {
                            if (map.get(10) != null) {
                                String join = map.get(10).toString();
                                String bri = map.get(3).toString();
                                if (join.length() < 5) {
                                    join = join + "-01-01";
                                    date = new SimpleDateFormat("yyyy-MM-dd").parse(join);
                                } else {
                                    date = HSSFDateUtil.getJavaDate(Double.parseDouble(map.get(10).toString()));
                                }
                                if (bri.length() < 5) {
                                    bri = bri + "-01-01";
                                    bridate = new SimpleDateFormat("yyyy-MM-dd").parse(bri);
                                } else {
                                    bridate = HSSFDateUtil.getJavaDate(Double.parseDouble(map.get(3).toString()));
                                }
                            }
                        } catch (ParseException e) {
                            e.printStackTrace();
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return ApiResult.fail(30002, "导入失败");
                        }
                        studentInfo.setJoinDate(date);
                        studentInfo.setBirthday(bridate);
                        Integer fs = null;
                        Integer s = null;
                        if (map.get(11)!=null) {
                            studentInfo.setIntroduction(map.get(11).toString());
                        }
                        if (map.get(12) != null && map.get(12).toString().length() > 0) {
                            fs = Integer.valueOf(map.get(12).toString());
                        }
                        if (map.get(13) != null && map.get(13).toString().length() > 0) {
                            s = Integer.valueOf(map.get(13).toString());
                        }
                        studentInfo.setFootballShirt(fs);
                        studentInfo.setShirt(s);
                        studentInfo.setTeamId(teamId);
                        studentInfo.setSchoollId(schoollId);
                        studentInfo.setAge(DateUtil.getAge(studentInfo.getBirthday()));
                        studentInfoService.saveOrUpdate(studentInfo);
                        size++;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return ApiResult.fail(30003, "文件出错，请另存文件");
                }
            } else {
                try {
                    HSSFWorkbook sheets = new HSSFWorkbook(inputStream);
                    HSSFSheet sheet = sheets.getSheetAt(0);
                    for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
                        StudentInfo studentInfo = new StudentInfo();
                        HSSFRow row = sheet.getRow(i);
                        if (row == null) {
                            continue;
                        }
                        Map map = new HashMap();
                        for (int index = 0; index < row.getLastCellNum(); index++) {
                            HSSFCell cell = row.getCell(index);
                            cell.setCellType(CellType.STRING);
                            if (cell.getStringCellValue().equals("")) {
                                map.put(index + 1, null);
                            } else {
                                map.put(index + 1, cell.getStringCellValue());
                            }
                        }
                        if (map.size() == 0) {
                            continue;
                        }
                        studentInfo.setName(map.get(1).toString());
                        studentInfo.setCard(map.get(2).toString());
                        if (studentInfo.getCard() != null) {
                            apiCode = studentInfoService.onlyValidation(null, studentInfo.getCard(), schoollId);
                            if (apiCode != null) {
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return ApiResult.fail(30000, map.get(4) + studentInfo.getName() + "的学号" + studentInfo.getCard() + "重复");
                            }
                        }
                        Date da = HSSFDateUtil.getJavaDate(Double.parseDouble(map.get(3).toString()));
                        studentInfo.setBirthday(da);
                        studentInfo.setHeight(Double.valueOf(map.get(5).toString()));
                        studentInfo.setWeight(Double.valueOf(map.get(6).toString()));
                        studentInfo.setPhone(map.get(7).toString());
                        if (studentInfo.getPhone() != null) {
                            String regex = "^\\d{11}$";
                            Pattern p = Pattern.compile(regex);
                            Matcher m = p.matcher(studentInfo.getPhone());
                            boolean isMath = m.matches();
                            if (!isMath) {
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return ApiResult.fail(30002, studentInfo.getPhone() + "手机号格式错误");
                            }

                            apiCode = studentInfoService.onlyValidation(studentInfo.getPhone(), null, schoollId);
                            if (apiCode != null) {
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return ApiResult.fail(30001, map.get(4) + studentInfo.getName() + "的手机号" + studentInfo.getPhone() + "重复");
                            }
                        }
                        studentInfo.setPasswd(MD5Utils.getSaltMD5(map.get(8).toString()));
                        if (map.get(9).toString().trim().equals("男")) {
                            studentInfo.setSex(1);
                        } else {
                            studentInfo.setSex(0);
                        }
                        Date date = new Date();
                        Date bridate = new Date();
                        try {
                            if (map.get(10) != null) {
                                String join = map.get(10).toString();
                                String bri = map.get(3).toString();
                                if (join.length() < 5) {
                                    join = join + "-01-01";
                                    date = new SimpleDateFormat("yyyy-MM-dd").parse(join);
                                } else {
                                    date = HSSFDateUtil.getJavaDate(Double.parseDouble(map.get(10).toString()));
                                }
                                if (bri.length() < 5) {
                                    bri = bri + "-01-01";
                                    bridate = new SimpleDateFormat("yyyy-MM-dd").parse(bri);
                                } else {
                                    bridate = HSSFDateUtil.getJavaDate(Double.parseDouble(map.get(3).toString()));
                                }
                            }
                        } catch (ParseException e) {
                            e.printStackTrace();
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return ApiResult.fail(30002, "导入失败");
                        }
                        studentInfo.setJoinDate(date);
                        studentInfo.setBirthday(bridate);
                        Integer fs = null;
                        Integer s = null;
                        if (map.size() > 10) {
                            studentInfo.setIntroduction(map.get(11).toString());
                        }
                        if (map.get(12) != null && map.get(12).toString().length() > 0) {
                            fs = Integer.valueOf(map.get(12).toString());
                        }
                        if (map.get(13) != null && map.get(13).toString().length() > 0) {
                            s = Integer.valueOf(map.get(13).toString());
                        }
                        studentInfo.setFootballShirt(fs);
                        studentInfo.setShirt(s);
                        studentInfo.setTeamId(teamId);
                        studentInfo.setSchoollId(schoollId);
                        studentInfo.setAge(DateUtil.getAge(studentInfo.getBirthday()));
                        studentInfoService.saveOrUpdate(studentInfo);
                        size++;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return ApiResult.fail(30003, "文件出错，请另存文件");
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ApiResult.fail(30002, "导入失败");
        }

        return ApiResult.ok(size);
    }

    /**
     * 学员登录
     *
     * @return
     */
    @PostMapping("/loginStu")
    @OperationLog(name = "录入设备系统学员登录")
    @ApiOperation(value = "录入设备系统学员登录", response = ApiResult.class, notes = "参数  phone：用户名   passwd：密码 deviceTokens:友盟唯一id \n 错误码 999：用户名不存在  1000：密码错误")
    public ApiResult loginStu(@RequestBody StudentInfo userApp) {
        if (!StringUtils.isEmpty(userApp.getPhone()) && !StringUtils.isEmpty(userApp.getPasswd())) {
            StudentInfo userApp1 = studentInfoService.examineName(userApp);
            if (userApp1 == null) {
                return ApiResult.result(ApiCode.USERNAME_ERROR);
            }
            StudentInfo userApp2 = studentInfoService.loginStu(userApp);
            if (userApp2 == null) {
                return ApiResult.result(ApiCode.PASSWORD_ERROR);
            }
            userApp2.setToken(genToken());
            putCurrent(userApp2.getToken(), userApp2);
            return ApiResult.ok(userApp2);
        }
        return ApiResult.result(ApiCode.USERNAME_PASSWORD_ISNULL);
    }
}

