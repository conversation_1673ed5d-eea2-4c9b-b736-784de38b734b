package io.geekidea.springbootplus.using.vo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 球队信息VO
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "TeamAppVO球队信息")
public class TeamAppVO {

    @ApiModelProperty("球队ID")
    private Long id;

    @ApiModelProperty("球队logo")
    private String logo;

    @ApiModelProperty("球队全称")
    private String fullName;

    @ApiModelProperty("球队简称")
    private String shortName;

    @ApiModelProperty("成立时间")
    private Date formedTime;

    @ApiModelProperty("属性")
    private String property;

    @ApiModelProperty("联系人")
    private String contacts;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("简介")
    private String introduction;

    @ApiModelProperty("区域信息")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject region;

    @ApiModelProperty("球场")
    private String court;

    @ApiModelProperty("运动类型：2篮球 3足球 4数字体育课")
    private Long courseId;

    @ApiModelProperty("创建该球队的用户id")
    private Long createUserId;

    @ApiModelProperty("队长用户ID")
    private Long captainUserId;

    @ApiModelProperty("是否主队：true-主队，false-客队")
    private Boolean isHomeTeam;

    @ApiModelProperty("成员数量")
    private Integer memberCount;

    @ApiModelProperty("是否为当前用户创建")
    private Boolean isCreatedByMe;

    @ApiModelProperty("当前用户是否为队长")
    private Boolean isCaptain;

    @ApiModelProperty("当前用户是否为成员")
    private Boolean isMember;


    @ApiModelProperty("赛事总场次")
    private Integer totalMatches;

    @ApiModelProperty("胜场数")
    private Integer winCount;

    @ApiModelProperty("负场数")
    private Integer loseCount;

    @ApiModelProperty("平场数")
    private Integer drawCount;

    @ApiModelProperty("创建时间")
    private Date createTime;



}
