package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 *
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FieldData对象")
@TableName(autoResultMap = true)
public class FieldData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("编号")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("所属小组id")
    private Long groupId;

    @ApiModelProperty("场记id")
    private Long fieldId;

    @ApiModelProperty("学员ID")
    private Long stuId;

    @ApiModelProperty("篮球数据json 格式: {\"walk\": 0, \"assist\": 0, \"snatch\": 0, \"outBall\": 0, \"dribbler\": 0, \"headFoul\": 0, \"hookFoul\": 0, \"walkBall\": 0, \"backboard\": 0, \"blockShot\": 0, \"onePointer\": 0, \"pullPeople\": 0, \"pushPepole\": 0, \"treeBasket\": 0, \"twoPointer\": 0, \"flyingElbow\": 0, \"penaltyExit\": 0, \"illegalHands\": 0, \"threePointer\": 0, \"illegalAttack\": 0, \"illegalDefense\": 0, \"illegalDribble\": 0, \"threeViolation\": 0, \"intentionalBall\": 0, \"technicalfoul\": 0, \"violationfoul\": 0, \"comeback\": 0,\"amazing\": 0, \"nicepass\":0, \"frontback\":0, \"afterback\":0, \"nullone\":0 \"nulltwo\":0, \"nullthree\":0,\"dunk\":0, \"helpball\":0 }" +
            "\n 足球数据json格式: {\"goal\":0,\"orthogonality\":0,\"assisting\":0,\"surpass\":0,\"pointsphere\":0,\"cornerkick\":0,\"freekick\":0,\"threatball\":0,\"heading\":0,\"raise\":0,\"preemption\":0,\"redcard\":0,\"shooting\":0,\"rules\":0,\"offside\":0,\"puking\":0,\"yellowcard\":0,\"owngoal\":0,\"waveshooting\":0,\"kickair\":0,\"stoperror\":0,\"defensiveerror\":0,\"passingerror\":0,\"wonderful\":0,\"excitingShot\":0,\"excitingSyeals\":0,\"longpass\":0,\"headgoal\":0,\"headclear\":0,\"clearance\":0,\"rescue\":0,\"pointMaking\":0,\"fatalerror\":0,\"wonderfulstop\":0,\"shoot\":0,\"threatenshoot\":0,\"scoring\":0,\"seriouserror\":0}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject dataMap;

    @ApiModelProperty("APP是否删除")
    private Boolean appDel;

    @TableField(exist = false)
    private String upMap;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty("隐藏的数据json")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject hideMap;

    @TableField(exist = false)
    private String onMap;

    @ApiModelProperty("场记名称（用于显示）")
    @TableField(exist = false)
    private String name;

    @ApiModelProperty("所属小组名称（用于显示）")
    @TableField(exist = false)
    private String groupName;

    @ApiModelProperty("所属小组分数（用于显示）")
    @TableField(exist = false)
    private String groupFraction;

    @ApiModelProperty("训练对战小组集合（用于显示）")
    @TableField(exist = false)
    private List<FieldGroup> fieldGroupList;

    @ApiModelProperty("学员头像（用于显示）")
    @TableField(exist = false)
    private String image;

    @ApiModelProperty("学员名称（用于显示）")
    @TableField(exist = false)
    private String stuName;

    @ApiModelProperty("球衣号码（用于显示）")
    @TableField(exist = false)
    private Integer shirt;

    @ApiModelProperty("足球衣号码（用于显示）")
    @TableField(exist = false)
    private Integer footballShirt;


    @ApiModelProperty("设备号")
    @TableField(exist = false)
    private Integer hardNum;

    @ApiModelProperty("场记类型（1：足球  3：篮球）")
    @TableField(exist = false)
    private Integer type;

}
