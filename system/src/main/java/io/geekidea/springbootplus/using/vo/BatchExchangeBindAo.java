package io.geekidea.springbootplus.using.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BatchExchangeBindAo")
public class BatchExchangeBindAo {
    @ApiModelProperty("设备组id")
    private Long groupId;
    @ApiModelProperty("被换绑的学生id（有设备的）")
    private Long studentId;
    @ApiModelProperty("要换绑的学生id（没设备的 --如果根据编号来换绑exchangeStuId传-1）")
    private Long exchangeStuId;
    @ApiModelProperty("要换绑的编号 没编号时传-1")
    private Integer exchangeNum;
    @ApiModelProperty("拆分模式情况，要换绑的设备已经被其他班级绑定 继续绑定 给true 其他情况给false")
    private Boolean ct;
}
