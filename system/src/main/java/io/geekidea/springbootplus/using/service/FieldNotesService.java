package io.geekidea.springbootplus.using.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.geekidea.springbootplus.using.entity.FieldData;
import io.geekidea.springbootplus.using.entity.FieldNotes;
import io.geekidea.springbootplus.using.param.FieldNotesPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import org.json.JSONObject;

import java.lang.reflect.Field;
import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
public interface FieldNotesService extends BaseService<FieldNotes> {

    /**
     * 保存
     *
     * @param fieldNotes
     * @return
     * @throws Exception
     */
    boolean saveFieldNotes(FieldNotes fieldNotes) throws Exception;

    /**
     * 复制
     * @param id
     * @return
     */
    boolean copyFieldNotes(Long id);

    /**
     * 修改
     *
     * @param fieldNotes
     * @return
     * @throws Exception
     */
    boolean updateFieldNotes(FieldNotes fieldNotes) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteFieldNotes(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param fieldNotesQueryParam
     * @return
     * @throws Exception
     */
    Paging<FieldNotes> getFieldNotesPageList(FieldNotesPageParam fieldNotesPageParam) throws Exception;

    List<FieldNotes> getPageList(FieldNotesPageParam fieldNotesPageParam);


    /**
     * 根据创建时间排序场记信息
     * @return
     */
    List<FieldNotes> getFieldNotesByCreate();

    /**
     * 小组
     * @param fieldNotesPageParam
     * @return
     */
    List<FieldNotes> getGroupField(FieldNotesPageParam fieldNotesPageParam);

    /**
     * 根据id获取小组信息
     * @param id
     * @return
     */
    FieldNotes getGroupById(Long id);

    /**
     * 快速场记
     * @param fieldNotes
     * @return
     */
    FieldNotes fieldFast(FieldNotes fieldNotes,String groupName1,String groupName2);

    /**
     * 场记通知学生
     * @param message
     */
    void sendFieldNotesByStudent(JSONObject message);

     void sendFieldNotesByUserApp(org.json.JSONObject message);

    FieldNotesPageParam createFieldNotesPageParam(Long userId, String matchType, Long matchId, Long pageIndex, Long pageSize);

    Long hitRate(FieldData fieldData);
}
