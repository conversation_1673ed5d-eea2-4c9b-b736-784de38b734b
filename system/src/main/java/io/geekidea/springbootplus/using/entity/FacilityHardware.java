package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FacilityHardware对象")
public class FacilityHardware extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("设备组id")
    private Long groupId;

    @NotBlank(message = "mac码不能为空")
    @ApiModelProperty("mac码")
    private String mac;

    @ApiModelProperty("箱子编号")
    private String box;

    @NotNull(message = "编号不能为空")
    @ApiModelProperty("编号")
    private Integer num;

    @ApiModelProperty("设备名")
    private String name;

    @ApiModelProperty("设备版本")
    private String version;

    @ApiModelProperty("电量")
    private Integer battery;

    @ApiModelProperty("电量最近上传时间戳")
    private Date batteryTime;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
