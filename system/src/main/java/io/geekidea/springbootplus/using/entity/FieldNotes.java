package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FieldNotes对象")
public class FieldNotes extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "场记id不能为空", groups = {Update.class})
    @ApiModelProperty("场记id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("训练or比赛id")
    private Long matchId;

    @ApiModelProperty("DRILL||GAME")
    private String matchType;

    @ApiModelProperty("小组 修改时从左（主队第一个）到右（客队第二个）按顺序提交")
    @TableField(exist = false)
    private List<FieldGroup> group;

    @ApiModelProperty("场记名称")
    private String name;

    @ApiModelProperty("场记类型（1 足球  3 篮球）")
    private Integer type;

    @ApiModelProperty("是否完成（0 未完成 1 完成）")
    private Integer finish;

    @ApiModelProperty("图片id")
    private Integer iconId;

    @ApiModelProperty("图片地址")
    @TableField(exist = false)
    private String iconUrl;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty("已进行时间 毫秒")
    @TableField(exist = false)
    private Long runningTime;

    @ApiModelProperty("学员id(如果是学员创建则需要传id)")
    @TableField(exist = false)
    private Long studentId;
}
