package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SchoolInfo对象")
public class SchoolInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("学校名称")
    private String name;

    @ApiModelProperty("学校logo")
    private String logo;

    @ApiModelProperty("学校电话")
    private String phone;

    @ApiModelProperty("公众号照片")
    private String pub;

    @ApiModelProperty("学校简介")
    private String introduction;

    @ApiModelProperty("监测开始时间")
    private Date monitorTime;

    @ApiModelProperty("监测上传的时间")
    private Date uploadTime;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
