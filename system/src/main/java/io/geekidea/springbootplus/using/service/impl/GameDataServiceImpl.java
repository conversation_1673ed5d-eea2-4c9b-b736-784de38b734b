package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.SerializationUtils;
import io.geekidea.springbootplus.using.arg.SystemArg;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.enums.CurveEnum;
import io.geekidea.springbootplus.using.param.DrillExtraPageParam;
import io.geekidea.springbootplus.using.param.FieldDataPageParam;
import io.geekidea.springbootplus.using.param.FieldNotesPageParam;
import io.geekidea.springbootplus.using.param.GameExtraPageParam;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.vo.*;
import lombok.Synchronized;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class GameDataServiceImpl implements GameDataService {
    @Autowired
    DrillService drillService;
    @Autowired
    GameService gameService;
    @Autowired
    private GameStudentService gameStudentService;
    @Autowired
    private RawDataService rawDataService;
    @Autowired
    private PersonDataService personDataService;
    @Autowired
    private TeamDataService teamDataService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private TransparentMessageService transparentMessageService;
    @Autowired
    private SystemArg systemArg;
    @Autowired
    private TeamService teamService;
    @Autowired
    private TeamAppService teamAppService;
    @Autowired
    private SchoolInfoService schoolInfoService;
    @Autowired
    private DelayQueueService delayQueueService;
    @Autowired
    private GameExtraService gameExtraService;
    @Autowired
    private GameExtraGroupService gameExtraGroupService;
    @Autowired
    private FieldNotesService fieldNotesService;
    @Autowired
    private FieldGroupService fieldGroupService;
    @Autowired
    private FieldDataService fieldDataService;
    @Autowired
    private MatchHardwareService matchHardwareService;
    @Autowired
    private MatchHardwareAppService matchHardwareAppService;
    @Autowired
    private UserAppService userAppService;
    @Autowired
    private MatchRemarkService matchRemarkService;
    @Autowired
    private ProDataService proDataService;
    @Autowired
    private TeamUserAppService teamUserAppService;

    @Override
    @Transactional
    public boolean uploadData(String strJSONArray, Long matchId, String matchType) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        //整合body
        JSONArray jsonArray = settleUploadBody(strJSONArray, matchId, matchType);
        Long teamId = jsonArray.getJSONObject(0).getLong("teamId");
        JSONObject o = getTeamExerciseStartAndStopTime(matchId);
        String origin = dateFormat.format(new Date(o.getLong("origin")));
        String destination = dateFormat.format(new Date(o.getLong("destination")));

        //发送计算请求
        RestTemplate restTemplate = new RestTemplate();
        String url = systemArg.getDomain_algorithm() + "?origin=" + origin + "&destination=" + destination;
        HttpEntity<JSONArray> entity = new HttpEntity<>(jsonArray);
        ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.POST, entity, JSONObject.class);
        if (!response.getBody().getBoolean("success")) {
            throw new IllegalArgumentException("数据上传失败");
        }
        //存个人数据
        if (!savePersonData(response.getBody().getJSONObject("data"), teamId, matchId, matchType)) {
            throw new IllegalArgumentException("数据上传失败");
        }
        //存团队数据
        if (teamId != 0 && !saveTeamData(response.getBody().getJSONObject("data"), teamId, matchId, matchType)) {
            throw new IllegalArgumentException("数据上传失败");
        }
        //proDataService.getComprehensiveScoreRanking(teamId, matchId, "GAME", 0l, true, new JSONObject());
        return true;
    }

    @Override
    public Object getPlayerData(Long matchId, Long studentId, Long teamId, String matchType) {
        Game game = gameService.getById(matchId);
        if (studentId == null||studentId==-1) {
            List<PersonData> personDataList = personDataService.findOfMatchId(matchId, teamId, matchType);
            TeamData teamData = teamDataService.findOfTeam(matchId, teamId, matchType);
            TeamDataAo teamDataAo = new TeamDataAo();
            teamDataAo.setGameInfo(game);
            if (teamData == null) {
                calculateTeamAvg(null, teamDataAo, null);
                return teamDataAo;
            }
            TeamApp team = teamAppService.getTeamApp(teamId);
            //    SchoolInfo schoolInfo = schoolInfoService.getById(team.getSchoolId());
            teamDataAo.setTeamId(teamData.getTeamId());
            teamDataAo.setMatchId(teamData.getMatchId());
            teamDataAo.setMatchType(teamData.getMatchType());
            teamDataAo.setCurves(teamData.getCurves());
            teamDataAo.setExerciseTime(teamData.getExerciseTime());
            teamDataAo.setFullName(team.getFullName());
            teamDataAo.setShortName(team.getShortName());
            teamDataAo.setLogo(team.getLogo());
/*            teamDataAo.setSchoolLogo(schoolInfo.getLogo());
            teamDataAo.setSchoolPub(schoolInfo.getPub());
            teamDataAo.setSchoolName(schoolInfo.getName());*/
            return calculateTeamData(teamDataAo, personDataList, matchType);
        }
        PersonData personData = personDataService.findOfMatchIdAndStudentId(matchType, matchId, studentId);
        if (personData == null) {
            JSONObject data = new JSONObject();
            data.put("gameInfo", game);
            if(game!=null) {
                data.put("matchId", game.getId());
            }
            return data;
        }
        PersonDataAo personDataAo = new PersonDataAo();
        BeanUtils.copyProperties(personData, personDataAo);
        List<PersonData> personDataList = personDataService.list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, matchType).eq(PersonData::getMatchId, game.getId()));
        calculatePersonData(personDataAo, personDataList, matchType);
        if (!game.getApp()) {
            personDataAo.setStudentInfo(studentInfoService.getById(studentId));
            Team team = teamService.getById(personData.getTeamId());
            if (team != null) {
                SchoolInfo schoolInfo = schoolInfoService.getById(team.getSchoolId());
                personDataAo.setMatchId(personData.getMatchId());
                personDataAo.setSchoolLogo(schoolInfo.getLogo());
                personDataAo.setSchoolPub(schoolInfo.getPub());
                personDataAo.setSchoolName(schoolInfo.getName());
                personDataAo.setFullName(team.getFullName());
                personDataAo.setShortName(team.getShortName());
            }
        } else {
            personDataAo.setUserApp(userAppService.getById(studentId));
        }
        personDataAo.setMatchRemark(matchRemarkService.getMatchRemark("GAME", personDataAo.getMatchId(), personDataAo.getStudentId()));

        personData.setAppLook(true);
        personDataService.saveOrUpdate(personData);
        return personDataAo;
    }

    @Override
    public Object getPlayerDataTuoNiao(Long matchId, Long studentId, Long teamId) {
        Game game = gameService.getById(matchId);
        if (studentId == null) {
            List<PersonData> personDataList = personDataService.findOfMatchId(matchId, teamId, "GAME");
            TeamData teamData = teamDataService.findOfTeam(matchId, teamId, "GAME");
            TeamDataAo teamDataAo = new TeamDataAo();
            TeamDataTuoNiaoAo teamDataTuoNiaoAo = new TeamDataTuoNiaoAo();
            teamDataAo.setGameInfo(game);
            if (teamData == null) {
                //calculateTeamAvg(null, teamDataAo, null);
                return null;
            }
            teamDataAo.setTeamId(teamData.getTeamId());
            teamDataAo.setMatchId(teamData.getMatchId());
            teamDataAo.setMatchType(teamData.getMatchType());
            teamDataAo.setCurves(teamData.getCurves());
            teamDataAo.setExerciseTime(teamData.getExerciseTime());
            calculateTeamDataTuoNiao(teamDataAo, personDataList, "GAME");
            BeanUtils.copyProperties(teamDataAo, teamDataTuoNiaoAo);
            return teamDataTuoNiaoAo;
        }
        PersonData personData = personDataService.findOfMatchIdAndStudentId("GAME", matchId, studentId);
        if (personData == null) {
            return null;
        }
        PersonDataAo personDataAo = new PersonDataAo();
        PersonDataTuoNiaoAo personDataTuoNiaoAo = new PersonDataTuoNiaoAo();
        BeanUtils.copyProperties(personData, personDataAo);
        List<PersonData> personDataList = personDataService.list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, "GAME").eq(PersonData::getMatchId, game.getId()));
        calculatePersonDataTuoNiao(personDataAo, personDataList, "GAME");
        BeanUtils.copyProperties(personDataAo, personDataTuoNiaoAo);
        return personDataTuoNiaoAo;
    }

    @Override
    public Object getPhysicalAnalysis(Long matchId, Long teamId, String matchType) {
        Game game = gameService.getById(matchId);
        JSONObject object = new JSONObject();
        List<JSONObject> list = new ArrayList<>();
        List<PersonData> personDatas = personDataService.findOfMatchId(matchId, teamId, "GAME").parallelStream().sorted((e1, e2) ->
                e2.getCalorie() - e1.getCalorie()
        ).collect(Collectors.toList());
        for (PersonData person : personDatas) {
            JSONObject o = new JSONObject();
            if (game.getApp()) {
                UserApp studentInfo = userAppService.getById(person.getStudentId());
                o.put("headImg", studentInfo.getHeadImg());
                o.put("name", studentInfo.getName());
            } else {
                StudentInfo studentInfo = studentInfoService.getById(person.getStudentId());
                o.put("headImg", studentInfo.getHeadImg());
                o.put("name", studentInfo.getName());
            }
            o.put("calorie", person.getCalorie());
            list.add(o);
        }
        object.put("calories", list);
        object.put("avgCalorie", list.stream().mapToDouble(e -> e.getInteger("calorie")).average().orElse(0.0));
        return object;
    }

    @Override
    public JSONArray moveDistanceRanking(Long matchId, String rankingKey, List<PersonData> personDatas, Long teamId, String matchType) {
        if (personDatas == null) {
            personDatas = personDataService.findOfMatchId(matchId, teamId, "GAME");
        }
        JSONArray jsonArray = new JSONArray();
        Game game = gameService.getById(matchId);
        List<PersonData> rankingData = new ArrayList<>();
        switch (rankingKey) {
            case "wholeMoveDistance":
                //总跑动 (m)
                rankingData = personDatas.stream().sorted(Comparator.comparingInt(PersonData::getRunDistance).reversed()).collect(Collectors.toList());
                break;
            case "maxSprintSpeed":
                // 最高冲刺速度 (m)
                rankingData = personDatas.stream().sorted(Comparator.comparingDouble(PersonData::getMaxSprintSpeed).reversed()).collect(Collectors.toList());
                break;
            case "maxCarrySpeed":
                // 最高带球速度 (km/h)
                // rankingData = personDatas.stream().sorted(Comparator.comparingDouble(PersonData::getMaxBallSpeed).reversed()).collect(Collectors.toList());
                break;
            case "highMoveDistance":
                //高速跑动 (m)
                jsonArray = sortedMoveDistance(personDatas, "SUP", "distance");
                break;
            case "midMoveDistance":
                //中速跑动 (m)
                jsonArray = sortedMoveDistance(personDatas, "MID", "distance");
                break;
            case "lowMoveDistance":
                // 低速跑动 (m)
                jsonArray = sortedMoveDistance(personDatas, "LOW", "distance");
                break;
            case "carryDistance":
                // 总带球 (m)
                jsonArray = sortedMoveDistance(personDatas, "", "ballDistance");
                break;
            case "highCarryDistance":
                // 高速带球 (m)
                jsonArray = sortedMoveDistance(personDatas, "HIGH", "ballDistance");
                break;
            case "midCarryDistance":
                // 中速带球 (m)
                jsonArray = sortedMoveDistance(personDatas, "MID", "ballDistance");
                break;
            case "lowCarryDistance":
                // 低速带球 (m)
                jsonArray = sortedMoveDistance(personDatas, "LOW", "ballDistance");
                break;
        }
        if (jsonArray.size() == 0) {
            for (PersonData e : rankingData) {
                JSONObject jsonObject = new JSONObject();
                String name = "";
                String headImg = "";
                Boolean isNull = false;
                if (game.getApp()) {
                    UserApp stu = userAppService.getById(e.getStudentId());
                    if (stu != null) {
                        name = stu.getName();
                        headImg = stu.getHeadImg();
                        isNull = true;
                    }
                } else {
                    StudentInfo stu = studentInfoService.getById(e.getStudentId());
                    if (stu != null) {
                        name = stu.getName();
                        headImg = stu.getHeadImg();
                        isNull = true;
                    }
                }

                if (isNull != null) {
                    jsonObject.put("name", name);
                    jsonObject.put("headImg", headImg);
                    jsonObject.put("id", e.getStudentId());
                    if ("wholeMoveDistance".equals(rankingKey)) {
                        jsonObject.put("value", e.getRunDistance());
                    } else if ("maxSprintSpeed".equals(rankingKey)) {
                        jsonObject.put("value", e.getMaxSprintSpeed());
                    } else if ("maxCarrySpeed".equals(rankingKey)) {
                        //  jsonObject.put("value", e.getMaxBallSpeed());
                    }
                    jsonArray.add(jsonObject);
                }
            }
        }
        return jsonArray;
    }

    public JSONArray sortedMoveDistance(List<PersonData> personDatas, String speed, String valueKey) {
        JSONArray jsonArray = new JSONArray();
        Game game = gameService.getById(personDatas.get(0).getMatchId());

        List<PersonData> rankingData = personDatas.stream().sorted((e1, e2) -> {
            int v1 = e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals(speed) || speed.trim().length() == 0).mapToInt(e -> ((JSONObject) e).getInteger(valueKey)).sum();
            int v2 = e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals(speed) || speed.trim().length() == 0).mapToInt(e -> ((JSONObject) e).getInteger(valueKey)).sum();
            if (speed.equals("MID")) {
                v1 += e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("HIGH") || speed.trim().length() == 0).mapToInt(e -> ((JSONObject) e).getInteger(valueKey)).sum();
                v1 += e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("EXCEED") || speed.trim().length() == 0).mapToInt(e -> ((JSONObject) e).getInteger(valueKey)).sum();
                v2 += e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("HIGH") || speed.trim().length() == 0).mapToInt(e -> ((JSONObject) e).getInteger(valueKey)).sum();
                v2 += e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("EXCEED") || speed.trim().length() == 0).mapToInt(e -> ((JSONObject) e).getInteger(valueKey)).sum();
            }
            return v2 - v1;
        }).collect(Collectors.toList());

        rankingData.stream().forEach(e -> {
            JSONObject jsonObject = new JSONObject();
            Double value = e.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(o -> ((JSONObject) o).getString("speed").equals(speed) || speed.trim().length() == 0).mapToDouble(o -> ((JSONObject) o).getInteger(valueKey)).sum();
            if (game.getApp()) {
                UserApp stu = userAppService.getById(e.getStudentId());
                jsonObject.put("name", stu.getName());
                jsonObject.put("headImg", stu.getHeadImg());
            } else {
                StudentInfo stu = studentInfoService.getById(e.getStudentId());
                jsonObject.put("name", stu.getName());
                jsonObject.put("headImg", stu.getHeadImg());
            }
            jsonObject.put("id", e.getStudentId());
            jsonObject.put("value", value);
            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }


    public JSONArray settleUploadBody(String strJSONArray, Long matchId, String matchType) {
        Map<Long, RawData> map = new HashMap<>();
        JSONArray jsonArray = strJSONArray == null ? new JSONArray() : JSONArray.parseArray(strJSONArray);
        getPersonExerciseStartAndStopTime(matchId, jsonArray, matchType);
        //查出已经上传过的body
        List<RawData> rawDataList = rawDataService.findOfMatch("GAME", matchId);
        Map<Long, RawData> rawMap = rawDataList.stream().collect(Collectors.toMap(RawData::getStudentId, Function.identity(), (key1, key2) -> key2));
        List<RawData> rawDatas = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            jsonArray.getJSONObject(i).put("userId", jsonArray.getJSONObject(i).getLongValue("studentId"));
            jsonArray.getJSONObject(i).put("teamId", jsonArray.getJSONObject(i).getLongValue("teamId"));
            Long stuId = jsonArray.getJSONObject(i).getLongValue("studentId");
            RawData rawData;
            if (rawMap.containsKey(stuId)) {
                rawDataList.remove(rawMap.get(stuId));
                rawData = rawMap.get(stuId).setRaw(jsonArray.getJSONObject(i));
            } else {
                rawData = new RawData();
                rawData.setMatchId(matchId);
                rawData.setStudentId(stuId);
                rawData.setRaw(jsonArray.getJSONObject(i));
                rawData.setMatchType(matchType);
                rawData.setTeamId(jsonArray.getJSONObject(i).getLongValue("teamId"));
            }
            map.put(stuId, rawData);
            rawDatas.add(rawData);
        }
        rawDataService.saveOrUpdateBatch(rawDatas);
        //将之前上传的数据 一起算
        rawDataList.forEach(o -> jsonArray.add(o.getRaw()));
        return jsonArray;
    }

    public boolean savePersonData(JSONObject resObject, Long teamId, Long matchId, String matchType) {
        JSONArray personData = resObject.getJSONArray("personData");
        for (int i = 0; i < personData.size(); i++) {
            personData.getJSONObject(i).put("studentId", personData.getJSONObject(i).getLongValue("userId"));
            personData.getJSONObject(i).put("matchId", matchId);
            personData.getJSONObject(i).put("matchType", matchType);
        }
        List<PersonData> list = JSON.parseArray(JSONArray.toJSONString(personData), PersonData.class);
        Game game = gameService.getById(matchId);
        List<PersonData> dataList = personDataService.list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchId, matchId).eq(PersonData::getMatchType, matchType));
        Map<Long, PersonData> personDataMap = dataList.stream().collect(Collectors.toMap(PersonData::getStudentId, Function.identity(), (key1, key2) -> key2));
        for (int i = 0; i < list.size(); i++) {
            PersonData o = list.get(i);
            if (personDataMap.containsKey(o.getStudentId())) {
                o.setId(personDataMap.get(o.getStudentId()).getId());
            }
            if(o.getRunDistance()==0){
               // list.set(i,paddingData(o));
            }
            o.setTeamId(teamId);
            o.setAppLook(false);
            o.setCourseId(game.getCourseId());
        }
        transparentMessageService.sendTransparentByGameUpload(list);
        return personDataService.saveOrUpdateBatch(list);
    }
    @Synchronized
    public PersonData paddingData(PersonData personData) {
        int randomInt = ThreadLocalRandom.current().nextInt(1, 10 + 1);
        PersonData personData1 = personDataService.getById(randomInt);
        personData1.setId(personData.getId());
        personData1.setStudentId(personData.getStudentId());
        personData1.setTeamId(personData.getTeamId());
        personData1.setMatchType(personData.getMatchType());
        personData1.setMatchId(personData.getMatchId());
        personData1.setExerciseTime(personData.getExerciseTime());
        personData1.setCreateTime(new Date());
        personData1.setUpdateTime(new Date());
        return SerializationUtils.clone(personData1);

    }

    public boolean saveTeamData(JSONObject resObject, Long teamId, Long matchId, String matchType) {
        JSONObject teamOb = resObject.getJSONObject("teamData");
        TeamData teamPo = JSONObject.toJavaObject(teamOb, TeamData.class);
        TeamData teamData = teamDataService.findOfTeam(matchId, teamId, matchType);
        teamPo.setTeamId(teamId);
        teamPo.setMatchId(matchId);
        teamPo.setMatchType(matchType);
        if (teamData != null) {
            teamPo.setId(teamData.getId());
            teamPo.setVersion(teamData.getVersion());
        }
        List<CurveDto> teamPoCurve = JSON.parseArray(teamPo.getCurves().toJSONString(), CurveDto.class);
        List<TeamData> teamDataList = teamDataService.findOfTeamList(teamId, matchType);
        CurveDto avgCurve = new CurveDto();
        avgCurve.setCurve(CurveEnum.TEAM_HISTORY_CONSUME);
        for (TeamData t : teamDataList) {
            CurveDto curveDto = JSON.parseObject(JSON.toJSONString(t.getCurves().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("curve").equals("TEAM_CONSUME")).findFirst().get()), CurveDto.class);
            avgCurve.setAxisX(curveDto.getAxisX());
            avgCurve.setAxisY(curveDto.getAxisY());
        }
        teamPoCurve.add(avgCurve);
        teamPo.setCurves(JSON.parseArray(JSON.toJSONString(teamPoCurve)));
        return teamDataService.saveOrUpdate(teamPo);
    }


    /**
     * 获取比赛运动的起始和结束时间（个人）
     */
    public void getPersonExerciseStartAndStopTime(Long matchId, JSONArray jsonArray, String matchType) {
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            long origin = 0l;
            long destination = 0l;
            GameStudent gameStudent = gameStudentService.getOneByGameIdAndStudentId(matchId, object.getLong("studentId"));
            if (gameStudent != null) {
                if (gameStudent.getStartTime() == null) {
                    Game game = gameService.getById(matchId);
                    origin = game.getFinishStartTime().getTime();
                    destination = game.getFinishStopTime().getTime();
                } else {
                    origin = gameStudent.getStartTime().getTime();
                    destination = gameStudent.getStopTime().getTime();
                }
            }else{
                Game game = gameService.getById(matchId);
                origin = game.getFinishStartTime().getTime();
                destination = game.getFinishStopTime().getTime();
            }
            object.put("origin", origin);
            object.put("destination", destination);
            object.put("timeSlot", new JSONArray());
            jsonArray.set(i, object);
        }
    }

    /**
     * 获取比赛运动的起始和结束时间（团队）
     */
    public JSONObject getTeamExerciseStartAndStopTime(Long matchId) {
        JSONObject object = new JSONObject();
        Game game = gameService.getById(matchId);
        object.put("origin", game.getRealDataTime() != null && game.getRealDataTime() != 0 ? game.getRealDataTime() : game.getFinishStartTime().getTime());
        object.put("destination", game.getFinishStopTime().getTime());
        return object;
    }

    /**
     * @desc: 计算个人数据
     * @author: DH
     * @date: 2021/3/17 11:16
     */
    public void calculatePersonData(PersonDataAo personDataAo, List<PersonData> personDataList, String matchType) {
        Game game = gameService.getById(personDataAo.getMatchId());
        game.setUserTeamId(teamUserAppService.getByUserIdAndGameId(personDataAo.getStudentId(), game));
        personDataAo.setGameInfo(game);
        personDataAo.setCompetitionTime(game.getFinishStartTime());
        if (game.getApp()) {
            TeamApp teamApp = teamAppService.getTeamApp(personDataAo.getTeamId());
            if (teamApp != null) {
                personDataAo.setTeamName(teamApp.getFullName());
            }
        } else {
            Team team = teamService.getById(personDataAo.getTeamId());
            if (team != null) {
                personDataAo.setTeamName(team.getFullName());
            }
        }

        //团队平均卡路里
        int calorieSum = personDataList.stream().mapToInt(e -> e.getCalorie()).sum();
        if (calorieSum != 0) {
            personDataAo.setAvgCalorie(calorieSum / personDataList.size());
        } else {
            personDataAo.setAvgCalorie(0);
        }

        //个人平均卡路里
        List<PersonData> historyPersonDataList = personDataService.findOfMatchIdByHist(personDataAo.getStudentId(), personDataAo.getMatchId(), "GAME");
        int historyCalorie = historyPersonDataList.stream().mapToInt(e -> e.getCalorie()).sum();
        if (historyCalorie != 0) {
            personDataAo.setHistoryCalorie(calorieSum / personDataList.size());
        } else {
            personDataAo.setHistoryCalorie(0);
        }
        //排名
        personDataAo.setRanking(getRankingAndData(personDataList, personDataAo, matchType));

        // 记时/计数/场记 数据
        JSONObject extra = new JSONObject();
        GameExtraPageParam dp = new GameExtraPageParam();
        dp.setGameId(personDataAo.getMatchId());
        dp.setFinish(1);
        dp.setUserId(personDataAo.getStudentId());
        dp.setType(-1);
        dp.setAstrict(true);
        try {
            List<GameExtraUserAo> gameExtraUserAos = gameExtraService.getGameExtraByUser(dp);
            extra.put("timingOrNumeration", gameExtraUserAos);

            dp.setPageIndex(1l);
            dp.setPageSize(3l);
            dp.setType(1);
            extra.put("timing", gameExtraService.getGameExtraByUser(dp));
            dp.setType(2);
            extra.put("numeration", gameExtraService.getGameExtraByUser(dp));
        } catch (Exception e) {
            e.printStackTrace();
        }
        extra.put("fieldGroup", fieldDataService.getStuField(fieldDataService.createFieldDataPageParam(personDataAo.getStudentId(), "GAME", personDataAo.getMatchId(), 1L, 3L)));
        personDataAo.setExtra(extra);

        JSONArray runRanking = new JSONArray(); //跑动排名
        runAndPassRanking(personDataList, runRanking);
        List<JSONObject> list = new ArrayList<>(); //mvp 未排序前
        for (int i = 0; i < runRanking.size(); i++) {
            JSONObject r = runRanking.getJSONObject(i);
            JSONObject v = new JSONObject();
            v.put("studentId", r.getLong("studentId"));
            v.put("price", mvpPrice(r.getLong("runDistance"), 0));
            v.put("runDistance", r.getLong("runDistance"));

            if (v.get("runDistance") == null) {
                v.put("runDistance", 0);
            }
            list.add(v);
        }
        JSONObject mvp = list.stream().max(Comparator.
                comparingDouble((e) -> e.getDouble("runDistance"))).get();
        if (game.getApp()) {
            mvp.put("studentName", userAppService.getById(mvp.getLongValue("studentId")).getName());
        } else {
            mvp.put("studentName", studentInfoService.getById(mvp.getLongValue("studentId")).getName());
        }
        personDataAo.setMvp(mvp);

        //专业数据
        JSONObject overallSpeed = new JSONObject();
        JSONObject overallSpeedCount = new JSONObject();
        JSONArray speedDataList = JSON.parseArray(personDataAo.getSpeedDataList().toJSONString());
        for (int i = 0; i < speedDataList.size(); i++) {
            JSONObject speed = speedDataList.getJSONObject(i);
            overallSpeed.put(speed.getString("speed"), speed.getInteger("distance"));
            if (speed.getString("speed").equals("EXCEED")) {
                overallSpeedCount.put("exceedCount", speed.getInteger("count"));
            } else if ((speed.getString("speed").equals("SUP"))) {
                overallSpeedCount.put("supCount", speed.getInteger("count"));
            }
        }
        if (overallSpeed.get("EXCEED") == null || overallSpeed.get("SUP") == null) {//之前的数据防止报错
            overallSpeed.put("EXCEED", 0);
            overallSpeedCount.put("exceedCount", 0);
            overallSpeed.put("SUP", 0);
            overallSpeedCount.put("supCount", 0);
        }
        personDataAo.setOverallSpeed(overallSpeed);
        personDataAo.setOverallSpeedCount(overallSpeedCount);
        if (game.getApp()) {
            personDataList = personDataService.list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getStudentId, personDataAo.getStudentId()));
        }

        //高中低数值
        Integer distance1 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        Integer distance2 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        Integer distance3 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("HIGH")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        Integer distance4 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("EXCEED")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        Integer distance5 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        JSONObject distances = new JSONObject();
        distances.put("low", distance1);
        distances.put("mid", distance2 + distance3 + distance4);
        distances.put("high", distance5);
        personDataAo.setDistances(distances);
        calculateTeamAvg(personDataList, null, personDataAo);
    }

    /**
     * @desc: 计算个人数据
     * @author: DH
     * @date: 2021/3/17 11:16
     */
    public void calculatePersonDataTuoNiao(PersonDataAo personDataAo, List<PersonData> personDataList, String matchType) {
        Game game = gameService.getById(personDataAo.getMatchId());

        //排名
        personDataAo.setRanking(getRankingAndData(personDataList, personDataAo, matchType));

        // 记时/计数/场记 数据
        JSONObject extra = new JSONObject();
        GameExtraPageParam dp = new GameExtraPageParam();
        dp.setGameId(personDataAo.getMatchId());
        dp.setFinish(1);
        dp.setUserId(personDataAo.getStudentId());
        try {
            dp.setType(1);
            extra.put("timing", gameExtraService.getGameExtraByUser(dp));
            dp.setType(2);
            extra.put("numeration", gameExtraService.getGameExtraByUser(dp));
        } catch (Exception e) {
            e.printStackTrace();
        }
        //计算场记数据
        FieldDataPageParam fieldDataPageParam = new FieldDataPageParam();
        fieldDataPageParam.setMatchType("GAME");
        fieldDataPageParam.setMatchId(personDataAo.getMatchId());
        fieldDataPageParam.setStudentId(personDataAo.getStudentId());
        extra.put("fieldGroup", fieldDataService.getStuField(fieldDataPageParam));
        personDataAo.setExtra(extra);
        JSONArray runRanking = new JSONArray(); //跑动排名
        runAndPassRanking(personDataList, runRanking);
        List<JSONObject> list = new ArrayList<>(); //mvp 未排序前
        for (int i = 0; i < runRanking.size(); i++) {
            JSONObject r = runRanking.getJSONObject(i);
            JSONObject v = new JSONObject();
            v.put("studentId", r.getLong("studentId"));
            v.put("price", mvpPrice(r.getLong("runDistance"), 0));
            v.put("runDistance", r.getLong("runDistance"));

            if (v.get("runDistance") == null) {
                v.put("runDistance", 0);
            }
            list.add(v);
        }
        JSONObject mvp = list.stream().max(Comparator.
                comparingDouble((e) -> e.getDouble("runDistance"))).get();
        if (game.getApp()) {
            mvp.put("studentName", userAppService.getById(mvp.getLongValue("studentId")).getName());
        } else {
            mvp.put("studentName", studentInfoService.getById(mvp.getLongValue("studentId")).getName());
        }
        personDataAo.setMvp(mvp);

        //专业数据
        JSONObject overallSpeed = new JSONObject();
        JSONObject overallSpeedCount = new JSONObject();
        JSONArray speedDataList = JSON.parseArray(personDataAo.getSpeedDataList().toJSONString());
        for (int i = 0; i < speedDataList.size(); i++) {
            JSONObject speed = speedDataList.getJSONObject(i);
            overallSpeed.put(speed.getString("speed"), speed.getInteger("distance"));
            if (speed.getString("speed").equals("EXCEED")) {
                overallSpeedCount.put("exceedCount", speed.getInteger("count"));
            } else if ((speed.getString("speed").equals("SUP"))) {
                overallSpeedCount.put("supCount", speed.getInteger("count"));
            }
        }
        personDataAo.setOverallSpeed(overallSpeed);
        personDataAo.setOverallSpeedCount(overallSpeedCount);
        if (game.getApp()) {
            personDataList = personDataService.list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getStudentId, personDataAo.getStudentId()));
        }
        //高中低数值
        Integer distance1 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        Integer distance2 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        Integer distance3 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("HIGH")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        Integer distance4 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("EXCEED")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        Integer distance5 = personDataAo.getSpeedDataList().toJavaObject(JSONArray.class).stream()
                .filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
        JSONObject distances = new JSONObject();
        distances.put("low", distance1);
        distances.put("mid", distance2 + distance3 + distance4);
        distances.put("high", distance5);
        personDataAo.setDistances(distances);
        calculateTeamAvg(personDataList, null, personDataAo);
    }

    /**
     * @desc: 计算团队数据
     * @author: DH
     * @date: 2021/3/12 15:30
     */
    public TeamDataAo calculateTeamData(TeamDataAo teamDataAo, List<PersonData> personDatas, String matchType) {
        Game game = gameService.getById(teamDataAo.getMatchId());
        JSONObject pace = new JSONObject();
        Double avgSpeed = personDatas.stream().mapToDouble(e -> {
            if (e.getPace() != null && e.getPace().getDouble("avgSpeed") != null) {
                return e.getPace().getIntValue("avgSpeed");
            } else {
                return 0;
            }
        }).average().orElse(0);
        BigDecimal b = new BigDecimal(avgSpeed);
        pace.put("avgSpeed", b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());

        pace.put("avgStride", (int) personDatas.stream().mapToInt(e -> {
            if (e.getPace() != null && e.getPace().getInteger("avgStride") != null) {
                return e.getPace().getIntValue("avgStride");
            } else {
                return 0;
            }
        }).average().orElse(0));
        pace.put("avgSpeedAllocation", (int) personDatas.stream().mapToLong(e -> {
            if (e.getPace() != null && e.getPace().getLong("avgSpeedAllocation") != null) {
                return e.getPace().getIntValue("avgSpeedAllocation");
            } else {
                return 0;
            }
        }).average().orElse(0));
        pace.put("avgStrideFrequency", (int) personDatas.stream().mapToInt(e -> {
            if (e.getPace() != null && e.getPace().getInteger("avgStrideFrequency") != null) {
                return e.getPace().getIntValue("avgStrideFrequency");
            } else {
                return 0;
            }
        }).average().orElse(0));
        teamDataAo.setPace(pace);

        teamDataAo.setSumStep(personDatas.stream().mapToInt(PersonData::getStepCount).sum());
        teamDataAo.setMaxTakeOffDistance((int) personDatas.stream().mapToInt(PersonData::getMaxTakeOffDistance).average().orElse(0.0));
        teamDataAo.setMaxTakeOffHeight((int) personDatas.stream().mapToInt(PersonData::getMaxTakeOffHeight).average().orElse(0.0));
        teamDataAo.setAvgDuration((int) personDatas.stream().mapToInt(PersonData::getAvgDuration).average().orElse(0.0));
        teamDataAo.setJumpAvgHeight((int) personDatas.stream().mapToInt(PersonData::getJumpAvgHeight).average().orElse(0.0));
        teamDataAo.setAvgDuration((int) personDatas.stream().mapToInt(PersonData::getAvgDuration).average().orElse(0.0));
        teamDataAo.setMaxDuration(personDatas.stream().mapToInt(PersonData::getAvgDuration).max().orElse(0));


        JSONArray runRanking = new JSONArray(); //跑动排名
        runAndPassRanking(personDatas, runRanking);
        List<JSONObject> list = new ArrayList<>(); //mvp 未排序前
        for (int i = 0; i < runRanking.size(); i++) {
            JSONObject r = runRanking.getJSONObject(i);
            JSONObject v = new JSONObject();
            v.put("studentId", r.getLong("studentId"));
            v.put("price", mvpPrice(r.getLong("runDistance"), 0));
            v.put("runDistance", r.getLong("runDistance"));

            if (v.get("runDistance") == null) {
                v.put("runDistance", 0);
            }
            list.add(v);
        }

        //总跑动 总跑动最多 因为是排了序的 所以下标0就是跑动最多
        if (game.getApp()) {
            UserApp runBestStu = userAppService.getById(runRanking.getJSONObject(0).getLongValue("studentId"));
            if (runBestStu != null) {
                teamDataAo.setRunBestStu(runBestStu.getName());
            }
        } else {
            StudentInfo runBestStu = studentInfoService.getById(runRanking.getJSONObject(0).getLongValue("studentId"));
            if (runBestStu != null) {
                teamDataAo.setRunBestStu(runBestStu.getName());
            }
        }
        teamDataAo.setSumRun(runRanking.stream().mapToLong(e -> JSON.parseObject(e.toString()).getLong("runDistance")).sum());
        teamDataAo.setSumCalorie(personDatas.stream().mapToInt(e -> e.getCalorie()).sum());
        teamDataAo.setBestStuRun(runRanking.getJSONObject(0).getLongValue("runDistance"));

        //历史纪录
        List<TeamData> teamDatas = teamDataService.findOfTeamList(teamDataAo.getTeamId(), "GAME").stream().filter(e -> !(e.getMatchId().equals(teamDataAo.getMatchId()))).collect(Collectors.toList());

        //历史平均消耗
        List<Integer> consume = new ArrayList<>();
        teamDatas.stream().forEach(e -> {
            JSONArray jsonArray = (JSONArray) e.getCurves();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if (jsonObject.getString("curve").equals("TEAM_CONSUME")) {
                    if (!e.getMatchId().equals(teamDataAo.getMatchId())) {
                        consume.add(jsonObject.getIntValue("sumY"));
                        break;
                    }
                }
            }
        });
        teamDataAo.setAvgConsume(consume.stream().mapToInt(e -> e).sum() / (consume.size() == 0 ? 1 : consume.size()));

        //速度数据
        Map<String, Object> speedDataMap = new HashMap<>();
        List<Object> speedDatas = personDatas.stream().flatMap(e -> e.getSpeedDataList().toJavaObject(JSONArray.class).stream()).collect(Collectors.toList());
        //高速
        speedDataMap.put("highDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());//全队高速跑动距离 m
        speedDataMap.put("highCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());//全队高速跑动次数
        speedDataMap.put("highBallCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballCount")).sum());//全队高速带球次数
        speedDataMap.put("highBallDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队高速带球跑动
        //中速
        speedDataMap.put("minDistance",
                speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum() +
                        speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("HIGH")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum() +
                        speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("EXCEED")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum()
        );//全队中速跑动距离 m

        speedDataMap.put("minCount",
                speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum()+
                        speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("HIGH")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum() +
                        speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("EXCEED")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum()
        );//全队中速跑动次数
        speedDataMap.put("minBallCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("ballCount")).sum());//全中速带球次数
        speedDataMap.put("minBallDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队中速带球跑动m
        //低速
        speedDataMap.put("lowDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());//全队低速跑动距离 m
        speedDataMap.put("lowCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());//全队低速跑动次数
        speedDataMap.put("lowBallCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("ballCount")).sum());//全低速带球次数
        speedDataMap.put("lowBallDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队低速带球跑动m
        speedDataMap.put("teamBallDistance", speedDatas.stream().mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队带球距离

        PersonData maxSprintSpeedPersonData = personDatas.stream().sorted((e1, e2) -> Double.compare(e2.getMaxSprintSpeed().doubleValue(), e1.getMaxSprintSpeed().doubleValue())).findFirst().get(); //最高冲刺速度第一
        PersonData maxHighDistancePersonData = personDatas.stream().sorted((e1, e2) -> {
            Integer o1 = e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
            Integer o2 = e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
            return Double.compare(o2, o1);
        }).findFirst().get();
        PersonData maxHighBallDistancePersonData = personDatas.stream().sorted((e1, e2) -> {
            Integer o1 = e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt();
            Integer o2 = e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt();
            return Double.compare(o2, o1);
        }).findFirst().get();

        JSONObject mvp = list.stream().max(Comparator.
                comparingDouble((e) -> e.getDouble("runDistance"))).get();

        Long absent = 0l;
        Long cancel = 0l;
        List<GameStudent> gameStudents = gameStudentService.getStudentGameStatus(teamDataAo.getMatchId(), teamDataAo.getTeamId());
        absent = gameStudents.stream().filter(e -> !e.getStatus()).count();
        cancel = gameStudents.stream().filter(e -> e.getCancel()).count();

        if ((matchType.equals("GAME") && !teamDataAo.getGameInfo().getApp())) {
            List<MatchHardware> matchHardwares = matchHardwareService.getMatchHardwareByMatchId(teamDataAo.getMatchId(), teamDataAo.getTeamId(), matchType);
            teamDataAo.setHardware(matchHardwares.stream().filter(e -> e.getStarted()).count());

            StudentInfo maxSprintSpeedStu = studentInfoService.getById(maxSprintSpeedPersonData.getStudentId());
            StudentInfo maxHighDistanceStu = studentInfoService.getById(maxHighDistancePersonData.getStudentId());
            StudentInfo maxHighBallDistanceStu = studentInfoService.getById(maxHighBallDistancePersonData.getStudentId());
            speedDataMap.put("maxSprintSpeedStudentName", maxSprintSpeedStu.getName());
            speedDataMap.put("maxHighDistanceStudentName", maxHighDistanceStu.getName());
            speedDataMap.put("maxHighBallDistanceStudentName", maxHighBallDistanceStu.getName());

            speedDataMap.put("maxSprintSpeedStudentHeadImg", maxSprintSpeedStu.getHeadImg());
            speedDataMap.put("maxHighDistanceStudentHeadImg", maxHighDistanceStu.getHeadImg());
            speedDataMap.put("maxHighBallDistanceStudentHeadImg", maxHighBallDistanceStu.getHeadImg());

            speedDataMap.put("maxSprintSpeed", maxSprintSpeedPersonData.getMaxSprintSpeed());
            speedDataMap.put("maxHighDistance", maxHighDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt());
            speedDataMap.put("maxHighBallDistance", maxHighBallDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt());

            StudentInfo studentInfoMvp = studentInfoService.getById(mvp.getLongValue("studentId"));
            if (studentInfoMvp != null) {
                mvp.put("studentName", studentInfoMvp.getName());
                //MVP
                teamDataAo.setMvp(mvp);
            }
        } else {
            List<MatchHardwareApp> matchHardwares = matchHardwareAppService.getMatchHardwareByMatchId(matchType, teamDataAo.getMatchId(), teamDataAo.getTeamId());
            teamDataAo.setHardware(matchHardwares.stream().filter(e -> e.getStarted()).count());

            UserApp maxSprintSpeedStu = userAppService.getById(maxSprintSpeedPersonData.getStudentId());
            UserApp maxHighDistanceStu = userAppService.getById(maxHighDistancePersonData.getStudentId());
            UserApp maxHighBallDistanceStu = userAppService.getById(maxHighBallDistancePersonData.getStudentId());
            speedDataMap.put("maxSprintSpeedStudentName", maxSprintSpeedStu.getName());
            speedDataMap.put("maxHighDistanceStudentName", maxHighDistanceStu.getName());
            speedDataMap.put("maxHighBallDistanceStudentName", maxHighBallDistanceStu.getName());

            speedDataMap.put("maxSprintSpeedStudentHeadImg", maxSprintSpeedStu.getHeadImg());
            speedDataMap.put("maxHighDistanceStudentHeadImg", maxHighDistanceStu.getHeadImg());
            speedDataMap.put("maxHighBallDistanceStudentHeadImg", maxHighBallDistanceStu.getHeadImg());

            speedDataMap.put("maxSprintSpeed", maxSprintSpeedPersonData.getMaxSprintSpeed());
            speedDataMap.put("maxHighDistance", maxHighDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt());
            speedDataMap.put("maxHighBallDistance", maxHighBallDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt());

            UserApp studentInfoMvp = userAppService.getById(mvp.getLongValue("studentId"));
            if (studentInfoMvp != null) {
                mvp.put("studentName", studentInfoMvp.getName());
                //MVP
                teamDataAo.setMvp(mvp);
            }
        }
        teamDataAo.setSpeedDataMap(speedDataMap);
        teamDataAo.setAbsent(absent.intValue());
        teamDataAo.setNoUploaded(teamDataAo.getHardware().intValue() - teamDataAo.getUploaded() - cancel.intValue());
        teamDataAo.setUploaded(personDatas.size());

        // 记时/计数/场记 数据
        JSONObject extra = new JSONObject();
        try {
            extra.put("timing", gameExtraService.getGameExtraByUser(gameExtraService.createGameExtraPageParam(teamDataAo.getMatchId(), null, 1, 1, false, 1l, 3L)));
            extra.put("numeration", gameExtraService.getGameExtraByUser(gameExtraService.createGameExtraPageParam(teamDataAo.getMatchId(), null, 1, 2, false, 1l, 3L)));
            extra.put("fieldGroup", fieldNotesService.getPageList(fieldNotesService.createFieldNotesPageParam(null, teamDataAo.getMatchType(), teamDataAo.getMatchId(), 1L, 3L)));
        } catch (Exception e) {
            e.printStackTrace();
        }

        teamDataAo.setExtra(extra);


        JSONObject runningForm = new JSONObject();
        Long avgTouchLandTime = 0l;
        Double avgTouchdownImpact = 0.0;
        Integer avgEctropionRange = null;
        Integer avgPendulumAngle = null;

        avgTouchLandTime = (long) personDatas.stream().mapToLong(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0l;
            } else {
                return e.getRunningForm().getLongValue("avgTouchLandTime");
            }
        }).average().orElse(0.0);

        avgTouchdownImpact = personDatas.stream().mapToDouble(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0.0;
            } else {
                return e.getRunningForm().getDouble("avgTouchdownImpact");
            }
        }).average().orElse(0.0);

        avgEctropionRange = (int) personDatas.stream().mapToInt(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0;
            } else {
                return e.getRunningForm().getInteger("avgEctropionRange");
            }
        }).average().orElse(0.0);

        avgPendulumAngle = (int) personDatas.stream().mapToInt(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0;
            } else {
                return e.getRunningForm().getInteger("avgPendulumAngle");
            }
        }).average().orElse(0.0);

        runningForm.put("avgTouchLandTime", avgTouchLandTime);
        BigDecimal bd = new BigDecimal(avgTouchdownImpact);
        runningForm.put("avgTouchdownImpact", bd.setScale(2, BigDecimal.ROUND_DOWN).doubleValue());
        runningForm.put("avgEctropionRange", avgEctropionRange);
        runningForm.put("avgPendulumAngle", avgPendulumAngle);
        teamDataAo.setRunningForm(runningForm);

        getDepthData(teamDataAo, personDatas);
        calculateTeamAvg(personDatas, teamDataAo, null);
        return teamDataAo;
    }

    /**
     * @desc: 计算团队数据
     * @author: DH
     * @date: 2021/3/12 15:30
     */
    public TeamDataAo calculateTeamDataTuoNiao(TeamDataAo teamDataAo, List<PersonData> personDatas, String matchType) {
        Game game = gameService.getById(teamDataAo.getMatchId());
        JSONObject pace = new JSONObject();
        Double avgSpeed = personDatas.stream().mapToDouble(e -> {
            if (e.getPace() != null && e.getPace().getDouble("avgSpeed") != null) {
                return e.getPace().getIntValue("avgSpeed");
            } else {
                return 0;
            }
        }).average().orElse(0);
        BigDecimal b = new BigDecimal(avgSpeed);
        pace.put("avgSpeed", b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());

        pace.put("avgStride", (int) personDatas.stream().mapToInt(e -> {
            if (e.getPace() != null && e.getPace().getInteger("avgStride") != null) {
                return e.getPace().getIntValue("avgStride");
            } else {
                return 0;
            }
        }).average().orElse(0));
        pace.put("avgSpeedAllocation", (int) personDatas.stream().mapToLong(e -> {
            if (e.getPace() != null && e.getPace().getLong("avgSpeedAllocation") != null) {
                return e.getPace().getIntValue("avgSpeedAllocation");
            } else {
                return 0;
            }
        }).average().orElse(0));
        pace.put("avgStrideFrequency", (int) personDatas.stream().mapToInt(e -> {
            if (e.getPace() != null && e.getPace().getInteger("avgStrideFrequency") != null) {
                return e.getPace().getIntValue("avgStrideFrequency");
            } else {
                return 0;
            }
        }).average().orElse(0));
        teamDataAo.setPace(pace);

        teamDataAo.setSumStep(personDatas.stream().mapToInt(PersonData::getStepCount).sum());
        teamDataAo.setMaxTakeOffDistance((int) personDatas.stream().mapToInt(PersonData::getMaxTakeOffDistance).average().orElse(0.0));
        teamDataAo.setMaxTakeOffHeight((int) personDatas.stream().mapToInt(PersonData::getMaxTakeOffHeight).average().orElse(0.0));
        teamDataAo.setAvgDuration((int) personDatas.stream().mapToInt(PersonData::getAvgDuration).average().orElse(0.0));
        teamDataAo.setJumpAvgHeight((int) personDatas.stream().mapToInt(PersonData::getJumpAvgHeight).average().orElse(0.0));
        teamDataAo.setAvgDuration((int) personDatas.stream().mapToInt(PersonData::getAvgDuration).average().orElse(0.0));
        teamDataAo.setMaxDuration(personDatas.stream().mapToInt(PersonData::getAvgDuration).max().orElse(0));


        JSONArray runRanking = new JSONArray(); //跑动排名
        runAndPassRanking(personDatas, runRanking);
        List<JSONObject> list = new ArrayList<>(); //mvp 未排序前
        for (int i = 0; i < runRanking.size(); i++) {
            JSONObject r = runRanking.getJSONObject(i);
            JSONObject v = new JSONObject();
            v.put("studentId", r.getLong("studentId"));
            v.put("price", mvpPrice(r.getLong("runDistance"), 0));
            v.put("runDistance", r.getLong("runDistance"));

            if (v.get("runDistance") == null) {
                v.put("runDistance", 0);
            }
            list.add(v);
        }

        //总跑动 总跑动最多 因为是排了序的 所以下标0就是跑动最多
        if (game.getApp()) {
            UserApp runBestStu = userAppService.getById(runRanking.getJSONObject(0).getLongValue("studentId"));
            if (runBestStu != null) {
                teamDataAo.setRunBestStu(runBestStu.getName());
            }
        } else {
            StudentInfo runBestStu = studentInfoService.getById(runRanking.getJSONObject(0).getLongValue("studentId"));
            if (runBestStu != null) {
                teamDataAo.setRunBestStu(runBestStu.getName());
            }
        }
        teamDataAo.setSumRun(runRanking.stream().mapToLong(e -> JSON.parseObject(e.toString()).getLong("runDistance")).sum());
        teamDataAo.setSumCalorie(personDatas.stream().mapToInt(e -> e.getCalorie()).sum());
        teamDataAo.setBestStuRun(runRanking.getJSONObject(0).getLongValue("runDistance"));

        //历史纪录
        List<TeamData> teamDatas = teamDataService.findOfTeamList(teamDataAo.getTeamId(), "GAME").stream().filter(e -> !(e.getMatchId().equals(teamDataAo.getMatchId()))).collect(Collectors.toList());

        //历史平均消耗
        List<Integer> consume = new ArrayList<>();
        teamDatas.stream().forEach(e -> {
            JSONArray jsonArray = (JSONArray) e.getCurves();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if (jsonObject.getString("curve").equals("TEAM_CONSUME")) {
                    if (!e.getMatchId().equals(teamDataAo.getMatchId())) {
                        consume.add(jsonObject.getIntValue("sumY"));
                        break;
                    }
                }
            }
        });
        teamDataAo.setAvgConsume(consume.stream().mapToInt(e -> e).sum() / (consume.size() == 0 ? 1 : consume.size()));

        //速度数据
        Map<String, Object> speedDataMap = new HashMap<>();
        List<Object> speedDatas = personDatas.stream().flatMap(e -> e.getSpeedDataList().toJavaObject(JSONArray.class).stream()).collect(Collectors.toList());
        //高速
        speedDataMap.put("highDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());//全队高速跑动距离 m
        speedDataMap.put("highCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());//全队高速跑动次数
        speedDataMap.put("highBallCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballCount")).sum());//全队高速带球次数
        speedDataMap.put("highBallDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队高速带球跑动
        //中速
        speedDataMap.put("minDistance",
                speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum() +
                        speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("HIGH")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum() +
                        speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("EXCEED")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum()
        );//全队中速跑动距离 m

        speedDataMap.put("minCount",
                speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum()+
                        speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("HIGH")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum() +
                        speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("EXCEED")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum()
        );//全队中速跑动次数
        speedDataMap.put("minBallCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("ballCount")).sum());//全中速带球次数
        speedDataMap.put("minBallDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队中速带球跑动m
        //低速
        speedDataMap.put("lowDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());//全队低速跑动距离 m
        speedDataMap.put("lowCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());//全队低速跑动次数
        speedDataMap.put("lowBallCount", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("ballCount")).sum());//全低速带球次数
        speedDataMap.put("lowBallDistance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队低速带球跑动m
        speedDataMap.put("teamBallDistance", speedDatas.stream().mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).sum());//全队带球距离

        PersonData maxSprintSpeedPersonData = personDatas.stream().sorted((e1, e2) -> Double.compare(e2.getMaxSprintSpeed().doubleValue(), e1.getMaxSprintSpeed().doubleValue())).findFirst().get(); //最高冲刺速度第一
        PersonData maxHighDistancePersonData = personDatas.stream().sorted((e1, e2) -> {
            Integer o1 = e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
            Integer o2 = e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt();
            return Double.compare(o2, o1);
        }).findFirst().get();
        PersonData maxHighBallDistancePersonData = personDatas.stream().sorted((e1, e2) -> {
            Integer o1 = e1.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt();
            Integer o2 = e2.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt();
            return Double.compare(o2, o1);
        }).findFirst().get();

        JSONObject mvp = list.stream().max(Comparator.
                comparingDouble((e) -> e.getDouble("runDistance"))).get();

        if ((matchType.equals("GAME") && !teamDataAo.getGameInfo().getApp())) {

            StudentInfo maxSprintSpeedStu = studentInfoService.getById(maxSprintSpeedPersonData.getStudentId());
            StudentInfo maxHighDistanceStu = studentInfoService.getById(maxHighDistancePersonData.getStudentId());
            StudentInfo maxHighBallDistanceStu = studentInfoService.getById(maxHighBallDistancePersonData.getStudentId());
            speedDataMap.put("maxSprintSpeedStudentName", maxSprintSpeedStu.getName());
            speedDataMap.put("maxHighDistanceStudentName", maxHighDistanceStu.getName());
            speedDataMap.put("maxHighBallDistanceStudentName", maxHighBallDistanceStu.getName());

            speedDataMap.put("maxSprintSpeedStudentHeadImg", maxSprintSpeedStu.getHeadImg());
            speedDataMap.put("maxHighDistanceStudentHeadImg", maxHighDistanceStu.getHeadImg());
            speedDataMap.put("maxHighBallDistanceStudentHeadImg", maxHighBallDistanceStu.getHeadImg());

            speedDataMap.put("maxSprintSpeed", maxSprintSpeedPersonData.getMaxSprintSpeed());
            speedDataMap.put("maxHighDistance", maxHighDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt());
            speedDataMap.put("maxHighBallDistance", maxHighBallDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt());

            StudentInfo studentInfoMvp = studentInfoService.getById(mvp.getLongValue("studentId"));
            if (studentInfoMvp != null) {
                mvp.put("studentName", studentInfoMvp.getName());
                //MVP
                teamDataAo.setMvp(mvp);
            }
        } else {
            UserApp maxSprintSpeedStu = userAppService.getById(maxSprintSpeedPersonData.getStudentId());
            UserApp maxHighDistanceStu = userAppService.getById(maxHighDistancePersonData.getStudentId());
            UserApp maxHighBallDistanceStu = userAppService.getById(maxHighBallDistancePersonData.getStudentId());
            speedDataMap.put("maxSprintSpeedStudentName", maxSprintSpeedStu.getName());
            speedDataMap.put("maxHighDistanceStudentName", maxHighDistanceStu.getName());
            speedDataMap.put("maxHighBallDistanceStudentName", maxHighBallDistanceStu.getName());

            speedDataMap.put("maxSprintSpeedStudentHeadImg", maxSprintSpeedStu.getHeadImg());
            speedDataMap.put("maxHighDistanceStudentHeadImg", maxHighDistanceStu.getHeadImg());
            speedDataMap.put("maxHighBallDistanceStudentHeadImg", maxHighBallDistanceStu.getHeadImg());

            speedDataMap.put("maxSprintSpeed", maxSprintSpeedPersonData.getMaxSprintSpeed());
            speedDataMap.put("maxHighDistance", maxHighDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).findFirst().getAsInt());
            speedDataMap.put("maxHighBallDistance", maxHighBallDistancePersonData.getSpeedDataList().toJavaObject(JSONArray.class).stream().filter(e -> ((JSONObject) e).getString("speed").equals("SUP")).mapToInt(e -> ((JSONObject) e).getInteger("ballDistance")).findFirst().getAsInt());

            UserApp studentInfoMvp = userAppService.getById(mvp.getLongValue("studentId"));
            if (studentInfoMvp != null) {
                mvp.put("studentName", studentInfoMvp.getName());
                //MVP
                teamDataAo.setMvp(mvp);
            }
        }

        JSONObject runningForm = new JSONObject();
        Long avgTouchLandTime = 0l;
        Double avgTouchdownImpact = 0.0;
        Integer avgEctropionRange = null;
        Integer avgPendulumAngle = null;

        avgTouchLandTime = (long) personDatas.stream().mapToLong(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0l;
            } else {
                return e.getRunningForm().getLongValue("avgTouchLandTime");
            }
        }).average().orElse(0.0);

        avgTouchdownImpact = personDatas.stream().mapToDouble(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0.0;
            } else {
                return e.getRunningForm().getDouble("avgTouchdownImpact");
            }
        }).average().orElse(0.0);

        avgEctropionRange = (int) personDatas.stream().mapToInt(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0;
            } else {
                return e.getRunningForm().getInteger("avgEctropionRange");
            }
        }).average().orElse(0.0);

        avgPendulumAngle = (int) personDatas.stream().mapToInt(e -> {
            if (e.getRunningForm() == null || e.getRunningForm().size() == 0) {
                return 0;
            } else {
                return e.getRunningForm().getInteger("avgPendulumAngle");
            }
        }).average().orElse(0.0);

        runningForm.put("avgTouchLandTime", avgTouchLandTime);
        BigDecimal bd = new BigDecimal(avgTouchdownImpact);
        runningForm.put("avgTouchdownImpact", bd.setScale(2, BigDecimal.ROUND_DOWN).doubleValue());
        runningForm.put("avgEctropionRange", avgEctropionRange);
        runningForm.put("avgPendulumAngle", avgPendulumAngle);
        teamDataAo.setRunningForm(runningForm);

        getDepthData(teamDataAo, personDatas);
        //calculateTeamAvg(personDatas, teamDataAo, null);
        return teamDataAo;
    }

    //深度数据
    public void getDepthData(TeamDataAo teamDataAo, List<PersonData> personDatas) {
        JSONArray runDistances = new JSONArray();
        JSONArray maxSprintSpeeds = new JSONArray();
        JSONArray leftOrRightTurnings = new JSONArray();
        JSONArray startingsOrBrakes = new JSONArray();
        JSONArray breakOuts = new JSONArray();
        JSONArray overallSpeeds = new JSONArray();
        JSONArray overallSpeedCounts = new JSONArray();

        Map<Long, UserApp> stuMap = userAppService.getByTeamId(teamDataAo.getTeamId()).stream().collect(Collectors.toMap(UserApp::getId, Function.identity(), (key1, key2) -> key2));
        personDatas.forEach(e -> {
            UserApp studentInfo = stuMap.get(e.getStudentId());
            if (studentInfo == null) {
                studentInfo = userAppService.getById(e.getStudentId());
            }
            if (studentInfo != null) {
                //       [{"count": 0, "speed": "HIGH", "distance": 0, "ballCount": 0, "ballDistance": 0}]
                JSONObject overallSpeed = new JSONObject();
                overallSpeed.put("studentId", e.getStudentId());
                overallSpeed.put("card", studentInfo.getCard());
                overallSpeed.put("name", studentInfo.getName());
                JSONObject overallSpeedCount = new JSONObject();
                overallSpeedCount.put("studentId", e.getStudentId());
                overallSpeedCount.put("card", studentInfo.getCard());
                overallSpeedCount.put("name", studentInfo.getName());
                JSONArray speedDataList = JSON.parseArray(e.getSpeedDataList().toJSONString());
                for (int i = 0; i < speedDataList.size(); i++) {
                    JSONObject speed = speedDataList.getJSONObject(i);
                    overallSpeed.put(speed.getString("speed"), speed.getInteger("distance"));
                    if (speed.getString("speed").equals("EXCEED")) {
                        overallSpeedCount.put("exceedCount", speed.getInteger("count"));
                    } else if ((speed.getString("speed").equals("SUP"))) {
                        overallSpeedCount.put("supCount", speed.getInteger("count"));
                    }
                }
                if (overallSpeed.get("EXCEED") == null || overallSpeed.get("SUP") == null) {//之前的数据防止报错
                    overallSpeed.put("EXCEED", 0);
                    overallSpeedCount.put("exceedCount", 0);
                    overallSpeed.put("SUP", 0);
                    overallSpeedCount.put("supCount", 0);
                } else { //进入到这里代表有值 overallSpeedCount添加sum值方面排序
                    overallSpeedCount.put("sum", overallSpeedCount.getInteger("exceedCount") + overallSpeedCount.getInteger("supCount"));
                }

                overallSpeeds.add(overallSpeed);
                overallSpeedCounts.add(overallSpeedCount);

                JSONObject runDistance = new JSONObject();
                runDistance.put("studentId", e.getStudentId());
                runDistance.put("card", studentInfo.getCard());
                runDistance.put("name", studentInfo.getName());
                runDistance.put("run", e.getRunDistance());
                double avg = (double) e.getRunDistance() / e.getExerciseTime();
                double result = new BigDecimal(avg).setScale(1, BigDecimal.ROUND_UP).doubleValue();
                runDistance.put("avg", result);
                runDistances.add(runDistance);

                JSONObject maxSprintSpeed = new JSONObject();
                maxSprintSpeed.put("studentId", e.getStudentId());
                maxSprintSpeed.put("card", studentInfo.getCard());
                maxSprintSpeed.put("name", studentInfo.getName());
                maxSprintSpeed.put("value", e.getMaxSprintSpeed());
                maxSprintSpeeds.add(maxSprintSpeed);

                JSONObject o = e.getDepthData();
                if (o == null) {
                    o = new JSONObject();
                }
                JSONObject leftOrRightTurning = new JSONObject();
                leftOrRightTurning.put("studentId", e.getStudentId());
                leftOrRightTurning.put("card", studentInfo.getCard());
                leftOrRightTurning.put("name", studentInfo.getName());
                leftOrRightTurning.put("left", o.getInteger("leftTurning") == null ? 0 : o.getInteger("leftTurning"));
                leftOrRightTurning.put("right", o.getInteger("rightTurning") == null ? 0 : o.getInteger("rightTurning"));
                leftOrRightTurning.put("sum", leftOrRightTurning.getInteger("left") + leftOrRightTurning.getInteger("right"));
                leftOrRightTurnings.add(leftOrRightTurning);

                JSONObject startingsOrBrake = new JSONObject();
                startingsOrBrake.put("studentId", e.getStudentId());
                startingsOrBrake.put("card", studentInfo.getCard());
                startingsOrBrake.put("name", studentInfo.getName());
                startingsOrBrake.put("starting", o.getInteger("starting") == null ? 0 : o.getInteger("starting"));
                startingsOrBrake.put("brake", o.getInteger("brake") == null ? 0 : o.getInteger("brake"));
                startingsOrBrake.put("sum", startingsOrBrake.getInteger("brake") + startingsOrBrake.getInteger("starting"));
                startingsOrBrakes.add(startingsOrBrake);

                JSONObject breakOut = new JSONObject();
                breakOut.put("studentId", e.getStudentId());
                breakOut.put("card", studentInfo.getCard());
                breakOut.put("name", studentInfo.getName());
                breakOut.put("breakOut", o.getInteger("breakOut") == null ? 0 : o.getInteger("breakOut"));
                breakOuts.add(breakOut);
            }
        });

        sortJSONArray(runDistances, "run");
        teamDataAo.setRunDistances(runDistances);

        sortJSONArray(overallSpeeds, "SUP");
        teamDataAo.setOverallSpeeds(overallSpeeds);

        sortJSONArray(overallSpeedCounts, "SUP");
        teamDataAo.setOverallSpeedCounts(overallSpeedCounts);

        sortJSONArray(maxSprintSpeeds, "value");
        teamDataAo.setMaxSprintSpeeds(maxSprintSpeeds);

        sortJSONArray(leftOrRightTurnings, "sum");
        teamDataAo.setLeftOrRightTurnings(leftOrRightTurnings);

        sortJSONArray(startingsOrBrakes, "sum");
        teamDataAo.setStartingsOrBrakes(startingsOrBrakes);

        sortJSONArray(breakOuts, "breakOut");
        teamDataAo.setBreakOuts(breakOuts);
    }

    public void calculateTeamAvg(List<PersonData> personDatas, TeamDataAo teamDataAo, PersonDataAo personDataAo) {
        JSONObject overallSpeed = new JSONObject();
        JSONObject overallSpeedCount = new JSONObject();
        JSONObject depthData = new JSONObject();
        if (personDatas == null) {//没有团队数据
            overallSpeed.put("LOW", -1);
            overallSpeed.put("MID", -1);
            overallSpeed.put("HIGH", -1);
            overallSpeed.put("EXCEED", -1);
            overallSpeed.put("SUP", -1);
            overallSpeedCount.put("exceedCount", -1);
            overallSpeedCount.put("supCount", -1);
            depthData.put("brake", -1);
            depthData.put("breakOut", -1);
            depthData.put("starting", -1);
            depthData.put("leftTurning", -1);
            teamDataAo.setTeamAvgDepthData(depthData);
            teamDataAo.setTeamAvgOverallSpeed(overallSpeed);
            teamDataAo.setTeamAvgOverallSpeedCount(overallSpeedCount);
            teamDataAo.setTeamAvgMaxSprintSpeed(-1.0);
            return;
        }
        overallSpeed.put("LOW", 0);
        overallSpeed.put("MID", 0);
        overallSpeed.put("HIGH", 0);
        overallSpeed.put("EXCEED", 0);
        overallSpeed.put("SUP", 0);
        overallSpeedCount.put("exceedCount", 0);
        overallSpeedCount.put("supCount", 0);
        depthData.put("brake", 0);
        depthData.put("breakOut", 0);
        depthData.put("starting", 0);
        depthData.put("leftTurning", 0);
        depthData.put("rightTurning", 0);
        personDatas.forEach(e -> {
            JSONArray speedDataList = JSON.parseArray(e.getSpeedDataList().toJSONString());
            for (int i = 0; i < speedDataList.size(); i++) {
                JSONObject speed = speedDataList.getJSONObject(i);
                overallSpeed.put(speed.getString("speed"), overallSpeed.getInteger(speed.getString("speed")) + speed.getInteger("distance"));
                if (speed.getString("speed").equals("EXCEED")) {
                    overallSpeedCount.put("exceedCount", overallSpeedCount.getInteger("exceedCount") + speed.getInteger("count"));
                } else if ((speed.getString("speed").equals("SUP"))) {
                    overallSpeedCount.put("supCount", overallSpeedCount.getInteger("supCount") + speed.getInteger("count"));
                }
            }

            JSONObject depthDataTeam = e.getDepthData();
            if (depthDataTeam != null) {
                depthData.put("brake", depthDataTeam.getInteger("brake") + depthData.getInteger("brake"));
                depthData.put("breakOut", depthDataTeam.getInteger("breakOut") + depthData.getInteger("breakOut"));
                depthData.put("starting", depthDataTeam.getInteger("starting") + depthData.getInteger("starting"));
                depthData.put("leftTurning", depthDataTeam.getInteger("leftTurning") + depthData.getInteger("leftTurning"));
                depthData.put("rightTurning", depthDataTeam.getInteger("rightTurning") + depthData.getInteger("rightTurning"));
            }
        });
        int avgCount = personDatas.stream().filter(e -> e.getStepCount() > 100).collect(Collectors.toList()).size();
        if (avgCount == 0) {
            avgCount = 1;
        }
        overallSpeed.put("LOW", overallSpeed.getInteger("LOW") / avgCount);
        overallSpeed.put("MID", overallSpeed.getInteger("MID") / avgCount);
        overallSpeed.put("HIGH", overallSpeed.getInteger("HIGH") / avgCount);
        overallSpeed.put("EXCEED", overallSpeed.getInteger("EXCEED") / avgCount);
        overallSpeed.put("SUP", overallSpeed.getInteger("SUP") / avgCount);
        overallSpeedCount.put("exceedCount", overallSpeedCount.getInteger("exceedCount") / avgCount);
        overallSpeedCount.put("supCount", overallSpeedCount.getInteger("supCount") / avgCount);
        depthData.put("brake", depthData.getInteger("brake") / avgCount);
        depthData.put("breakOut", depthData.getInteger("breakOut") / avgCount);
        depthData.put("starting", depthData.getInteger("starting") / avgCount);
        depthData.put("leftTurning", depthData.getInteger("leftTurning") / avgCount);
        depthData.put("rightTurning", depthData.getInteger("rightTurning") / avgCount);


        double avgMaxSprintSpeed = personDatas.stream().mapToDouble(PersonData::getMaxSprintSpeed).average().getAsDouble();
        BigDecimal bd = new BigDecimal(avgMaxSprintSpeed);
        avgMaxSprintSpeed = bd.setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
        if (teamDataAo != null) {
            teamDataAo.setTeamAvgDepthData(depthData);
            teamDataAo.setTeamAvgOverallSpeed(overallSpeed);
            teamDataAo.setTeamAvgOverallSpeedCount(overallSpeedCount);
            teamDataAo.setTeamAvgMaxSprintSpeed(avgMaxSprintSpeed);
        } else {
            personDataAo.setTeamAvgDepthData(depthData);
            personDataAo.setTeamAvgOverallSpeed(overallSpeed);
            personDataAo.setTeamAvgOverallSpeedCount(overallSpeedCount);
            personDataAo.setTeamAvgMaxSprintSpeed(avgMaxSprintSpeed);
        }
    }

    /**
     * @desc: 数据计算和排名
     * @author: DH
     * @date: 2021/3/19 16:21
     */
    public JSONObject getRankingAndData(List<PersonData> personDataList, PersonDataAo personDataAo, String matchType) {
        JSONObject ranking = new JSONObject();//排名前
        JSONObject rankingAfter = new JSONObject();//排名后
        JSONObject rankingUpOrDown = new JSONObject();//比较值
        List<String> rankingKey = getRankingKey();
        rankingKey.stream().forEach(key -> ranking.put(key, new JSONArray()));
        //历史比赛数据
        List<PersonData> historPersonData = personDataService.findOfMatchIdByHist(personDataAo.getTeamId(), personDataAo.getMatchId(), matchType);
        personDataList.stream().forEach(e -> {
            //步数
            JSONObject o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getStepCount());
            ranking.getJSONArray("stepCount").add(o);
            //跑动
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getRunDistance());
            ranking.getJSONArray("runDistance").add(o);
            // 最高冲刺速度
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", e.getMaxSprintSpeed());
            ranking.getJSONArray("maxSprintSpeed").add(o);
            // 高速跑动距离
            o = new JSONObject();
            List<Object> speedDatas = e.getSpeedDataList().toJavaObject(JSONArray.class).stream().collect(Collectors.toList());
            o.put("k", e.getStudentId());
            o.put("v", speedDatas.stream().filter(a -> ((JSONObject) a).getString("speed").equals("SUP")).mapToInt(a -> ((JSONObject) a).getInteger("distance")).sum());
            ranking.getJSONArray("highDistance").add(o);
            // 起跳
            o = new JSONObject();
            o.put("k", e.getStudentId());
            o.put("v", ((JSONObject) e.getPose()).getJSONObject("jump").getIntValue("count"));
            ranking.getJSONArray("jump").add(o);
        });

        rankingUpOrDown.put("stepCount", historPersonData.stream().mapToInt(a -> a.getStepCount()).average().orElse(0));
        rankingUpOrDown.put("runDistance", historPersonData.stream().mapToInt(a -> a.getRunDistance()).average().orElse(0));
        rankingUpOrDown.put("maxSprintSpeed", historPersonData.stream().mapToDouble(a -> a.getMaxSprintSpeed()).average().orElse(0));
        rankingUpOrDown.put("jump", historPersonData.stream().mapToDouble(a -> ((JSONObject) a.getPose()).getJSONObject("jump").getIntValue("count")).average().orElse(0));
        rankingUpOrDown.put("highDistance", historPersonData.stream().mapToDouble(a -> a.getSpeedDataList().toJavaObject(JSONArray.class).stream().collect(Collectors.toList())
                .stream().filter(c -> ((JSONObject) c).getString("speed").equals("HIGH")).mapToInt(c -> ((JSONObject) c).getInteger("distance")).sum()
        ).average().orElse(0));

        rankingKey.stream().forEach(e -> {
            JSONArray jsonArray = sortJSONArray(ranking.getJSONArray(e), "v");
            for (int i = 0; i < jsonArray.size(); i++) {
                if (jsonArray.getJSONObject(i).getLong("k").equals(personDataAo.getStudentId())) {
                    JSONObject o = new JSONObject();
                    double upOrDownValue = rankingUpOrDown.getDoubleValue(e);
                    double value = jsonArray.getJSONObject(i).getDoubleValue("v");
                    if (upOrDownValue == value) {
                        o.put("state", 1);
                    } else if (value > upOrDownValue) {
                        o.put("state", 2);
                    } else {
                        o.put("state", 0);
                    }
                    o.put("ranking", i + 1);
                    rankingAfter.put(e, o);
                    break;
                }
            }
        });
        return rankingAfter;
    }

    /**
     * @desc: 计算百分比 保留一个小数
     * @author: DH
     * @date: 2021/3/22 11:29
     */
    public static String rate(Integer v1, Integer v2) {
        if (v1 == 0 || v2 == 0) {
            return 0 + "";
        }
        Integer chapterCount = v1;
        Integer learnCount = v2;
        // 创建一个数值格式化对象
        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后2位
        numberFormat.setMaximumFractionDigits(1);
        String result = numberFormat.format((float) chapterCount / (float) learnCount);//所占百分比
        return result;
    }

    /**
     * @desc: 跑动和传球排名
     * @author: DH
     * @date: 2021/6/11 15:25
     */
    public void runAndPassRanking(List<PersonData> personData, JSONArray runRanking) {
        personData.stream().forEach(e -> {
            JSONObject runMap = new JSONObject();
            runMap.put("studentId", e.getStudentId());
            runMap.put("runDistance", e.getRunDistance());
            runRanking.add(runMap);
        });
        //进行排名
        sortJSONArray(runRanking, "runDistance");
    }

    /**
     * @desc: JSONArray排序
     * @author: DH
     * @date: 2021/3/13 14:09
     */
    public JSONArray sortJSONArray(JSONArray jsonArray, String sortKey) {
        jsonArray.sort((o1, o2) -> {
            Double v1 = JSONObject.parseObject(JSON.toJSONString(o1)).getDoubleValue(sortKey);
            Double v2 = JSONObject.parseObject(JSON.toJSONString(o2)).getDoubleValue(sortKey);
            return Double.compare(v2, v1);
        });
        return jsonArray;
    }

    /**
     * @desc: 计算劳模值
     * @author: DH
     * @date: 2021/3/13 14:16
     */
    public double mvpPrice(long run, long pass) {
        BigDecimal bg = new BigDecimal(pass * 0.45 + run * 0.55);
        return bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * @desc: 获取个人数据需要排名的KEY
     * @author: DH
     * @date: 2021/3/17 15:11
     */
    public List<String> getRankingKey() {
        List<String> keys = new ArrayList<>();
        keys.add("stepCount");//步数
        keys.add("runDistance");//跑动
        keys.add("jump");//起跳
        keys.add("maxSprintSpeed");//最高冲刺速度
        keys.add("highDistance");//高速跑动距离
        return keys;
    }
}
