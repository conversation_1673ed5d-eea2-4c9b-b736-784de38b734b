package io.geekidea.springbootplus.using.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StudentInfo对象")
@TableName(autoResultMap = true)
public class StudentInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("学生id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "学校id不能为空")
    @ApiModelProperty("学校id")
    private Long schoollId;

    @ApiModelProperty("班级id")
    private Long teamId;

    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("身高（厘米）")
    private Double height;

    @ApiModelProperty("体重（kg）")
    private Double weight;

    @ApiModelProperty("出生年月")
    private Date birthday;

    @ApiModelProperty("球鞋尺寸")
    private String size;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("密码")
    private String passwd;

    @ApiModelProperty("友盟设备唯一id")
    private String deviceTokens;

    @ApiModelProperty("惯用脚")
    private String habit;

    @ApiModelProperty("性别 0女 1男")
    private Integer sex;

    @ApiModelProperty("加入时间")
    private Date joinDate;

    @ApiModelProperty("头像")
    private String headImg;

    @ApiModelProperty("证书")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List certificate;

    @ApiModelProperty("手机号码")
    private String phone;

    @ApiModelProperty("简介")
    private String introduction;

    @ApiModelProperty("App学员绑定的设备")
    @TableField(exist = false)
    private HardwareApp hardwareApp;

    @ApiModelProperty("Pad学员绑定的设备")
    @TableField(exist = false)
    private Hardware hardware;

    @ApiModelProperty("训练是否作废 ---获取 班级下学生列表 类似PAD查看球鞋状态 才有")
    @TableField(exist = false)
    private Boolean cancel;

    @TableField(exist = false)
    @ApiModelProperty("是否已经同步数据 ---获取 班级下学生列表 类似PAD查看球鞋状态 才有")
    private Boolean uploaded;

    @ApiModelProperty("是否出勤 ---获取 班级下学生列表 类似PAD查看球鞋状态 才有")
    @TableField(exist = false)
    private Boolean status;

    @ApiModelProperty("是否首发 ---获取 班级下学生列表 类似PAD查看球鞋状态 才有")
    @TableField(exist = false)
    private Boolean starter;

    @ApiModelProperty("学员当天运动是否达标")
    @TableField(exist = false)
    private Boolean sportQualified;

    @ApiModelProperty("当天运动时间 分钟")
    @TableField(exist = false)
    private Long exerciseTime;

    @ApiModelProperty("学号")
    private String card;

    @ApiModelProperty("篮球球衣号")
    private Integer shirt;

    @ApiModelProperty("足球球衣号")
    private Integer footballShirt;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    @ApiModelProperty("登录成功的token")
    private String token;

    @ApiModelProperty("班级名称")
    @TableField(exist = false)
    private String teamName;

    @ApiModelProperty("学校名称")
    @TableField(exist = false)
    private String schoolName;

    @ApiModelProperty("所属球队类型  2篮球 3足球 4数字体育课")
    @TableField(exist = false)
    private Long courseId;

    @ApiModelProperty("TeamBox")
    @TableField(exist = false)
    private Integer teamboxId;

    @ApiModelProperty("正常考勤次数")
    @TableField(exist = false)
    private Integer normal;

    @ApiModelProperty("缺勤次数")
    @TableField(exist = false)
    private Integer defect;

    @ApiModelProperty("注册验证码")
    @TableField(exist = false)
    private String phoneCode;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Boolean deleted;
}
