package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.CourseAmount;
import io.geekidea.springbootplus.using.param.CourseAmountPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-02-02
 */
public interface CourseAmountService extends BaseService<CourseAmount> {

    /**
     * 保存
     *
     * @param courseAmount
     * @return
     * @throws Exception
     */
    boolean saveCourseAmount(CourseAmount courseAmount) throws Exception;

    /**
     * 修改
     *
     * @param courseAmount
     * @return
     * @throws Exception
     */
    boolean updateCourseAmount(CourseAmount courseAmount) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteCourseAmount(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param courseAmountQueryParam
     * @return
     * @throws Exception
     */
    Paging<CourseAmount> getCourseAmountPageList(CourseAmountPageParam courseAmountPageParam) throws Exception;

}
