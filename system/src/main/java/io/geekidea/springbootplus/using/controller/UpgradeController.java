package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.using.arg.SystemArg;
import io.geekidea.springbootplus.using.common.RequestCommonMethod;
import io.geekidea.springbootplus.using.entity.FacilityGroup;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.entity.Team;
import io.geekidea.springbootplus.using.entity.UserApp;
import io.geekidea.springbootplus.using.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/using/upgrade")
@Module("teambox")
@Api(value = "获取升级信息", tags = {"获取升级信息"})
public class UpgradeController {

    @Autowired
    RequestCommonMethod requestCommonMethod;
    @Autowired
    private SystemArg systemArg;
    @Autowired
    TransparentMessageService transparentMessageService;

    @Autowired
    UserAppService userAppService;

    @Autowired
    StudentInfoService studentInfoService;

    @Autowired
    FacilityGroupService facilityGroupService;

    @Autowired
    TeamService teamService;

    @GetMapping("/getDownloadUrl")
    @ApiOperation(value = "获取app升级信息", response = ApiResult.class, notes = "参考PAD ")
    public ApiResult getDownloadUrl() {
        String s = requestCommonMethod.postVsteam(systemArg.getVsteam_url_prefix() + "/files/images/android/getDownloadUrl", null);
        if (s == null) {//防止vsteam项目 更新固件接口失败 影响pad的登录
            System.out.println("getDownloadUrl Error");
            s = StringEscapeUtils.unescapeJava("[{\"versionCode\":56,\"versionName\":\"2.2.19\",\"updateInfo\":\"-新增课件推荐快捷使用功能;-上版本Bug修正\",\"updateInfoEng\":\"-Add the quick use function of courseware recommendation;-Bug correction of previous version\",\"downloadUrl\":\"https://file-microteam.oss-cn-shenzhen.aliyuncs.com/MicroTeam.apk\",\"diffUpdate\":1,\"isUpdate\":3,\"appDesc\":\"1.支持微队自带的智能足球、智能篮球可穿戴设备;2.业余球队管理神器、深刻理解业余球队需求;3.快速寻找特价球场、免费球场;4.一个有情怀、略文艺的球队社交App;\",\"appDescEng\":\"1. Support the wearable devices of smart football and smart basketball brought by the micro team;2. Manage the talent of amateur team and deeply understand the needs of amateur team;3. Quickly find special courses and free courses;\",\"firmwareGpsVersion\":\"4.3.50\",\"firmwareGpsInfo\":\"-优化起跳算法;-修改一些已知bug\",\"firmwareGpsInfoEng\":\"-Optimize the take-off algorithm;-Fixed few known bugs\",\"firmwareGpsIsUpdate\":3,\"firmwareGpsUrl\":\"https://file-microteam.oss-cn-shenzhen.aliyuncs.com/AI_FB_4.3.50.zip\",\"firmwareVersion\":\"2.2.28\",\"firmwareInfo\":\"-修改一些已知bug\",\"firmwareInfoEng\":\"-fixed few known bugs\",\"firmwareIsUpdate\":3,\"firmwareUrl\":\"https://www.microteam.cn/vsteam/upload/firmware/AI_FB_2.2.28_2020706_alpha.zip\",\"factoryUrl\":\"https://www.microteam.cn/vsteam/upload/firmware/TACKE_1_.0.3_2020421_factory_.zip\",\"factoryVersion\":\"1.0.3\",\"factoryUrlGps\":\"https://www.microteam.cn/vsteam/upload/firmware/TACKE_2_.0.2_2020420_factory_gps.zip\",\"factoryGpsVersion\":\"2.0.2\",\"firmwareSdk17Version\":\"7.2.3\",\"firmwareSdk17Info\":\"-优化起跳算法;-修改一些已知bug\",\"firmwareSdk17InfoEng\":\"-Optimize the take-off algorithm;-Fixed few known bugs\",\"firmwareSdk17IsUpdate\":3,\"firmwareSdk17Url\":\"https://file-microteam.oss-cn-shenzhen.aliyuncs.com/AI_FB_7.2.3_20211124.zip\",\"factorySdk17Url\":\"https://file-microteam.oss-cn-shenzhen.aliyuncs.com/AI_FB_7.0.3_20211124.zip\",\"factorySdk17Version\":\"7.0.3\",\"pc_apkUrl\":\"https://file-microteam.oss-cn-shenzhen.aliyuncs.com/MicroTeamEvaluate.apk\",\"pc_apkVersion\":\"*******.2\",\"pc_apkInfo\":null,\"pc_apkInfoEng\":null,\"pc_apkUpdate\":3,\"HD_Coach_apkUrl\":\"https://file-microteam.oss-cn-shenzhen.aliyuncs.com/MicroTeamHD.apk\",\"HD_Coach_apkVersion\":\"*******\",\"HD_Coach_apkInfo\":null,\"HD_Coach_apkInfoEng\":null,\"HD_Coach_apkIsUpdate\":3,\"teamboxUrl\":null,\"teamboxVersion\":null,\"teamboxInfo\":null,\"teamboxInfoEng\":null,\"teamboxIsUpdate\":3,\"factory_teamBoxUrl\":null,\"factory_teamBoxVersion\":null,\"factory_teamBoxInfo\":null,\"factory_teamBoxInfoEng\":null,\"factory_teamBoxIsUpdate\":3}]");
        }
        return ApiResult.ok(JSONArray.parseArray(s));
    }

    @PostMapping("/infromTag")
    @ApiOperation(value = "硬件orApp更新发送透传", response = ApiResult.class, notes = "参考PAD ")
    public ApiResult infromTag(@RequestBody JSONObject object) {
        org.json.JSONObject o = new org.json.JSONObject(object.getInnerMap());
        transparentMessageService.sedTransparent(o);
        return ApiResult.result(true);
    }

    @GetMapping("/test")
    public ApiResult test() throws Exception {
        List<FacilityGroup> facilityGroups = facilityGroupService.list(new LambdaQueryWrapper<FacilityGroup>());
        for (FacilityGroup facilityGroup : facilityGroups) {
            if (facilityGroup.getTeams().size() > 0) {
                Long teamId = facilityGroup.getTeams().getLong(0);
                Team team = teamService.getById(teamId);
                facilityGroup.setTeacherId(team.getTeacherId());
            }
        }
        return ApiResult.result(facilityGroupService.saveOrUpdateBatch(facilityGroups));
    }
}
