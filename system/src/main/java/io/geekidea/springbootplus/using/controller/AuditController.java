package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.using.entity.Audit;
import io.geekidea.springbootplus.using.service.AuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/using/audit")
@Module("teambox")
@Api(value = "审核", tags = {"审核"})
public class AuditController {
    @Autowired
    AuditService auditService;

    /**
     * 职业赛事等级提交审核
     */
    @PostMapping("/gradeAudit")
    @OperationLog(name = "职业赛事等级提交审核", type = OperationLogType.DELETE)
    @ApiOperation(value = "职业赛事等级提交审核", response = ApiResult.class)
    public ApiResult<Boolean> gradeAudit(@RequestBody Audit audit) throws Exception {
        boolean flag = auditService.gradeAudit(audit);
        return ApiResult.result(flag);
    }
}
