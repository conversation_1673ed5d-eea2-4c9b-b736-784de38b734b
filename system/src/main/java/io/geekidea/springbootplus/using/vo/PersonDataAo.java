package io.geekidea.springbootplus.using.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.using.entity.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "PersonDataAo")
public class PersonDataAo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 球赛id
     */
    private Long matchId;

    private Double bmi;


    /**
     * 学校log
     */
    private String schoolLogo;

    /**
     * 学校Nmae
     */
    private String schoolName;

    /**
     * 学校公众号
     */
    private String schoolPub;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 团队名
     */
    private String teamName;

    /**
     * 学员id
     */
    private Long studentId;

    /**
     * 学员名字
     */
    private String studentName;

    /**
     * 步数
     */
    private int stepCount;

    /**
     * 跑动次数
     */
    private Integer runCount;

    /**
     * 最大冲刺速度(km/h)
     */
    private Double maxSprintSpeed;

    /**
     * 跑动距离
     */
    private Integer runDistance;

    /**
     * 卡路里(kcal）
     */
    private Integer calorie;

    /**
     * 团队平均卡路里
     */
    private Integer avgCalorie;

    /**
     * 运动时间（秒）
     */
    private long exerciseTime;

    /**
     * 速度数据
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON speedDataList;

    /**
     * 个人曲线
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON curveList;

    /**
     * 动作
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON pose;

    /**
     * 个人历史消耗卡路里
     */
    private Integer historyCalorie;

    /**
     * 学员信息
     */
    private StudentInfo studentInfo;

    /**
     * APP学员信息
     */
    private UserApp userApp;

    /**
     * 排名
     */
    private JSONObject ranking;

    /**
     * 记时/计数/场记 数据
     */
    private JSONObject extra;

    /**
     * 本场球鞋数量
     */
    private int gameHardwareCount;

    /**
     * 球赛开始时间
     */
    private Date competitionTime;

    /**
     * 训练信息
     */
    private Object drillInfo;

    /**
     * 训练信息
     */
    private Game gameInfo;

    /**
     * 监测信息
     */
    private KindergartenMonitor kindergartenMonitor;

    /**
     * 起跳次数
     */
    private Integer jumpCount;

    /**
     * 起跳平均高度 m米
     */
    private Integer jumpAvgHeight;

    /**
     * 速度步伐
     */
    private JSONObject pace;

    /**
     * 平均触地时间
     */
    private Integer avgTouchDownTime;

    /**
     * 跑步姿势
     */
    private JSONObject runningForm;

    /**
     * 着地方式
     */
    private JSONObject touchdownWay;

    /**
     * 配速
     */
    private JSONArray speedAllocation;

    /**
     * 深度数据
     */
    private JSONObject depthData;

    private JSONObject overallSpeed;

    private JSONObject overallSpeedCount;

    /**
     * 深度数据
     */
    private JSONObject teamAvgDepthData;

    private JSONObject teamAvgOverallSpeed;

    private JSONObject teamAvgOverallSpeedCount;

    /**
     * 团队平均最大冲刺速度(km/h)
     */
    private Double teamAvgMaxSprintSpeed;

    //最长滞空时间
    private Integer maxDuration;

    //平均滞空时间
    private Integer avgDuration;

    //最远起跳距离
    private Integer maxTakeOffDistance;

    //最大起跳高度
    private Integer maxTakeOffHeight;

    private Object mvp;

    /**
     * 球队全称
     */
    private String fullName;

    /**
     * 球队简称
     */
    private String shortName;

    private MatchRemark matchRemark;

    private JSONObject analysisOfMotion;//运动习惯

    private long runExerciseTime;//跑动运动时间

    private long stepExerciseTime;//步行运动时间

   private JSONObject tndicator;//各项指标达标情况

    private JSONArray livenessMinute;

    private Integer hardType;

    private JSONObject distances;

}