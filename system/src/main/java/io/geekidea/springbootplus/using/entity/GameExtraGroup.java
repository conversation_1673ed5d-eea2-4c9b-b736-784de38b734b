package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GameExtraGroup对象")
@TableName(autoResultMap = true)
public class GameExtraGroup extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("APP不需要传")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("记时/计数Id  APP不需要传")
    private Long gameExtraId;

    @ApiModelProperty("小组名")
    private String name;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    @ApiModelProperty("记时 计数数据  格式： [{\"id\":\"学生id Number\",\"code\":\"是否扫码 true\",\"turns\":\" 圈数\",\"name\":\"学生名\",\"headImg\":\"头像\",\"count\":\"计数数量 Number\",\"startTime\":\"开始时间戳 Number\",\"stopTime\":\"结束时间戳 Number\"}]\n" +
            "}]  上传不需要学生姓名和头像字段 ")
    private JSONArray users;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
