package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.MatchRemark;
import io.geekidea.springbootplus.using.param.MatchRemarkPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-08-12
 */
public interface MatchRemarkService extends BaseService<MatchRemark> {
    MatchRemark getMatchRemark(String matchType,Long matchId,Long studentId);

    /**
     * 保存
     *
     * @param matchRemark
     * @return
     * @throws Exception
     */
    boolean saveMatchRemark(MatchRemark matchRemark) throws Exception;

    /**
     * 修改
     *
     * @param matchRemark
     * @return
     * @throws Exception
     */
    boolean updateMatchRemark(MatchRemark matchRemark) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteMatchRemark(String matchType,Long matchId,Long stuId) throws Exception;



}
