package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 修改大图URL参数
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@ApiModel("修改大图URL参数")
public class UpdateBigUrlParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("大图URL")
    @NotBlank(message = "大图URL不能为空")
    private String bigUrl;
}
