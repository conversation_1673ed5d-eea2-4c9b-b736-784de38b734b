package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "HardwareApp对象")
public class HardwareApp  extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "学生id 不能为空")
    @ApiModelProperty("学生id or APP用户id userId")
    private Long studentId;

    @NotNull(message = "学校id不能为空")
    @ApiModelProperty("学校id")
    private Long schoollId;

    @ApiModelProperty("班级id")
    private Long teamId;

    @ApiModelProperty("学生信息")
    @TableField(exist = false)
    private StudentInfo studentInfo;

    @NotBlank(message = "mac码不能为空")
    @ApiModelProperty("mac码")
    private String mac;

    @ApiModelProperty("箱子编号")
    private String box;

    @NotNull(message = "编号不能为空")
    @ApiModelProperty("编号")
    private Integer num;

    @ApiModelProperty("长编号")
    private String longNum;

    @ApiModelProperty("设备版本")
    private String version;

    @ApiModelProperty("设备名称")
    private String hardwareName;

    @ApiModelProperty("初次启动时间")
    private Date firstStartTime;

    @ApiModelProperty("电量")
    private Integer battery;

    @ApiModelProperty("电量最近上传时间")
    private Date batteryTime;

    @ApiModelProperty("设备是否启动于当前比赛")
    @TableField(exist = false)
    private Boolean started = false;

    @ApiModelProperty("是否解绑")
    private Boolean unbind;

    @ApiModelProperty("1.标准版 2.专业版")
    private Integer hardType;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
