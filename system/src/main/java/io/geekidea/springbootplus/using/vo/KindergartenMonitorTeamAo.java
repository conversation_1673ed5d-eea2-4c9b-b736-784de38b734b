package io.geekidea.springbootplus.using.vo;

import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "KindergartenMonitorTeamAo")
public class KindergartenMonitorTeamAo {
    @ApiModelProperty("监测id 没有的话给-1")
    private Long monitorId = -1L;
    @ApiModelProperty("监测状态(未开始:NOTSTARTED,已开始:STARTED,已结束:FINISHED) 新添加传入未开始状态")
    private MatchStatusEnum status;
    @ApiModelProperty("球队id")
    private Long teamId;
    @ApiModelProperty("球队名字")
    private String teamName;
    @ApiModelProperty("保护套颜色")
    private String caseColor;
    @ApiModelProperty("启动设备人数")
    private long hardware;
    @ApiModelProperty("运动达标人数")
    private long exerciseStandardNum;
    @ApiModelProperty("启动监测时间")
    private long startTime=-1l;
    @ApiModelProperty("班级总人数")
    private int peopleNum;
    @ApiModelProperty(" 设置为体育监测班级的时间")
    private Date sportsMonitorTime;
    @ApiModelProperty("监测排序号")
    private Integer monitorSort;


    @ApiModelProperty("学生概览")
    private List<ExerciseConditionAo> exerciseConditionAos;

}
