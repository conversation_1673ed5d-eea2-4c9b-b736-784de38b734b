package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "StudentHardware对象")
public class StudentHardware extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "学员id不能为空")
    @ApiModelProperty("学员id")
    private Long studentId;

    @NotNull(message = "球队id不能为空")
    @ApiModelProperty("球队id")
    private Long teamId;

    @NotNull(message = "鞋子id不能为空")
    @ApiModelProperty("鞋子id")
    private Long hardwareId;

    @NotNull(message = "是否已经启动不能为空")
    @ApiModelProperty("是否已经启动")
    private Boolean started;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @ApiModelProperty("是否解绑")
    private Boolean unbind;

}
