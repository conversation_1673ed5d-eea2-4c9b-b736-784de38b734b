package io.geekidea.springbootplus.using.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.geekidea.springbootplus.using.entity.Game;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.util.Date;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Repository
public interface GameMapper extends BaseMapper<Game> {

    Game getLastNoUploadAndNoCancel(@Param("teamIds")List<Long> teamIds);

    Game getLastNoUploadAndNoCancelApp(@Param("teamIds")List<Long> teamIds);

    Game getLastNoUploadAndNoCancelStu(@Param("stuId")Long stuId);

    Game getLastNoUploadAndNoCancelStuApp(@Param("stuId")Long stuId);

    IPage<Game> getGrowUpData(Page<Game> page, @Param("teamId")Long teamId, @Param("studentId") Long studentId, @Param("befor") Date befor, @Param("after") Date after);

}
