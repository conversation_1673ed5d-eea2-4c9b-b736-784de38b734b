package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.using.entity.TeamPay;
import io.geekidea.springbootplus.using.service.TeamPayService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.FacilityGroupPayPageParam;
import io.geekidea.springbootplus.framework.common.controller.BaseController;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Slf4j
@RestController
@RequestMapping("/facilityGroupPay")
@Module("${cfg.module}")
@Api(value = "API", tags = {""})
public class TeamPayController extends BaseController {

    @Autowired
    private TeamPayService teamPayService;

    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class)
    public ApiResult<Boolean> addFacilityGroupPay(@Validated(Add.class) @RequestBody TeamPay teamPay) throws Exception {
        boolean flag = teamPayService.saveFacilityGroupPay(teamPay);
        return ApiResult.result(flag);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    public ApiResult<Boolean> updateFacilityGroupPay(@Validated(Update.class) @RequestBody TeamPay teamPay) throws Exception {
        boolean flag = teamPayService.updateFacilityGroupPay(teamPay);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteFacilityGroupPay(@PathVariable("id") Long id) throws Exception {
        boolean flag = teamPayService.deleteFacilityGroupPay(id);
        return ApiResult.result(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = TeamPay.class)
    public ApiResult<TeamPay> getFacilityGroupPay(@PathVariable("id") Long id) throws Exception {
        TeamPay teamPay = teamPayService.getById(id);
        return ApiResult.ok(teamPay);
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = TeamPay.class)
    public ApiResult<Paging<TeamPay>> getFacilityGroupPayPageList(@Validated @RequestBody FacilityGroupPayPageParam facilityGroupPayPageParam) throws Exception {
        Paging<TeamPay> paging = teamPayService.getFacilityGroupPayPageList(facilityGroupPayPageParam);
        return ApiResult.ok(paging);
    }

}

