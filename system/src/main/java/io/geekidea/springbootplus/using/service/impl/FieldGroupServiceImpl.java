package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.FieldGroup;
import io.geekidea.springbootplus.using.mapper.FieldGroupMapper;
import io.geekidea.springbootplus.using.service.FieldGroupService;
import io.geekidea.springbootplus.using.param.FieldGroupPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Slf4j
@Service
public class FieldGroupServiceImpl extends BaseServiceImpl<FieldGroupMapper, FieldGroup> implements FieldGroupService {

    @Autowired
    private FieldGroupMapper fieldGroupMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveFieldGroup(FieldGroup fieldGroup) throws Exception {
        fieldGroup.setCreateTime(new Date());
        fieldGroup.setUpdateTime(new Date());
        return super.save(fieldGroup);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateFieldGroup(FieldGroup fieldGroup) throws Exception {
        return super.updateById(fieldGroup);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteFieldGroup(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<FieldGroup> getFieldGroupPageList(FieldGroupPageParam fieldGroupPageParam) throws Exception {
        Page<FieldGroup> page = new PageInfo<>(fieldGroupPageParam, OrderItem.desc(getLambdaColumn(FieldGroup::getCreateTime)));
        LambdaQueryWrapper<FieldGroup> wrapper = new LambdaQueryWrapper<>();
        IPage<FieldGroup> iPage = fieldGroupMapper.selectPage(page, wrapper);
        return new Paging<FieldGroup>(iPage);
    }

    @Override
    public List<FieldGroup> getFieldGroupByNotesId(Long fieldNotesId) {
        List<FieldGroup> fieldGroupList = getBaseMapper().selectList(new LambdaQueryWrapper<FieldGroup>().eq(FieldGroup::getFieldId,fieldNotesId));
        return fieldGroupList;
    }

}
