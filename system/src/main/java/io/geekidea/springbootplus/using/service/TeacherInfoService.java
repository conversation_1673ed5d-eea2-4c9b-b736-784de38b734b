package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TeacherInfo;
import io.geekidea.springbootplus.using.param.TeacherInfoPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface TeacherInfoService extends BaseService<TeacherInfo> {

    /**
     * 保存
     *
     * @param teacherInfo
     * @return
     * @throws Exception
     */
    boolean saveTeacherInfo(TeacherInfo teacherInfo) throws Exception;

    /**
     * 修改
     *
     * @param teacherInfo
     * @return
     * @throws Exception
     */
    boolean updateTeacherInfo(TeacherInfo teacherInfo) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTeacherInfo(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param teacherInfoQueryParam
     * @return
     * @throws Exception
     */
    Paging<TeacherInfo> getTeacherInfoPageList(TeacherInfoPageParam teacherInfoPageParam) throws Exception;

    /**
     * @desc: 根据用户id获取老师
     * @author: DH
     * @date: 2022/5/5 10:40
     */
    TeacherInfo getByUserId(Long userId);

    /**
     * @desc: 根据学校id获取老师
     * @author: DH
     * @date: 2022/5/5 10:40
     */
    List<TeacherInfo> getBySchoolId(Long scholldId);

    /**
     * @desc: 根据id修改bigUrl
     * @author: DH
     * @date: 2025/7/18
     */
    boolean updateBigUrl(Long id, String bigUrl) throws Exception;
}
