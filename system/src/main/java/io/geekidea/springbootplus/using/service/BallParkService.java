package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.BallPark;
import io.geekidea.springbootplus.using.param.BallParkPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
public interface BallParkService extends BaseService<BallPark> {

    /**
     * 保存
     *
     * @param ballPark
     * @return
     * @throws Exception
     */
    boolean saveBallPark(BallPark ballPark) throws Exception;

    /**
     * 修改
     *
     * @param ballPark
     * @return
     * @throws Exception
     */
    boolean updateBallPark(BallPark ballPark) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteBallPark(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param ballParkQueryParam
     * @return
     * @throws Exception
     */
    Paging<BallPark> getBallParkPageList(BallParkPageParam ballParkPageParam) throws Exception;

    List<BallPark> historicBallPark( Long courseId, Long studentId,String search);

}
