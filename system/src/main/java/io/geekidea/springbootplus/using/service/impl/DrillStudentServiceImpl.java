package io.geekidea.springbootplus.using.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.geekidea.springbootplus.using.entity.Drill;
import io.geekidea.springbootplus.using.entity.DrillStudent;
import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.mapper.DrillStudentMapper;
import io.geekidea.springbootplus.using.service.DrillService;
import io.geekidea.springbootplus.using.service.DrillStudentService;
import io.geekidea.springbootplus.using.param.DrillStudentPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.service.HardwareService;
import io.geekidea.springbootplus.using.service.TransparentMessageService;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class DrillStudentServiceImpl extends BaseServiceImpl<DrillStudentMapper, DrillStudent> implements DrillStudentService {

    @Autowired
    private DrillStudentMapper drillStudentMapper;
    @Autowired
    private HardwareService hardwareService;
    @Autowired
    @Lazy
    private TransparentMessageService transparentMessageService;
    @Autowired
    @Lazy
    DrillService drillService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveDrillStudent(DrillStudent drillStudent) throws Exception {
        return super.save(drillStudent);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateDrillStudent(DrillStudent drillStudent) throws Exception {
        return super.updateById(drillStudent);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteDrillStudent(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<DrillStudent> getDrillStudentPageList(DrillStudentPageParam drillStudentPageParam) throws Exception {
        Page<DrillStudent> page = new PageInfo<>(drillStudentPageParam, OrderItem.desc(getLambdaColumn(DrillStudent::getCreateTime)));
        LambdaQueryWrapper<DrillStudent> wrapper = new LambdaQueryWrapper<>();
        IPage<DrillStudent> iPage = drillStudentMapper.selectPage(page, wrapper);
        return new Paging<DrillStudent>(iPage);
    }

    @Override
    public boolean setStudentDrillStatus(List<DrillStudent> drillStudents) {
        if (drillStudents.isEmpty()) {
            return false;
        }
        Drill drill=drillService.getById(drillStudents.get(0).getDrillId());
        drillStudents.forEach(e -> {
            DrillStudent drillStudent = getOne(new QueryWrapper<DrillStudent>().eq("student_id", e.getStudentId()).eq("team_id", e.getTeamId()).eq("drill_id", e.getDrillId()));
            if (drillStudent != null) {
                e.setId(drillStudent.getId());
            }
            if(drill!=null&&null==e.getStartTime()&&!drill.getHaveHard()){
                e.setStartTime(new Date());
                long stopTime = e.getStartTime().getTime() + (drill.getDuration() * (60 * 1000));
                e.setStopTime(new Date(stopTime));
            }
            saveOrUpdate(e);
        });
        return true;
    }

    @Override
    public DrillStudent getOneByDrillIdAndStudentId(Long drillId, Long studentId) {
        return getOne(new LambdaQueryWrapper<DrillStudent>().eq(DrillStudent::getDrillId, drillId).eq(DrillStudent::getStudentId, studentId));
    }

    @Override
    public List<DrillStudent> getListByDrillId(Long drillId,Long teamId) {
        return list(new LambdaQueryWrapper<DrillStudent>().eq(DrillStudent::getDrillId, drillId).eq(DrillStudent::getTeamId,teamId));
    }

    @Override
    public int attendance(Long drillId,Long teamId) {
        return count(new QueryWrapper<DrillStudent>().eq("team_id",teamId).eq("drill_id", drillId).isNotNull("status"));
    }

    @Override
    public boolean setDrillCancel(List<String> macs) {
        if(macs.size()==0){
            return false;
        }
        List<DrillStudent> drillStudents = drillStudentMapper.getNoUploaded(macs);
        List<DrillStudent> drillStudents1 = new ArrayList<>();
        if (drillStudents.size() == 0) {
            return false;
        }
        drillStudents.forEach(e -> {
            DrillStudent drillStudent = getById(e.getId());
            drillStudent.setCancel(true);
            drillStudents1.add(drillStudent);
        });
        try {
            transparentMessageService.sendTransparentByDrillCancel(drillStudents.get(0).getDrillId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return saveOrUpdateBatch(drillStudents1);
    }

    @Override
    public int unstartCount(Long drillId,Long teamId) {
        int noHard = 0;
        List<DrillStudent> drillStudents = list(new LambdaQueryWrapper<DrillStudent>().eq(DrillStudent::getDrillId, drillId).eq(DrillStudent::getTeamId,teamId).eq(DrillStudent::getStatus, true).eq(DrillStudent::getCancel, false).isNull(DrillStudent::getStartTime));
        for (DrillStudent e:drillStudents) {
            Hardware hardware = hardwareService.getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getStudentId, e.getStudentId()).eq(Hardware::getUnbind, false));
            if(hardware == null){
                noHard ++;
            }
        }
        return drillStudents.size() - noHard;
    }
}
