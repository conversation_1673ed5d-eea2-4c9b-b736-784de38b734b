package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PersonData对象")
@TableName(autoResultMap = true)
public class PersonData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("球赛类型  DRILL:训练 GAME:比赛")
    private String matchType;

    @NotNull(message = "球赛id不能为空")
    @ApiModelProperty("球赛id")
    private Long matchId;

    @ApiModelProperty("团队id")
    private Long teamId;

    @NotNull(message = "学员id不能为空")
    @ApiModelProperty("学员id")
    private Long studentId;

    @ApiModelProperty("步数")
    private Integer stepCount;

    @ApiModelProperty("跑动次数")
    private Integer runCount;

    @ApiModelProperty("跑动距离(m)")
    private Integer runDistance=0;

    @ApiModelProperty("卡路里(kcal）")
    private Integer calorie;

    @ApiModelProperty("最大冲刺速度(km/h)")
    private Double maxSprintSpeed;

    @ApiModelProperty("运动时间")
    private Long exerciseTime;

    @NotBlank(message = "速度数据不能为空")
    @ApiModelProperty("速度数据")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON speedDataList;

    @NotBlank(message = "个人曲线不能为空")
    @ApiModelProperty("个人曲线")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON curveList;

    @ApiModelProperty("运动集锦")
    private String touchBalls;

    @ApiModelProperty("动作")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON pose;

    @ApiModelProperty("活跃度")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray liveness;

    @ApiModelProperty("活跃度（一分钟）")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray livenessMinute;

    @ApiModelProperty("起跳次数")
    private Integer jumpCount;

    @ApiModelProperty("起跳平均高度 m米")
    private Integer jumpAvgHeight;

    @ApiModelProperty("pace:{\"avgSpeedAllocation\":平均配速/km 时间戳,\"avgSpeed\":平均速度/小时,\"avgStrideFrequency\":平均步频/分钟:,\"avgStride\":平均步幅/厘米} 速度步伐")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject pace;

    @ApiModelProperty("平均触地时间 单位 s")
    private Integer avgTouchDownTime;

    @ApiModelProperty("跑步姿势 {\"avgTouchLandTime\":平均触地时间 单位 毫秒,\"avgTouchdownImpact\":平均着地冲击 单位 g,\"avgEctropionRange:\"平均外翻幅度 单位 °:,\"PendulumAngle\":平均摆动 单位 °}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject runningForm;

    @ApiModelProperty("touchdownWay:着地方式 {\"front\":前脚 10 单位%,\"whole\":全脚 10 单位%,\"queen\":脚跟 10 单位%}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject touchdownWay;

    @ApiModelProperty("配速")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray speedAllocation;

    @ApiModelProperty("最长滞空时间")
    private Integer maxDuration;

    @ApiModelProperty("平均滞空时间")
    private Integer avgDuration;

    @ApiModelProperty("最远起跳距离")
    @TableField("max_take_off_distance")
    private Integer maxTakeOffDistance;

    @ApiModelProperty("最大起跳高度 ")
    private Integer maxTakeOffHeight;

    @ApiModelProperty("{\\\"leftTurning\\\":左变向次数,\\\"rightTurning\\\":右变向次数,\\\"breakOut\\\":爆发次数,\\\"starting\\\":启动次数,\\\"brake\\\":制动次数} \\n\"")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject depthData;

    @ApiModelProperty("是否app的数据")
    @TableField(exist = false)
    private Boolean app;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @ApiModelProperty("得分")
    @TableField(exist = false)
    private Integer fraction;

    @ApiModelProperty("助攻")
    @TableField(exist = false)
    private Integer assist;

    @ApiModelProperty("学员名字")
    @TableField(exist = false)
    private String stuName;

    @ApiModelProperty("学员头像")
    @TableField(exist = false)
    private String image;

    @ApiModelProperty("实际跑动运动时间")
    private long runExerciseTime;

    @ApiModelProperty("实际步数运动时间")
    private long stepExerciseTime;

    @ApiModelProperty("查询者是否查看了这场数据")
    private Boolean appLook;

    @ApiModelProperty("1.标准版 2.专业版")
    private Integer hardType;

    private Long courseId;
}
