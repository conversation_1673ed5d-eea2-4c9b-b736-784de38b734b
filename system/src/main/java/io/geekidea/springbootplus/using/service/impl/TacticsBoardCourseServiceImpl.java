package io.geekidea.springbootplus.using.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.mapper.*;
import io.geekidea.springbootplus.using.service.TacticsBoardCourseService;
import io.geekidea.springbootplus.using.param.TacticsBoardPageParam;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.param.UserFavoriteTacticsPageParam;
import io.geekidea.springbootplus.using.service.UserFavoriteTacticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 战术板综合信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-22
 */
@Slf4j
@Service
public class TacticsBoardCourseServiceImpl extends BaseServiceImpl<TacticsBoardCourseMapper, TacticsBoardCourse> implements TacticsBoardCourseService {

    @Autowired
    private TacticsBoardCourseMapper tacticsBoardCourseMapper;

    @Autowired
    private UserFavoriteTacticsService userFavoriteTacticsService;

    @Autowired
    private TacticsBoardPlayerMapper tacticsBoardPlayerMapper;

    @Autowired
    private TacticsBoardLineMapper tacticsBoardLineMapper;

    @Autowired
    private TacticsBoardToolMapper tacticsBoardToolMapper;

    @Autowired
    private TacticsBoardTextMapper tacticsBoardTextMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveTacticsBoard(TacticsBoardCourse tacticsBoardCourse) throws Exception {
         super.saveOrUpdate(tacticsBoardCourse);
        return tacticsBoardCourse.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTacticsBoard(TacticsBoardCourse tacticsBoardCourse) throws Exception {
        return super.updateById(tacticsBoardCourse);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTacticsBoard(Long id) throws Exception {
        return super.removeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTacticsBoardList(List<Long> ids) throws Exception {
        return super.removeByIds(ids);
    }

    @Override
    public TacticsBoardCourse getTacticsBoardById(Long id) throws Exception {
        // 获取战术板课件基本信息和关联的战术板信息
        TacticsBoardCourse tacticsBoardCourse = tacticsBoardCourseMapper.getTacticsBoardById(id);
        
        if (tacticsBoardCourse != null && tacticsBoardCourse.getTacticsBoard() != null) {
            TacticsBoard tacticsBoard = tacticsBoardCourse.getTacticsBoard();
            Long boardId = tacticsBoard.getId();
            
            // 查询关联的球员信息
            List<TacticsBoardPlayer> playerList = tacticsBoardPlayerMapper.selectList(
                new QueryWrapper<TacticsBoardPlayer>().eq("board_id", boardId)
            );
            tacticsBoard.setPlayerList(playerList);
            
            // 查询关联的线条信息
            List<TacticsBoardLine> lineList = tacticsBoardLineMapper.selectList(
                new QueryWrapper<TacticsBoardLine>().eq("board_id", boardId)
            );
            tacticsBoard.setLineList(lineList);
            
            // 查询关联的工具信息
            List<TacticsBoardTool> toolList = tacticsBoardToolMapper.selectList(
                new QueryWrapper<TacticsBoardTool>().eq("board_id", boardId)
            );
            tacticsBoard.setToolList(toolList);
            
            // 查询关联的文本信息
            List<TacticsBoardText> textList = tacticsBoardTextMapper.selectList(
                new QueryWrapper<TacticsBoardText>().eq("board_id", boardId)
            );
            tacticsBoard.setTextList(textList);
        }
        
        return tacticsBoardCourse;
    }

    @Override
    public Paging<TacticsBoardCourse> getTacticsBoardPageList(TacticsBoardPageParam tacticsBoardPageParam) throws Exception {
        Page<TacticsBoardCourse> page = new PageInfo<>(tacticsBoardPageParam, OrderItem.desc("create_time"));

        IPage<TacticsBoardCourse> iPage = null;
        if (tacticsBoardPageParam.getIsFavorite()!=null&&tacticsBoardPageParam.getIsFavorite()) {
            iPage = tacticsBoardCourseMapper.getTacticsBoardPageListFavorite(page, tacticsBoardPageParam);
        } else {
            iPage = tacticsBoardCourseMapper.getTacticsBoardPageList(page, tacticsBoardPageParam);
        }
        // 对于分页列表，可以选择不加载详细的关联数据，以提高性能
        // 如果需要加载，可以遍历结果并加载关联数据
        // 为每个TacticsBoard加载关联数据
       iPage.getRecords().forEach(e -> {
            loadRelatedData(e.getTacticsBoard());
        });
        return new Paging<>(iPage);
    }

    /**
     * 加载TacticsBoard的关联数据
     */
    private void loadRelatedData(TacticsBoard tacticsBoard) {
        if (tacticsBoard == null || tacticsBoard.getId() == null) {
            return;
        }

        Long boardId = tacticsBoard.getId();

        // 查询关联的球员信息
        List<TacticsBoardPlayer> playerList = tacticsBoardPlayerMapper.selectList(
                new QueryWrapper<TacticsBoardPlayer>().eq("board_id", boardId)
        );
        tacticsBoard.setPlayerList(playerList);

        // 查询关联的线条信息
        List<TacticsBoardLine> lineList = tacticsBoardLineMapper.selectList(
                new QueryWrapper<TacticsBoardLine>().eq("board_id", boardId)
        );
        tacticsBoard.setLineList(lineList);

        // 查询关联的工具信息
        List<TacticsBoardTool> toolList = tacticsBoardToolMapper.selectList(
                new QueryWrapper<TacticsBoardTool>().eq("board_id", boardId)
        );
        tacticsBoard.setToolList(toolList);

        // 查询关联的文本信息
        List<TacticsBoardText> textList = tacticsBoardTextMapper.selectList(
                new QueryWrapper<TacticsBoardText>().eq("board_id", boardId)
        );
        tacticsBoard.setTextList(textList);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean toggleTacticsBoardFavorite(UserFavoriteTacticsPageParam favoriteParam) throws Exception {
        // 构建查询条件，查找用户对指定战术板的收藏记录
        LambdaQueryWrapper<UserFavoriteTactics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFavoriteTactics::getUserId, favoriteParam.getUserId())
                .eq(UserFavoriteTactics::getTacticsBoardId, favoriteParam.getTacticsBoardId());

        // 查询是否存在收藏记录
        UserFavoriteTactics existingFavorite = userFavoriteTacticsService.getOne(queryWrapper);

        if (favoriteParam.getIsFavorite()) {
            // 用户要收藏战术板
            if (existingFavorite == null) {
                // 若之前未收藏，创建新的收藏记录
                UserFavoriteTactics newFavorite = new UserFavoriteTactics();
                newFavorite.setUserId(favoriteParam.getUserId());
                newFavorite.setTacticsBoardId(favoriteParam.getTacticsBoardId());
                return userFavoriteTacticsService.save(newFavorite);
            }
            // 若已收藏，直接返回 true
            return true;
        } else {
            // 用户要取消收藏战术板
            if (existingFavorite != null) {
                // 若之前已收藏，删除收藏记录
                return userFavoriteTacticsService.remove(queryWrapper);
            }
            // 若未收藏，直接返回 true
            return true;
        }
    }
}