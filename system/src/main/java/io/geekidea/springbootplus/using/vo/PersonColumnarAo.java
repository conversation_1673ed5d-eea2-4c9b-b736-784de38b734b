package io.geekidea.springbootplus.using.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(autoResultMap = true)
public class PersonColumnarAo {
    @ApiModelProperty("球队id")
    private Long teamId;

    @ApiModelProperty("学生id")
    private Long studentId;

    @ApiModelProperty("时间日期 根据查询的type动态返回 日：2022-02-26 周：45|今年第多少周 月：2022-02 年：2022")
    private String date;

    @ApiModelProperty("跑动占比")
    private long run;

    @ApiModelProperty("步行占比")
    private long step;

    @ApiModelProperty("休息占比")
    private long quiet;

    @ApiModelProperty("禁坐占比")
    private long meditation;

    @ApiModelProperty("起跳占比")
    private long jump;

    @ApiModelProperty("起跳次数")
    private long jumpCount;

    @ApiModelProperty("跨步次数")
    private long stride;

    @ApiModelProperty("运动时间")
    @JsonIgnore
    private long exerciseTime = 0l;

    @ApiModelProperty("跑动运动时间")
    @JsonIgnore
    private long runExerciseTime = 0l;

    @ApiModelProperty("步数运动时间")
    @JsonIgnore
    private long stepExerciseTime = 0l;

}
