package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.KindergartenTeamMonitor;
import io.geekidea.springbootplus.using.entity.PersonData;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.mapper.PersonDataMapper;
import io.geekidea.springbootplus.using.mapper.StudentInfoMapper;
import io.geekidea.springbootplus.using.service.DrillService;
import io.geekidea.springbootplus.using.service.KindergartenTeamMonitorService;
import io.geekidea.springbootplus.using.service.PersonDataService;
import io.geekidea.springbootplus.using.param.PersonDataPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.service.StudentInfoService;
import io.geekidea.springbootplus.using.vo.*;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class PersonDataServiceImpl extends BaseServiceImpl<PersonDataMapper, PersonData> implements PersonDataService {

    @Autowired
    private PersonDataMapper personDataMapper;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private StudentInfoMapper studentInfoMapper;
    @Autowired
    private DrillService drillService;
    @Lazy
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private
    KindergartenTeamMonitorService kindergartenTeamMonitorService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean savePersonData(PersonData personData) throws Exception {
        return super.save(personData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updatePersonData(PersonData personData) throws Exception {
        return super.updateById(personData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deletePersonData(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<PersonData> getPersonDataPageList(PersonDataPageParam personDataPageParam) throws Exception {
        Page<PersonData> page = new PageInfo<>(personDataPageParam, OrderItem.desc(getLambdaColumn(PersonData::getCreateTime)));
        LambdaQueryWrapper<PersonData> wrapper = new LambdaQueryWrapper<>();
        IPage<PersonData> iPage = personDataMapper.selectPage(page, wrapper);
        return new Paging<PersonData>(iPage);
    }

    @Override
    public PersonData findOfMatchIdAndStudentId(String matchType, Long matchId, Long studentId) {
        return getOne(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, matchType).eq(PersonData::getMatchId, matchId).eq(PersonData::getStudentId, studentId));
    }

    @Override
    public List<PersonData> findOfMatchId(Long matchId, Long teamId, String matchType) {
        return list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, matchType).eq(PersonData::getMatchId, matchId).eq(PersonData::getTeamId, teamId));
    }

    @Override
    public List<PersonData> findOfMatchIdByHist(Long teamId, Long matchId, String matchType) {
        List<PersonData> personDatas = list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, matchType).eq(PersonData::getTeamId, teamId).ne(PersonData::getMatchId, matchId));
        return personDatas.size() == 0 ? findOfMatchId(matchId, teamId, matchType) : personDatas;
    }

    @Override
    public List<PersonData> findOfMatchIdByHistStu(Long stuId, Long matchId, String matchType) {
        List<PersonData> personDatas = list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, matchType).eq(PersonData::getStudentId, stuId).ne(PersonData::getMatchId, matchId));
        return personDatas.size() == 0 ? Collections.emptyList() : personDatas;
    }

    @Override
    public List<String> exerciseTime(List<Long> teamIds, Long studentId, String start, String stop, String matchType) {
        return getBaseMapper().getExerciseTime(teamIds, studentId, start, stop, matchType);
    }

    @Override
    public List<ExerciseConditionAo> findByMonth(Long teamId, String year, String month) {
        return getBaseMapper().findByMonth(teamId, year, month);
    }

    @Override
    public List<ExerciseConditionAo> findByMonthStu(Long studentId, String year, String month) {
        return getBaseMapper().findByMonthStu(studentId, year, month);
    }

    @Override
    public List<ExerciseConditionAo> findByDay(Long teamId, String year, String month, String day) {
        return getBaseMapper().findByDay(teamId, year, month, day);
    }

    @Override
    public List<ExerciseConditionAo> findByDayStu(Long studentId, String year, String month, String day) {
        return getBaseMapper().findByDayStu(studentId, year, month, day);
    }

    @Override
    public List<ExerciseConditionAo> findByMonitor(Long matchId) {
        return getBaseMapper().findByMonitor(matchId);
    }

    @Override
    public List<ExerciseConditionAo> findByMonitor(Long teamId, String startTime, String stopTime) {
        return getBaseMapper().findByMonitorTeam(teamId, startTime, stopTime);
    }

    @Override
    public Object getMotionSteps(Long teamId, String createTime, String matchType) {
        //总运动统计
        StuPersonDataAo personData = getBaseMapper().getMotionSteps(teamId, createTime, matchType);
        //班级人数
        Integer count = studentInfoService.getBaseMapper().selectCount(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, teamId));
        if (count == null) {
            count = 0;
        }
        if (personData == null) {
            personData = new StuPersonDataAo();
            personData.setSumSteps(0);
            personData.setSumRun(0);
            personData.setSumCalorie(0);
            personData.setSumExercise(0);
        }
        personData.setStuCount(count);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("personData", personData);
        //学员数据
//        List<PersonData> list = getBaseMapper().selectData(teamId,createTime);
//        jsonObject.put("stuList",list);
        List<PersonData> list = studentInfoMapper.getListByTeamId(teamId);
        for (PersonData pd : list) {
            PersonData p = getBaseMapper().getMotion(pd.getStudentId(), createTime, "DRILL");
            if (p != null) {
                pd.setStepCount(p.getStepCount());
                pd.setRunDistance(p.getRunDistance());
                pd.setCalorie(p.getCalorie());
                pd.setExerciseTime(p.getExerciseTime());
                pd.setCreateTime(p.getCreateTime());
            } else {
                pd.setStepCount(0);
                pd.setRunDistance(0);
                pd.setCalorie(0);
                pd.setExerciseTime(0L);
                pd.setCreateTime(null);
            }
        }
        jsonObject.put("stuList", list);
        return jsonObject;
    }

    @Override
    public Object teamSteps(Long teamId, String startTime, String stopTime) {
        List<Integer> list = new ArrayList<>();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date stime = sf.parse(startTime);
            Date ptime = sf.parse(stopTime);
            Long tian = (ptime.getTime() - stime.getTime()) / (1000 * 24 * 60 * 60);
            for (int i = 0; i < tian + 1; i++) {
                String dat = sf.format(stime.getTime() + i * 1000 * 24 * 60 * 60);
                StuPersonDataAo sp = getBaseMapper().getSumSteps(teamId, dat);
                if (sp == null) {
                    list.add(0);
                } else {
                    list.add(sp.getSumSteps());
                }

            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public Object getDailyData(Long teamId, String date) {
        //List<PersonData> personDataByDate = getDailyDataPersonDataCollect(teamId, date);
        String dateStart = date + " 00:00:00";
        String dateStop = date + " 23:59:59";
        List<PersonData> personDataByDate = list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, "DRILL").eq(PersonData::getTeamId, teamId).gt(PersonData::getCreateTime, dateStart).lt(PersonData::getCreateTime, dateStop));
        List<StudentInfo> studentInfos = studentInfoService.getStusByTeamId(teamId);
        personDataByDate = personDataByDate.stream().filter(e -> {//这里为了去除已删除掉的学生
            for (StudentInfo studentInfo : studentInfos) {
                if (studentInfo.getId().equals(e.getStudentId())) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
        Map<Long, List<PersonData>> personDataByDateMap = personDataByDate.stream().collect(Collectors.groupingBy(PersonData::getStudentId));
        int stuCount = studentInfoService.countByTeam(teamId);
        int exercisePass = 0;//运动是否达标
        int maxStepCount = personDataByDate.stream().mapToInt(PersonData::getStepCount).max().orElse(0);//最高步数
        int avgStepCount = (int) personDataByDate.stream().mapToInt(PersonData::getStepCount).average().orElse(0.0);
        int maxCalorie = personDataByDate.stream().mapToInt(PersonData::getCalorie).max().orElse(0);//最高卡路里
        int avgCalorie = (int) personDataByDate.stream().mapToInt(PersonData::getCalorie).average().orElse(0.0);
        List<StudentInfo> attendanceStu = drillService.attendance(teamId, date);
        for (Map.Entry<Long, List<PersonData>> map : personDataByDateMap.entrySet()) {
            long exericsTime = map.getValue().stream().mapToLong(PersonData::getExerciseTime).sum();
            if (exericsTime >= 60) {
                exercisePass++;
            }
        }
        List<DailyDataAo> dailyDataAos = new ArrayList<>();
        DailyDataAo exercise = new DailyDataAo();
        exercise.setOne(exercisePass);
        exercise.setTwo(stuCount - exercisePass);

        DailyDataAo step = new DailyDataAo();
        step.setOne(maxStepCount);
        step.setTwo(avgStepCount);

        DailyDataAo calorie = new DailyDataAo();
        calorie.setOne(maxCalorie);
        calorie.setTwo(avgCalorie);

        DailyDataAo attendance = new DailyDataAo();
        if (attendanceStu.size() > 0) {
            attendance.setOne(attendanceStu.stream().mapToInt(StudentInfo::getNormal).sum());
            attendance.setTwo(attendanceStu.stream().mapToInt(StudentInfo::getDefect).sum());
        } else {
            attendance.setOne(0);
            attendance.setTwo(0);
        }
        dailyDataAos.add(exercise);
        dailyDataAos.add(step);
        dailyDataAos.add(calorie);
        dailyDataAos.add(attendance);
        return dailyDataAos;
    }

    @Override
    public List<PersonData> getDailyDataPersonDataCollect(Long teamId, String date) {
        String dateStart = date + " 00:00:00";
        String dateStop = date + " 23:59:59";
        List<PersonData> personDatas = list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, "DRILL").eq(PersonData::getTeamId, teamId).gt(PersonData::getCreateTime, dateStart).lt(PersonData::getCreateTime, dateStop));
        Map<Long, List<PersonData>> personDataByDateMap = personDatas.stream().collect(Collectors.groupingBy(PersonData::getStudentId));
        List<PersonData> personDataByDate = new ArrayList<>();
        for (Map.Entry<Long, List<PersonData>> map : personDataByDateMap.entrySet()) {
            List<PersonData> personDataStus = map.getValue();
            PersonData personData = new PersonData();
            personData.setStudentId(map.getKey());
            for (PersonData p : personDataStus) {
                if (personData.getStepCount() != null) {
                    personData.setStepCount(personData.getStepCount() + p.getStepCount());
                    personData.setRunCount(personData.getRunCount() + p.getRunCount());
                    personData.setRunDistance(personData.getRunDistance() + p.getRunDistance());
                    personData.setCalorie(personData.getCalorie() + p.getCalorie());
                    personData.setMaxSprintSpeed(personData.getMaxSprintSpeed() + p.getMaxSprintSpeed());
                    personData.setExerciseTime(personData.getExerciseTime() + p.getExerciseTime());
                    personData.setAvgDuration(personData.getAvgDuration() + p.getAvgDuration());
                    personData.setMaxDuration(personData.getMaxDuration() + p.getMaxDuration());
                    personData.setMaxTakeOffDistance(personData.getMaxTakeOffDistance() + p.getMaxTakeOffDistance());
                    personData.setMaxTakeOffHeight(personData.getMaxTakeOffHeight() + p.getMaxTakeOffHeight());
                    personData.setJumpAvgHeight(personData.getJumpAvgHeight() + p.getJumpAvgHeight());
                    personData.setJumpCount(personData.getJumpCount() + p.getJumpCount());
                    personData.setAvgTouchDownTime(personData.getAvgTouchDownTime() + p.getAvgTouchDownTime());
                } else {
                    personData.setStepCount(p.getStepCount());
                    personData.setRunCount(p.getRunCount());
                    personData.setRunDistance(p.getRunDistance());
                    personData.setCalorie(p.getCalorie());
                    personData.setMaxSprintSpeed(p.getMaxSprintSpeed());
                    personData.setExerciseTime(p.getExerciseTime());
                    personData.setAvgDuration(p.getAvgDuration());
                    personData.setMaxDuration(p.getMaxDuration());
                    personData.setMaxTakeOffDistance(p.getMaxTakeOffDistance());
                    personData.setMaxTakeOffHeight(p.getMaxTakeOffHeight());
                    personData.setJumpAvgHeight(p.getJumpAvgHeight());
                    personData.setJumpCount(p.getJumpCount());
                    personData.setAvgTouchDownTime(p.getAvgTouchDownTime());
                    personData.setPace(p.getPace());
                    personData.setRunningForm(p.getRunningForm());
                    personData.setTouchdownWay(p.getTouchdownWay());
                }
            }

            //速度
            List<Object> speedDatas = personDataStus.stream().map(PersonData::getSpeedDataList).
                    flatMap(list -> ((JSONArray) list).stream()).
                    collect(Collectors.toList());
            JSONArray speedDataList = new JSONArray();
            JSONObject hige = new JSONObject();
            hige.put("speed", "HIGH");
            hige.put("distance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("HIGH")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());
            hige.put("count", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("HIGH")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());
            hige.put("ballCount", 0);
            hige.put("ballDistance", 0);
            speedDataList.add(hige);
            JSONObject mid = new JSONObject();
            mid.put("speed", "MID");
            mid.put("distance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());
            mid.put("count", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("MID")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());
            mid.put("ballCount", 0);
            mid.put("ballDistance", 0);
            speedDataList.add(mid);
            JSONObject low = new JSONObject();
            low.put("speed", "LOW");
            low.put("distance", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("distance")).sum());
            low.put("count", speedDatas.stream().filter(e -> ((JSONObject) e).getString("speed").equals("LOW")).mapToInt(e -> ((JSONObject) e).getInteger("count")).sum());
            low.put("ballCount", 0);
            low.put("ballDistance", 0);
            speedDataList.add(low);
            personData.setSpeedDataList(speedDataList);
            personDataByDate.add(personData);
        }
        return personDataByDate;
    }

    @Override
    public List<Map<String, Object>> findSynthesizeCurve(Long teamId, String start, String stop, String matchType) {
        return getBaseMapper().findSynthesizeCurve(teamId, start, stop, matchType);
    }

    @Override
    public Date getEcentData(Long teamId, String matchType) {
        return getBaseMapper().getEcentData(teamId, matchType);
    }

    @Override
    public List<StuPersonDataAo> runList(Long teamId, String data, String matchType) {
        List<StuPersonDataAo> list = getBaseMapper().runList(teamId, data, matchType);
        return list;
    }

    @Override
    public List<StuPersonDataAo> heightList(Long teamId, String data, String matchType) {
        List<StuPersonDataAo> list = getBaseMapper().heightList(teamId, data, matchType);
        return list;
    }

    @Override
    public List<StuPersonDataAo> runteamList(Long teamId, Long matchId, String matchType) {
        List<StuPersonDataAo> list = getBaseMapper().runteamList(teamId, matchId, matchType);
        return list;
    }

    @Override
    public List<StuPersonDataAo> heightteamList(Long teamId, Long matchId, String matchType) {
        List<StuPersonDataAo> list = getBaseMapper().heightteamList(teamId, matchId, matchType);
        return list;
    }

    @Override
    public StuPersonDataAo sumCalculation(Long matchId, String matchType) {
        return getBaseMapper().sumCalculation(matchId, matchType);
    }

    @Override
    public StuPersonDataAo allRunList(Long stuId, String data) {
        return getBaseMapper().allRunList(stuId, data);
    }

    @Override
    public List<Long> getUploadIdsByStu(String matchType, Long studentId) {
        return getBaseMapper().getUploadIdsByStu(matchType, studentId);
    }

    @Override
    public StuPersonDataAo getMotionByMatchId(Long teamId, Long matchId, String matchType) {
        return getBaseMapper().getMotionByMatchId(teamId, matchId, matchType);
    }

    @Override
    public StuPersonDataAo allRunByMatchId(Long stuId, Long matchId, String matchType) {
        return getBaseMapper().allRunByMatchId(stuId, matchId, matchType);
    }

    @Override
    public StuPersonDataAo allHeightByMatchId(Long stuId, Long matchId, String matchType) {
        return getBaseMapper().allHeightByMatchId(stuId, matchId, matchType);
    }

    @Override
    public List<Long> getUploadIdsByMatchIdAndTeamId(String matchType, Long matchId, Long teamId) {
        return getBaseMapper().getUploadIdsByMatchIdAndTeamId(matchType, matchId, teamId);
    }

    @Override
    public boolean likeIncr(String matchType, Long matchId, Long studentId) {
        String key = "";
        if (matchType.equals("GAME")) {
            key = CacheConsts.MATCHDATAGAMELIKECOUNT;
        } else {
            key = CacheConsts.MATCHDATADRILLLIKECOUNT;
        }
        int count = getMatchLike(matchType, matchId, studentId);
        redisUtils.hSet(key, matchId + "-" + studentId, ++count);
        return true;
    }

    @Override
    public int getMatchLike(String matchType, Long matchId, Long studentId) {
        String key = "";
        if (matchType.equals("GAME")) {
            key = CacheConsts.MATCHDATAGAMELIKECOUNT;
        } else {
            key = CacheConsts.MATCHDATADRILLLIKECOUNT;
        }
        Object count = redisUtils.hGet(key, matchId + "-" + studentId);
        return count != null ? (int) count : 0;
    }

    @Override
    public List<DataStatisticsAo> dataStatistics(Long teamId, Long studentId, String start, String stop, Integer type) {
        return getBaseMapper().dataStatistics(teamId, studentId, start, stop, type);
    }

    @Override
    public List<TeamExerciseAo> sportsRanking(Long schoolId, String start, String stop, Long teamId, Integer type) {
        if (teamId == -1) {//查团队
            List<Long> teamIds = kindergartenTeamMonitorService.list(new LambdaQueryWrapper<KindergartenTeamMonitor>()
                    .eq(KindergartenTeamMonitor::getSchoolId, schoolId)).stream().map(KindergartenTeamMonitor::getTeamId).collect(Collectors.toList());
            if (teamIds.size() == 0) {
                return new ArrayList<>();
            }
            return getBaseMapper().sportsRankingTeam(start, stop, teamIds, type).stream().peek(e -> e.setPracticalExerciseTime(e.getStepExerciseTime() + e.getRunExerciseTime())).collect(Collectors.toList());
        } else {
            return getBaseMapper().sportsRankingPerson(start, stop, teamId, type).stream().peek(e -> e.setPracticalExerciseTime(e.getStepExerciseTime() + e.getRunExerciseTime())).collect(Collectors.toList());

        }
    }

    @Override
    public List<DataStatisticsNewAo> dataStatisticsNew(Long teamId, Long studentId, String start, String stop, Integer type) {
        return getBaseMapper().dataStatisticsNew(teamId, studentId, start, stop, type);
    }

    @Override
    public List<PersonData> getCurveList(String start, String stop, Long studentId) {
        return getBaseMapper().getCurveList(start, stop, studentId);
    }

    @Override
    public List<TeamExerciseAo> personExerciseTime(String start, String stop, Long studentId, Long teamId, Integer type) {
        List<TeamExerciseAo> teamExerciseAos = getBaseMapper().personExerciseTime(start, stop, studentId, teamId, type);
        return teamExerciseAos.stream().peek(e -> e.setPracticalExerciseTime(e.getStepExerciseTime() + e.getRunExerciseTime())).collect(Collectors.toList());
    }

    @Override
    public List<TeamExerciseAo> teamExerciseTime(String start, String stop, Long teamId, Integer type) {
        List<TeamExerciseAo> teamExerciseAos = getBaseMapper().teamExerciseTime(start, stop, teamId, type);
        return teamExerciseAos.stream().peek(e -> e.setPracticalExerciseTime(e.getStepExerciseTime() + e.getRunExerciseTime())).collect(Collectors.toList());
    }

    @Override
    public List<PersonColumnarAo> personColumnar(String start, String stop, Long studentId, Long teamId, Integer type) {
        return getBaseMapper().personColumnar(start, stop, studentId, teamId, type);
    }

    @Override
    public TeamExerciseAo teamRunCalorieSum(Long teamId, Integer type, String start, String stop) {
        return getBaseMapper().teamRunCalorieSum(teamId, type, start, stop);
    }

    @Override
    public List<PersonData> getListNoData(String matchType, Long teamId, Long matchId) {
        return getBaseMapper().getListNoData(matchType, teamId, matchId);
    }

    @Override
    public PersonData getArrow(Long studentId, Long matchId, String matchType) {
        PersonData personData = getBaseMapper().getArrow(studentId,matchId,matchType);
        if(personData==null){
            personData = new PersonData();
            personData.setId(-1L);
            personData.setRunExerciseTime(0l);
            personData.setMaxSprintSpeed(0.0);
            personData.setCalorie(0);

            return personData;
        }
        return personData;
    }

}
