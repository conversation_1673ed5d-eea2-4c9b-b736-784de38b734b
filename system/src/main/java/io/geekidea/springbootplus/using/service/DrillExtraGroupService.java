package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.DrillExtraGroup;
import io.geekidea.springbootplus.using.param.DrillExtraGroupPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-07-21
 */
public interface DrillExtraGroupService extends BaseService<DrillExtraGroup> {

    /**
     * 保存
     *
     * @param drillExtraGroup
     * @return
     * @throws Exception
     */
    boolean saveDrillExtraGroup(DrillExtraGroup drillExtraGroup) throws Exception;

    /**
     * 修改
     *
     * @param drillExtraGroup
     * @return
     * @throws Exception
     */
    boolean updateDrillExtraGroup(DrillExtraGroup drillExtraGroup) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteDrillExtraGroup(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param drillExtraGroupQueryParam
     * @return
     * @throws Exception
     */
    Paging<DrillExtraGroup> getDrillExtraGroupPageList(DrillExtraGroupPageParam drillExtraGroupPageParam) throws Exception;

    List<DrillExtraGroup> getDrillExtraGroup(Long drillExtaId);
}
