package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.Course;
import io.geekidea.springbootplus.using.param.CoursePageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface CourseService extends BaseService<Course> {

    /**
     * 保存
     *
     * @param course
     * @return
     * @throws Exception
     */
    boolean saveCourse(Course course) throws Exception;

    /**
     * 修改
     *
     * @param course
     * @return
     * @throws Exception
     */
    boolean updateCourse(Course course) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteCourse(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param courseQueryParam
     * @return
     * @throws Exception
     */
    Paging<Course> getCoursePageList(CoursePageParam coursePageParam) throws Exception;

}
