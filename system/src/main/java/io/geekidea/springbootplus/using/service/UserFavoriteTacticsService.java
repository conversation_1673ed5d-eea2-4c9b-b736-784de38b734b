package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.UserFavoriteTactics;
import io.geekidea.springbootplus.using.param.UserFavoriteTacticsPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 * 用户收藏战术板记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface UserFavoriteTacticsService extends BaseService<UserFavoriteTactics> {

    /**
     * 保存
     *
     * @param userFavoriteTactics
     * @return
     * @throws Exception
     */
    boolean saveUserFavoriteTactics(UserFavoriteTactics userFavoriteTactics) throws Exception;

    /**
     * 修改
     *
     * @param userFavoriteTactics
     * @return
     * @throws Exception
     */
    boolean updateUserFavoriteTactics(UserFavoriteTactics userFavoriteTactics) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteUserFavoriteTactics(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param userFavoriteTacticsQueryParam
     * @return
     * @throws Exception
     */
    Paging<UserFavoriteTactics> getUserFavoriteTacticsPageList(UserFavoriteTacticsPageParam userFavoriteTacticsPageParam) throws Exception;

}
