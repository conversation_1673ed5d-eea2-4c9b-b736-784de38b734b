package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.using.entity.Course;
import io.geekidea.springbootplus.using.service.CourseService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.CoursePageParam;
import io.geekidea.springbootplus.framework.common.controller.BaseController;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.common.param.IdParam;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@RestController
@RequestMapping("/using/course")
@Module("teambox")
@Api(value = "课程（体育项目）", tags = {"课程（体育项目）"})
public class CourseController extends BaseController {

    @Autowired
    private CourseService courseService;

    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class)
    public ApiResult<Boolean> addCourse(@Validated(Add.class) @RequestBody Course course) throws Exception {
        boolean flag = courseService.saveCourse(course);
        return ApiResult.result(flag);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    public ApiResult<Boolean> updateCourse(@Validated(Update.class) @RequestBody Course course) throws Exception {
        boolean flag = courseService.updateCourse(course);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteCourse(@PathVariable("id") Long id) throws Exception {
        boolean flag = courseService.deleteCourse(id);
        return ApiResult.result(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = Course.class)
    public ApiResult<Course> getCourse(@PathVariable("id") Long id) throws Exception {
        Course course = courseService.getById(id);
        return ApiResult.ok(course);
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = Course.class,notes = "返回是数组")
    public ApiResult<Object> getCoursePageList(@Validated @RequestBody CoursePageParam coursePageParam) throws Exception {
        Paging<Course> paging = courseService.getCoursePageList(coursePageParam);
        return ApiResult.ok(paging.getRecords());
    }

}

