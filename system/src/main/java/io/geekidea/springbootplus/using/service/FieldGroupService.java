package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.FieldGroup;
import io.geekidea.springbootplus.using.param.FieldGroupPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
public interface FieldGroupService extends BaseService<FieldGroup> {

    /**
     * 保存
     *
     * @param fieldGroup
     * @return
     * @throws Exception
     */
    boolean saveFieldGroup(FieldGroup fieldGroup) throws Exception;

    /**
     * 修改
     *
     * @param fieldGroup
     * @return
     * @throws Exception
     */
    boolean updateFieldGroup(FieldGroup fieldGroup) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteFieldGroup(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param fieldGroupQueryParam
     * @return
     * @throws Exception
     */
    Paging<FieldGroup> getFieldGroupPageList(FieldGroupPageParam fieldGroupPageParam) throws Exception;

    /**
     * 根据场记id查询小组数据
     * @param fieldNotesId
     * @return
     */
    List<FieldGroup> getFieldGroupByNotesId(Long fieldNotesId);


}
