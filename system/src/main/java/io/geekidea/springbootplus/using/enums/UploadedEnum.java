package io.geekidea.springbootplus.using.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

@Getter
public enum UploadedEnum {
    //NOT未同步 - ALL全部同步 - PORTION部分同步
    NOT("NOT"), ALL("ALL") ,PORTION("PORTION");

    @EnumValue
    private String uploaded;

    UploadedEnum(String uploaded) {
        this.uploaded = uploaded;
    }

    public static UploadedEnum getValue(String uploaded) {
        for (UploadedEnum uploadedEnum : values()) {
            if (uploadedEnum.getUploaded().equals(uploaded)) {
                return uploadedEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "UploadedEnum{" +
                "uploaded=" + uploaded +
                '}';
    }
}
