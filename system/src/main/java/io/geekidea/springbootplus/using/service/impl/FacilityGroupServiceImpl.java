package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.util.DateUtil;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.entity.FacilityGroup;
import io.geekidea.springbootplus.using.entity.FacilityHardware;
import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.entity.Team;
import io.geekidea.springbootplus.using.mapper.HardwareGroupMapper;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.param.FacilityGroupPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Slf4j
@Service
public class FacilityGroupServiceImpl extends BaseServiceImpl<HardwareGroupMapper, FacilityGroup> implements FacilityGroupService {

    @Autowired
    private HardwareGroupMapper hardwareGroupMapper;
    @Autowired
    private HardwareService hardwareService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private FacilityHardwareService facilityHardwareService;
    @Autowired
    private DrillStudentService drillStudentService;
    @Autowired
    private DemoMacQrcodeService demoMacQrcodeService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveFacilityGroup(FacilityGroup facilityGroup) throws Exception {
        return super.saveOrUpdate(facilityGroup);
    }

    @Override
    public FacilityGroup getFacilityGroup(Long id) {
        FacilityGroup facilityGroup = getById(id);
        if (facilityGroup == null) {
            return null;
        }
        JSONArray array = facilityGroup.getTeams();
        if (array != null && array.size() > 0) {
            facilityGroup.setBindTeamCount(array.size());
            array.stream().forEach(e -> {
                JSONObject o = (JSONObject) e;
                o.put("name", teamService.getById(o.getLongValue("id")).getFullName());
                o.put("bind", hardwareService.countByTeamAndBoxNum(o.getLongValue("id"), facilityGroup.getBox()));
                o.put("count", studentInfoService.countByTeam(o.getLongValue("id")));
            });
            array.sort(Comparator.comparingInt(e -> ((JSONObject) e).getIntValue("bind")));
        }

        facilityGroup.setCount(demoMacQrcodeService.getCountByBox(facilityGroup.getBox()));
        facilityGroup.setBindCount(facilityHardwareService.getFacilityHardwareCount(id));
        if (!facilityGroup.getMore()) {
            facilityGroup.setResidue(facilityGroup.getBindCount() - hardwareService.getResidue(facilityGroup.getBox()));
        } else {
            facilityGroup.setResidue(facilityGroup.getBindCount());
        }
        return facilityGroup;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateFacilityGroup(FacilityGroup facilityGroup) throws Exception {
        if(facilityGroup.getTeams()!=null){
            facilityGroup.setTeams(null);
        }
        return super.saveOrUpdate(facilityGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFacilityGroup(Long id) throws Exception {
        return removeById(id);
    }

    @Override
    public Paging<FacilityGroup> getFacilityGroupPageList(FacilityGroupPageParam facilityGroupPageParam) throws Exception {
        Page<FacilityGroup> page = new PageInfo<>(facilityGroupPageParam, OrderItem.desc(getLambdaColumn(FacilityGroup::getCreateTime)));
        LambdaQueryWrapper<FacilityGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacilityGroup::getSchoolId, facilityGroupPageParam.getSchoolId());
        wrapper.eq(FacilityGroup::getMore, facilityGroupPageParam.getMore());
        IPage<FacilityGroup> iPage = hardwareGroupMapper.selectPage(page, wrapper);
        iPage.getRecords().forEach(r -> {
            JSONArray array = r.getTeams();
            if (array != null && array.size() > 0) {
                r.setBindTeamCount(array.size());
                array.stream().forEach(e -> {
                    JSONObject o = (JSONObject) e;
                    Team team = teamService.getById(o.getLongValue("id"));
                    if (team != null) {
                        o.put("name", team.getFullName());
                        o.put("bind", hardwareService.countByTeamAndBoxNum(o.getLongValue("id"), r.getBox()));
                        o.put("count", studentInfoService.countByTeam(o.getLongValue("id")));
                    }
                });
                array.sort(Comparator.comparingInt(e -> ((JSONObject) e).getIntValue("bind")));
            }
            r.setCount(demoMacQrcodeService.getCountByBox(r.getBox()));
            r.setBindCount(facilityHardwareService.getFacilityHardwareCount(r.getId()));
            if (!r.getMore()) {
                r.setResidue(r.getBindCount() - hardwareService.getResidue(r.getBox()));
            } else {
                r.setResidue(r.getBindCount());
            }

            JSONArray array1 = new JSONArray();
            for (Object o : array) {
                if (array1.size() == 3) {
                    break;
                }
                array1.add(o);
            }
            r.setTeams(array1); //列表只取前三条
        });
        return new Paging<FacilityGroup>(iPage);
    }

    @Override
    @Transactional
    public Boolean dredge(Long groupId, Long teamId, Long originalGroupId) {
        FacilityGroup facilityGroup = getById(groupId);
        JSONArray teams = facilityGroup.getTeams();
        for (Object e : teams) {
            JSONObject o = (JSONObject) e;
            if (o.getLongValue("id") == teamId.longValue()) {
                return true;
            }
        }

        JSONObject o = new JSONObject();
        o.put("id", teamId);
        teams.add(o);
        facilityGroup.setTeams(teams);

        if (originalGroupId != -1) {
            unBind(teamId, originalGroupId);
        }
        return saveOrUpdate(facilityGroup);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean unBind(Long teamId, Long groupId) {
        FacilityGroup facilityGroup = getById(groupId);
        if (facilityGroup == null) {
            return false;
        }
        JSONArray teams = facilityGroup.getTeams();
        for (int i = 0; i < teams.size(); i++) {
            JSONObject o = teams.getJSONObject(i);
            if (o.getLongValue("id") == teamId.longValue()) {
                teams.remove(i);
                break;
            }
        }
        facilityGroup.setTeams(teams);
        saveOrUpdate(facilityGroup);

        List<FacilityHardware> facilityHardwares = facilityHardwareService.getFacilityHardwareList(groupId);
        List<String> hards = facilityHardwares.stream().map(FacilityHardware::getMac).collect(Collectors.toList());
        if (hards.size() == 0) {
            return true;
        }
        List<Hardware> hardwares = hardwareService.list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getTeamId, teamId).in(Hardware::getMac, hards));
        hardwares.forEach(e -> {
            e.setUnbind(true);
        });
        if (facilityGroup.getMore()) {
            drillStudentService.setDrillCancel(hards);
        }
        return hardwareService.saveOrUpdateBatch(hardwares);
    }

    @Override
    public Long getFacilityGroupId(Long teamId) {
        Team team = teamService.getById(teamId);
        Map<String, Object> map = teamFacilityGroup(team.getSchoolId(), teamId, null);
        return (Long) map.get("groupId");
    }

  /*  @Override
    public Boolean isBindFacilityGroup(Long schoolId, Long teamId) {
        List<FacilityGroup> facilityGroups = list(new LambdaQueryWrapper<FacilityGroup>().eq(FacilityGroup::getSchoolId, schoolId));
        List<Object> array = facilityGroups.parallelStream().map(FacilityGroup::getTeams).flatMap(e -> e.stream()).collect(Collectors.toList());
        for (Object e:array) {
            JSONObject o = (JSONObject)e;
            if(teamId.longValue() == o.getLongValue("id")){
                return true;
            }
        }
        return false;
    }*/

    public Map<String, Object> teamFacilityGroup(Long schoolId, Long teamId, String key) {
        List<FacilityGroup> facilityGroups = list(new LambdaQueryWrapper<FacilityGroup>().eq(FacilityGroup::getSchoolId, schoolId));
        Map<String, Object> map = new HashMap<>();
        if (key == null || key.equals("isBind")) {
            List<Object> array = facilityGroups.parallelStream().map(FacilityGroup::getTeams).flatMap(e -> e.stream()).collect(Collectors.toList());
            for (Object e : array) {
                JSONObject o = (JSONObject) e;
                if (teamId.longValue() == o.getLongValue("id")) {
                    map.put("isBind", true);
                    break;
                }
            }
        }
        if (key == null || key.equals("groupId")) {
            for (FacilityGroup facilityGroup : facilityGroups) {
                if (map.get("groupId") != null) {
                    break;
                }
                for (Object e : facilityGroup.getTeams()) {
                    JSONObject o = (JSONObject) e;
                    if (teamId.longValue() == o.getLongValue("id")) {
                        map.put("groupId", facilityGroup.getId());
                        map.put("groupName", facilityGroup.getName());
                        map.put("restrictDate", o.getLongValue("restrictDate"));
                        break;
                    }
                }
            }
        }
        if (map.get("groupId") == null) {
            map.put("groupId", -1l);
        }
        if (map.get("isBind") == null) {
            map.put("isBind", false);
        }
        return map;
    }

    @Override
    public List<FacilityHardware> residue(Long groupId) {
        FacilityGroup facilityGroup = getById(groupId);
        if (facilityGroup == null) {
            return null;
        }
        List<FacilityHardware> facilityHardwares = facilityHardwareService.getFacilityHardwareList(groupId);
        List<Hardware> hardwares = hardwareService.list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getSchoollId, facilityGroup.getSchoolId()).eq(Hardware::getBox, facilityGroup.getBox()).eq(Hardware::getUnbind, 0));
        facilityHardwares = facilityHardwares.stream().filter(e -> {
            for (Hardware h : hardwares) {
                if (h.getMac().equals(e.getMac())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        return facilityHardwares;
    }
}
