package io.geekidea.springbootplus.using.enums;

public enum CurveEnum {
    //个人跑动
    PERSON_RUN,
    //个人跑动平均
    PERSON_RUN_AVG,
    //个人步数
    STEP,
    //步幅
    PERSON_STRIDE_FREQUENCY,
    //步数
    PERSON_STEP,
    //个人步数平均
    STEP_AVG,
    //个人带球跑动
    PERSON_BALL_RUN,
    //个人触球
    PERSON_TOUCH_BALL,
    //个人消耗卡路里
    PERSON_CONSUME,
    //个人时间内的团队平均消耗卡路里
    PERSON_TEAM_AVG_CONSUME,
    //个人球权
    PERSON_POSSESSION,
    //个人历史消耗卡路里
    PERSON_HISTORY_CONSUME,
    //球队跑动
    TEAM_RUN,
    //球队带球跑动
    TEAM_BALL_RUN,
    //球队消耗卡路里
    TEAM_CONSUME,
    //球队历史消耗卡路里
    TEAM_HISTORY_CONSUME,
    //球队球权
    TEAM_POSSESSION,
    //团队触球
    TEAM_TOUCH_BALL,
    //综合跑动
    SYNTHESIZE_RUN,
    //综合消耗
    SYNTHESIZE_CONSUME,
    //起跳
    GROWTALLER,
    //跨步
    STRIDE,
    //起跳平均
    GROWTALLER_AVG,
    //跨步平均
    STRIDE_AVG;
}
