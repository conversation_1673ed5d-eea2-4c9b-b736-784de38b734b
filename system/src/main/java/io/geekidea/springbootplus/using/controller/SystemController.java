package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/using/system")
@Module("高级机密")
@Api(value = "高级机密", tags = {"高级机密"})
public class SystemController {
    @Autowired
    SystemService systemService;
    @Autowired
    TeamService teamService;

    /**
     * @desc: 设置版本
     * @author: DH
     * @date: 2023/6/8 16:03
     */
    @PostMapping("/setHdVersion/{id}")
    @OperationLog(name = "设置版本")
    @ApiOperation(value = "设置版本", response = ApiResult.class, notes = "id:user表的id body参数 [\"1.1\"]")
    public ApiResult<Object> setHdVersion(@PathVariable("id") Long id, @RequestBody JSONArray array) {
        return ApiResult.ok(systemService.setHdVersion(id, array));
    }

    /**
     * @desc: 获取版本 前端分享使用
     * @author: DH
     * @date: 2023/6/8 16:03
     */
    @PostMapping("/getHdVersion/{matchType}/{matchId}")
    @GetMapping(name = "获取版本")
    @ApiOperation(value = "获取版本", response = ApiResult.class, notes = "返回账号的版本集合 没有对应版本返回空数组 ['1.1','1.2']\\n\" +\n" +
            "            \"    TeamTag校园数宁体育课\\n\" +\n" +
            "            \"    1.1 标准版\\n\" +\n" +
            "            \"    1.2 高级版\\n\" +\n" +
            "            \"    1.3 旗舰版\\n\" +\n" +
            "            \"    TeamTag智能篮球\\n\" +\n" +
            "            \"    2.1 标准版\\n\" +\n" +
            "            \"    2.2 专业版\\n\" +\n" +
            "            \"    2.3 俱乐部版\\n\" +\n" +
            "            \"    2.4 赛事版\\n\" +\n" +
            "            \"    TeamTag智能足球\\n\" +\n" +
            "            \"    3.1 少儿版\\n\" +\n" +
            "            \"    3.2 青少年版\\n\" +\n" +
            "            \"    3.3 成人版\\n\" +\n" +
            "            \"    3.4 专业版\\n\" +\n" +
            "            \"    3.5 俱乐部版\\n\" +\n" +
            "            \"    3.6 赛事版\"")
    public ApiResult<Object> getHdVersion(@PathVariable("matchType") String matchType, @PathVariable("matchId") Long matchId) {
        return ApiResult.ok(systemService.getHdVersion(matchType, matchId));
    }

    /**
     * @desc: pad版本变更班级限制
     * @author: DH
     * @date: 2023/6/8 16:03
     */
    @PostMapping("/updateVersion/{username}/{courseId}/{type}")
    @OperationLog(name = "pad版本变更班级限制")
    @ApiOperation(value = "pad版本变更班级限制", response = ApiResult.class, notes = "参数 username：登录的用户名 courseId：球队类型 type：1减少班级 2增加班级 ")
    public ApiResult<Object> updateVersion(@PathVariable("username") String username, @PathVariable("courseId") Integer courseId, @PathVariable("type") Integer type) {
        return ApiResult.ok(systemService.updateVersion(username, courseId, type));
    }

    /**
     * @desc: 个人数据点赞次数获取
     * @author: DH
     * @date: 2023/10/21 10:46
     */
    @GetMapping("/getMatchLike/{matchType}/{matchId}/{studentId}")
    @OperationLog(name = "个人数据点赞次数获取")
    @ApiOperation(value = "个人数据点赞次数获取", response = ApiResult.class)
    public ApiResult<Object> getMatchLike(@PathVariable("matchType") String matchType, @PathVariable("matchId") Long matchId, @PathVariable("studentId") Long studentId) {
        return ApiResult.ok(systemService.likeIncr(matchType, matchId, studentId));
    }

    /**
     * @desc: 个人数据点赞次数增加
     * @author: DH
     * @date: 2023/10/21 10:46
     */
    @PostMapping("/likeIncr/{matchType}/{matchId}/{studentId}")
    @OperationLog(name = "个人数据点赞次数增加")
    @ApiOperation(value = "个人数据点赞次数增加", response = ApiResult.class)
    public ApiResult<Object> likeIncr(@PathVariable("matchType") String matchType, @PathVariable("matchId") Long matchId, @PathVariable("studentId") Long studentId) {
        return ApiResult.ok(systemService.likeIncr(matchType, matchId, studentId));
    }

    @GetMapping("/xxx")
    public ApiResult<Object> xxx() {
        List<Team> teamList = teamService.list(new LambdaQueryWrapper<Team>().eq(Team::getSportsMonitor, 1).orderByDesc(Team::getSportsMonitorTime));
        for (int i = 0; i < teamList.size(); i++) {
            teamList.get(i).setMonitorSort(i+1);
        }
        return ApiResult.ok(teamService.saveOrUpdateBatch(teamList));
    }

}
