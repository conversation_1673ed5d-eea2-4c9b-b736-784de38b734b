package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 *  分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-04-28
 */
@Data
@ApiModel(value = "StudentInfoPageParam分页参数")
public class StudentInfoPageParam extends BasePageOrderParam {
    @ApiModelProperty("球队id")
    private Long teamId;

    @ApiModelProperty("训练id")
    private Long drillId;

    @ApiModelProperty("比赛id")
    private Long gameId;

    @ApiModelProperty("是否查询删除或者注销")
    private Boolean deleted = false;
}
