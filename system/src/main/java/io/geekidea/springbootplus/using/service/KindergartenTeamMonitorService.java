package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.KindergartenTeamMonitor;
import io.geekidea.springbootplus.using.param.KindergartenTeamMonitorPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
public interface KindergartenTeamMonitorService extends BaseService<KindergartenTeamMonitor> {

    /**
     * 保存
     *
     * @param kindergartenTeamMonitor
     * @return
     * @throws Exception
     */
    boolean saveKindergartenTeamMonitor(KindergartenTeamMonitor kindergartenTeamMonitor) throws Exception;

    /**
     * 修改
     *
     * @param kindergartenTeamMonitor
     * @return
     * @throws Exception
     */
    boolean updateKindergartenTeamMonitor(KindergartenTeamMonitor kindergartenTeamMonitor) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteKindergartenTeamMonitor(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param kindergartenTeamMonitorQueryParam
     * @return
     * @throws Exception
     */
    Paging<KindergartenTeamMonitor> getKindergartenTeamMonitorPageList(KindergartenTeamMonitorPageParam kindergartenTeamMonitorPageParam) throws Exception;

}
