package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.constant.CacheConsts;
import io.geekidea.springbootplus.framework.util.MD5Utils;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.common.RequestCommonMethod;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.entity.User;
import io.geekidea.springbootplus.using.service.StudentInfoService;
import io.geekidea.springbootplus.using.service.UserService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2022-05-05
 */
@Slf4j
@RestController("usingLoginController")
@RequestMapping("/using")
@Module("teambox")
@Api(value = "登录", tags = {"登录"})
public class LoginController extends BaseController {

    @Autowired
    private UserService userService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    RequestCommonMethod requestCommonMethod;
    @Autowired
    RedisUtils redisUtils;

    @PostMapping("/login")
    @OperationLog(name = "登录", type = OperationLogType.ADD)
    @ApiOperation(value = "登录", response = User.class, notes = "body 只需要传 name跟password 还有deviceTokens 友盟设备id  CODE 20014;用户不存在\n" +
            "999;用户名不存在  \n" +
            "1000;密码错误 \n" +
            "1001;密码为空")
    public ApiResult login(@RequestBody User user) throws Exception {
        User user1;
        if (StringUtils.isNotBlank(user.getName()) && StringUtils.isNotBlank(user.getPassword())) {
            if (userService.getByName(user.getName()) == null) {
                return ApiResult.fail(ApiCode.USERNAME_ERROR);
            }
            user1 = userService.login(user);
            if (user1 == null) {
                return ApiResult.fail(ApiCode.PASSWORD_ERROR);
            }
        } else {
            return ApiResult.fail(ApiCode.USERNAME_PASSWORD_ISNULL);
        }
        if (user1 != null) {
            user1.setToken(genToken());
            user1.setDeviceTokens(user.getDeviceTokens());
            putCurrent(user1.getToken(), user1);
            user1.setCount(userService.count(user1.getSchoolInfo().getId()));
            Boolean more = (Boolean) redisUtils.get(CacheConsts.SCHOOL_MORE + user1.getId());
            if (more == null) {
                user1.setMore(false);
            } else {
                user1.setMore(more);
            }
        }
        userService.saveOrUpdate(user1);
        return ApiResult.ok(user1);
    }

    @PostMapping("/logout")
    @OperationLog(name = "登出", type = OperationLogType.ADD)
    @ApiOperation(value = "登出", response = ApiResult.class)
    public ApiResult logout() {
        delCurrent();
        return ApiResult.ok();
    }

    @PostMapping("/updatePassword")
    public ApiResult updatePassword() {
        List<StudentInfo> studentInfos = studentInfoService.list();
        studentInfos.stream().forEach(e -> {
            e.setPasswd(MD5Utils.getSaltMD5(e.getPasswd()));
            if(e.getPasswd()!=null){
                studentInfoService.saveOrUpdate(e);
            }
        });
        return ApiResult.ok();
    }
}

