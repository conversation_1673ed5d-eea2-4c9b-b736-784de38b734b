package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.using.service.ProDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/using/proData")
@Module("teambox")
@Api(value = "赛事专业版数据", tags = {"赛事专业版数据"})
public class ProDataController {
    @Autowired
    ProDataService proDataService;

    @GetMapping("/getProData/{matchId}/{matchType}/{teamId}/{studentId}")
    @OperationLog(name = "获取专业版数据")
    @ApiOperation(value = "获取专业版数据", response = ApiResult.class, notes = "matchId:赛事id matchType:GAME|DRILL 查团队studentId传-1 data返回：" +
            "\n{\n" +
            "        \"studentId\": 48, //学生id\n" +
            "        \"name\": \"李正法15\",//学生名字\n" +
            "        \"headImg\": xxx, //学生头像\n" +
            "        \"score\": 21.0,//总分\n" +
            "        \"ranking\": 2,//总排名\n" +
            "        \"pentacle\": 9,//五角星份\n" +
            "        \"grade\": 9,//赛事等级\n" +
            "        \"footballShirt\": null,//足球球衣\n" +
            "        \"shirt\": null,//篮球球衣\n" +
            "        \"offense\": {//进攻  参考场记字段意思\n" +
            "        rate:{\"onePointHitRate\",80//一分命中率,\"twoPointHitRate\",80//二分命中率,\"threePointHitRate\",80//三分命中率,\"totalHitRate\",80//总得命中率}//篮球数据" +
            "        \"pentacle\": 9,//五角星份\n" +
            "            \"ranking\": 15,//进攻综合排名\n" +
            "            \"sumScore\": 0.0,//进攻分数\n" +
            "            \"wonderfulstop\": {//精彩停球 \n" +
            "                \"score\": 0.0,//单项分数\n" +
            "                \"ranking\": 15,//排名 ranking小于1的话代表无排名数据\n" +
            "                \"value\": 0//场记字段实际值 为null代表没录入过\n" +
            "                \"arrow\": -1:下降|0：不变|1：上升\n" +
            "            }" +
            "\t},\n" +
            "        \"guard\": {//防守 参考场记字段意思\n" +
            "        \"pentacle\": 9,//五角星份\n" +
            "            \"ranking\": 15,//防守综合排名\n" +
            "            \"sumScore\": 0.0,//进攻分数\n" +
            "            \"excitingSyeals\": {//参考场记字段\n" +
            "                \"score\": 0.0,\n" +
            "                \"ranking\": 15,\n" +
            "                \"value\": 0\n" +
            "                \"arrow\": -1:下降|0：不变|1：上升\n" +
            "            }\n" +
            "        },\n" +
            "        \"fault\": {//失误犯规 参考场记字段意思\n" +
            "        \"pentacle\": 9,//五角星份\n" +
            "            \"ranking\": 15,//失误犯规排名\n" +
            "            \"sumScore\": 0.0,//失误犯规分数\n" +
            "            \"kickair\": {\n" +
            "                \"score\": -0.0,\n" +
            "                \"ranking\": 15,\n" +
            "                \"value\": 0\n" +
            "                \"arrow\": 0.0\n" +
            "            }\n" +
            "        },\n" +
            "        \"runningFitness\": {//跑动 \n" +
            "            \"pentacle\": 9,//五角星份\n" +
            "            \"ranking\": 15,//跑动排名\n" +
            "            \"sumScore\": 0.0,//跑动分数\n" +
            "            \"jerk\": {//急停 -- 还会返回其他跑动其他数据 runDistance（跑动距离）highDistance(高速跑动)  maxSprintSpeed（最高冲刺） calorie（消耗） turning(变向) 参考个人数据\n" +
            "                \"score\": 0.0,\n" +
            "                \"ranking\": 3,\n" +
            "                \"value\": 0.0,\n" +
            "                \"arrow\": -1:下降|0：不变|1：上升\n" +
            "            }\n" +
            "        },\n" +
            "        control:{\"teamControlTime\",80//主队控球时间 毫秒 为0代表没记录,\"opponentControlTime\",80//客队控球时间 毫秒 为0代表没记录},//主客队控球\n" +
            "    \"spider\": {蜘蛛图//+\n" +
            "           \"synthesis\": 9,//综合\n" +
            "            \"runningFitness\": 15,//跑动\n" +
            "            \"offense\": 0.0,//进攻\n" +
            "            \"guard\": 0.0,//防守\n" +
            "            \"fault\": 0.0,//失误\n" +
            "            \"surpass\": 25,//超越多少占比 如果是负数则是低于占比\n" +
            "            \" }")
    public ApiResult getProData(@PathVariable("matchId") Long matchId, @PathVariable("matchType") String matchType, @PathVariable("teamId") Long teamId, @PathVariable("studentId") Long studentId) {
        return ApiResult.ok(proDataService.getComprehensiveScoreRanking(teamId, matchId, matchType, studentId, false, new JSONObject()));
    }

    @GetMapping("/getProTeamData/{matchId}/{matchType}/{teamId}")
    @OperationLog(name = "获取团队专业版场记- 综合分数排名数据")
    @ApiOperation(value = "获取团队专业版场记- 综合分数排名数据", response = ApiResult.class, notes = "matchId:赛事id matchType:GAME|DRILL \n" +
            "{\n" +
            "        \"offense\": [进攻\n" +
            "            {\n" +
            "                \"studentId\": 1300,\n" +
            "                \"score\": 0 分数,\n" +
            "                \"name\": \"陈晓龙\",\n" +
            "                \"card\": \"ggggggggep\"\n" +
            "            }\n" +
            "        ],\n" +
            "        \"guard\": [防守\n" +
            "            {\n" +
            "                \"studentId\": 1300,\n" +
            "                \"score\": 0,\n" +
            "                \"name\": \"陈晓龙\",\n" +
            "                \"card\": \"ggggggggep\"\n" +
            "            }\n" +
            "        ],\n" +
            "        \"fault\": [失误\n" +
            "            {\n" +
            "                \"studentId\": 1300,\n" +
            "                \"score\": 0,\n" +
            "                \"name\": \"陈晓龙\",\n" +
            "                \"card\": \"ggggggggep\"\n" +
            "            }\n" +
            "        ],\n" +
            "        \"runningFitness\": [跑动\n" +
            "            {\n" +
            "                \"studentId\": 1300,\n" +
            "                \"score\": 18,\n" +
            "                \"name\": \"陈晓龙\",\n" +
            "                \"card\": \"ggggggggep\"\n" +
            "            }\n" +
            "        ],\n" +
            "rate:{\"onePointHitRate\",80//一分命中率,\"twoPointHitRate\",80//二分命中率,\"threePointHitRate\",80//三分命中率,\"totalHitRate\",80//总得命中率}//篮球数据 \n" +
            "teamControlTime:1234 主队控球时间 毫秒 为0代表没记录\n" +
            "opponentControlTime:1234 客队控球时间 毫秒 为0代表没记录\n" +
            "    \"spider\": {蜘蛛图//+\n" +
            "           \"synthesis\": 9,//综合\n" +
            "            \"runningFitness\": 15,//跑动\n" +
            "            \"offense\": 0.0,//进攻\n" +
            "            \"guard\": 0.0,//防守\n" +
            "            \"fault\": 0.0,//失误\n" +
            "            \"surpass\": 25//超越多少占比 如果是负数则是低于占比\n" +
            "            \"}")
    public ApiResult getProTeamData(@PathVariable("matchId") Long matchId, @PathVariable("matchType") String matchType, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(proDataService.getProTeamData(teamId, matchId, matchType));
    }

    /**
     * @desc: 天梯图
     * @author: DH
     * @date: 2023/8/28 14:31
     */
    @PostMapping("/highLadder/{teamId}/{matchId}/{matchType}/{studentId}")
    @OperationLog(name = "天梯图")
    @ApiOperation(value = "天梯图", response = ApiResult.class, notes = "返回：{\"name\":学生名,\"headImg\":头像,\"level\":等级,\"index\":下标}")
    public ApiResult highLadder(@PathVariable("teamId") Long teamId, @PathVariable("matchId") Long matchId, @PathVariable("matchType") String matchType, @PathVariable("studentId") Long studentId) {
        return ApiResult.ok(proDataService.highLadder(teamId, matchId, matchType, studentId));
    }
}
