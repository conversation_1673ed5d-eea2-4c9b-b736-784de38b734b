package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.geekidea.springbootplus.using.entity.BallPark;
import io.geekidea.springbootplus.using.entity.Game;
import io.geekidea.springbootplus.using.entity.PersonData;
import io.geekidea.springbootplus.using.param.FieldDataPageParam;
import io.geekidea.springbootplus.using.param.GameExtraPageParam;
import io.geekidea.springbootplus.using.param.GamePageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface GameService extends BaseService<Game> {

    Game getById(Long id);

    Game getGame(Long id,Long userId);

    /**
     * 保存
     *
     * @param game
     * @return
     * @throws Exception
     */
    boolean saveGame(Game game) throws Exception;

    /**
     * 修改
     *
     * @param game
     * @return
     * @throws Exception
     */
    boolean updateGame(Game game) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteGame(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param gameQueryParam
     * @return
     * @throws Exception
     */
    Paging<Game> getGamePageList(GamePageParam gamePageParam) throws Exception;

    //优化
    Paging<Game> getGamePageList1(GamePageParam gamePageParam) throws Exception;

    Boolean getStuHardGameStatus(Long gameId,Long userId,Long teamId);

    Boolean stopGame(Long gameId);

    Game unUploadOfGame(Long teamId, Long gameId);

    Game unUploadOfGameUser(Long stuId, Long gameId);

    Boolean startedCount(Long teamId, Boolean app);

    IPage<Game> getGrowUpData(GamePageParam gamePageParam);

    Integer unstartCount(Long gameId, Long teamId);

    Object teamRankGame(Long teamId, Long gameId, Integer key);

    Object fieldRank(Long gameId,Long groupId,String key);

    List<BallPark> historicBallPark(Long courseId,Long userId,String search);

    BallPark addOrBallPark(BallPark ballPark);

    Boolean delBallPark(Long id) throws Exception;
}
