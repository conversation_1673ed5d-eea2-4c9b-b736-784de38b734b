package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 * 战术板工具表，存储各种工具信息 分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "战术板工具表，存储各种工具信息分页参数")
public class TacticsBoardToolPageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;
}
