package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.entity.FieldNotes;
import io.geekidea.springbootplus.using.service.FieldDataService;
import io.geekidea.springbootplus.using.service.FieldNotesService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.FieldNotesPageParam;
import io.geekidea.springbootplus.framework.common.controller.BaseController;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.common.param.IdParam;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Slf4j
@RestController
@RequestMapping("/fieldNotes")
@Module("${cfg.module}")
@Api(value = "场记模块", tags = {"场记模块"})
public class FieldNotesController extends BaseController {

    @Autowired
    private FieldNotesService fieldNotesService;
    @Autowired
    private FieldDataService fieldDataService;
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class, notes = "")
    public ApiResult<Boolean> addFieldNotes(@Validated(Add.class) @RequestBody FieldNotes fieldNotes) throws Exception {
        boolean flag = fieldNotesService.saveFieldNotes(fieldNotes);
        return ApiResult.ok(flag);
    }

    /**
     * 复制
     *
     * @param
     * @return
     * @throws Exception
     */
    @PostMapping("/copy/{id}")
    @OperationLog(name = "复制", type = OperationLogType.ADD)
    @ApiOperation(value = "复制", response = ApiResult.class, notes = "")
    public ApiResult<Boolean> copyFieldNotes(@PathVariable("id") Long id) {
        boolean flag = fieldNotesService.copyFieldNotes(id);
        return ApiResult.ok(false);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    public ApiResult<Boolean> updateFieldNotes(@Validated(Update.class) @RequestBody FieldNotes fieldNotes) throws Exception {
        boolean flag = fieldNotesService.updateFieldNotes(fieldNotes);
        return ApiResult.ok(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteFieldNotes(@PathVariable("id") Long id) throws Exception {
        boolean flag = fieldNotesService.deleteFieldNotes(id);
        return ApiResult.ok(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = FieldNotes.class)
    public ApiResult<FieldNotes> getFieldNotes(@PathVariable("id") Long id) throws Exception {
        FieldNotes fieldNotes = fieldNotesService.getGroupById(id);
        return ApiResult.ok(fieldNotes);
    }

    /**
     * 删除学生显示
     */
    @PostMapping("/deleteByApp/{fieldId}/{groupId}/{stuId}")
    @OperationLog(name = "APP删除学生显示", type = OperationLogType.DELETE)
    @ApiOperation(value = "APP删除学生显示", response = ApiResult.class)
    public ApiResult<Boolean> deleteByApp(@PathVariable("fieldId") Long fieldId,@PathVariable("groupId") Long groupId,@PathVariable("stuId") Long stuId) throws Exception {
        boolean flag = fieldDataService.deleteByApp(fieldId,groupId,stuId);
        return ApiResult.result(flag);
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = FieldNotes.class)
    public ApiResult<Paging<FieldNotes>> getFieldNotesPageList(@Validated @RequestBody FieldNotesPageParam fieldNotesPageParam) throws Exception {
        Paging<FieldNotes> paging = fieldNotesService.getFieldNotesPageList(fieldNotesPageParam);
        return ApiResult.ok(paging);
    }

    /**
     * 快速场记
     *
     * @param fieldNotes
     * @return
     */
    @PostMapping("/fieldFast")
    @OperationLog(name = "快速场记")
    @ApiOperation(value = "快速场记", notes = "参数 matchType:GAME|DRILL  matchId：训练or比赛id  name：场记名称")
    public ApiResult<Object> fieldFast(@RequestBody FieldNotes fieldNotes) {
        FieldNotes fieldNotes1 = fieldNotesService.fieldFast(fieldNotes, null, null);
        return ApiResult.ok(fieldNotes1.getId());
    }

    @PostMapping("/setProjectionScreen/{id}")
    @OperationLog(name = "设置最近修改的场记内容")
    @ApiOperation(value = "设置最近修改的场记内容", notes = "id:场记id body json格式  APP自定义内容用于判断")
    public ApiResult<Object> setProjectionScreen(@PathVariable("id") Long id, @RequestBody JSONObject object) {
        redisUtils.set("projectionScreen", object, 60 * 60 * 10);
        return ApiResult.ok(true);
    }

    @PostMapping("/getProjectionScreen/{id}")
    @OperationLog(name = "设置最近修改的场记内容")
    @ApiOperation(value = "获取最近修改的场记内容", notes = "id：场记id 返回app上传的APP自定义json内容")
    public ApiResult<Object> getProjectionScreen(@PathVariable("id") Long id) {
        return ApiResult.ok(redisUtils.get("projectionScreen"));
    }
}

