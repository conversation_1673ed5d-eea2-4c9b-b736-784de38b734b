package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.CourseAmount;
import io.geekidea.springbootplus.using.mapper.CourseAmountMapper;
import io.geekidea.springbootplus.using.service.CourseAmountService;
import io.geekidea.springbootplus.using.param.CourseAmountPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-02
 */
@Slf4j
@Service
public class CourseAmountServiceImpl extends BaseServiceImpl<CourseAmountMapper, CourseAmount> implements CourseAmountService {

    @Autowired
    private CourseAmountMapper courseAmountMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveCourseAmount(CourseAmount courseAmount) throws Exception {
        return super.save(courseAmount);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateCourseAmount(CourseAmount courseAmount) throws Exception {
        return super.updateById(courseAmount);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteCourseAmount(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<CourseAmount> getCourseAmountPageList(CourseAmountPageParam courseAmountPageParam) throws Exception {
        Page<CourseAmount> page = new PageInfo<>(courseAmountPageParam, OrderItem.desc(getLambdaColumn(CourseAmount::getCreateTime)));
        LambdaQueryWrapper<CourseAmount> wrapper = new LambdaQueryWrapper<>();
        IPage<CourseAmount> iPage = courseAmountMapper.selectPage(page, wrapper);
        return new Paging<CourseAmount>(iPage);
    }

}
