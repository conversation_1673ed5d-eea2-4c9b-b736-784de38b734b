package io.geekidea.springbootplus.using.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DataStatisticsAo")
public class DataStatisticsAo {
    @ApiModelProperty("x轴日期 根据查询的type动态返回 日：2022-02-26 周：2022-02-26（周一） 月：2022-02 年：2022")
    private String date;
    @ApiModelProperty("卡路里(kcal）")
    private int calorie;
    @ApiModelProperty("步数")
    private int stepCount;
    @ApiModelProperty("跑动距离m")
    private int runDistance;
    @ApiModelProperty("高速跑动距离m")
    private int highDistance;
    @ApiModelProperty("中速跑动距离m")
    private int midDistance;
    @ApiModelProperty("低速跑动距离m")
    private int lowDistance;
    @ApiModelProperty("大于20km/h的跑动距离m")
    private int exceedDistance;
    @ApiModelProperty("大于25km/h的跑动距离m")
    private int supDistance;
}
