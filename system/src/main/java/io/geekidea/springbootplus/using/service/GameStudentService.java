package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.GameStudent;
import io.geekidea.springbootplus.using.param.GameStudentPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface GameStudentService extends BaseService<GameStudent> {

    GameStudent getOneByGameIdAndStudentId(Long gameId, Long studentId);

    Boolean setStudentGameStatus(List<GameStudent> gameStudentList);

    List<GameStudent> getStudentGameStatus(Long gameId, Long teamId);

    List<GameStudent> getStudentGameStatus(List<Long> gameIds, Long teamId, Long studentId);

    List<GameStudent> getStudentGameStatusNoCancel(Long gameId, Long teamId);

    List<GameStudent> getListByGameId(Long gameId);

    int attendance(Long gameId, Long teamId);

    boolean setGameCancel(List<String> macs);

    boolean stopGame(Long gameId);
}
