package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONArray;

public interface SystemService {
    boolean setHdVersion(Long id,JSONArray array);

    Object getHdVersion(String matchType,Long matchId);

    boolean updateVersion(String username,Integer courseId,Integer type);

    boolean updateVersion(Long schoolId,Integer courseId,Integer type);

    boolean likeIncr(String matchType,Long matchId,Long stuId);

    int getMatchLike(String matchType,Long matchId,Long stuId);
}
