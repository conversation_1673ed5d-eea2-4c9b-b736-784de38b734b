package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TeamData对象")
@TableName(autoResultMap = true)
public class TeamData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "球赛id不能为空")
    @ApiModelProperty("球赛id")
    private Long matchId;

    @ApiModelProperty("球赛类型  DRILL:训练 GAME:比赛")
    private String matchType;

    private Long teamId;

    @ApiModelProperty("团队曲线")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON curves;

    @ApiModelProperty("运动时间（秒）")
    private Long exerciseTime;

    @ApiModelProperty("版本")
    @Version
    private Integer version;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
