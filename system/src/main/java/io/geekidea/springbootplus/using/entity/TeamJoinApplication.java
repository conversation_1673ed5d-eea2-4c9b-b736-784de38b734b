package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 球队入队申请表
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TeamJoinApplication对象")
@TableName(autoResultMap = true)
public class TeamJoinApplication extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "申请用户ID不能为空")
    @ApiModelProperty("申请用户ID")
    private Long applicantUserId;

    @NotNull(message = "球队ID不能为空")
    @ApiModelProperty("球队ID")
    private Long teamId;

    @ApiModelProperty("申请理由")
    private String reason;

    @ApiModelProperty("申请状态：0-待审核，1-已通过，2-已拒绝")
    private Integer status;

    @ApiModelProperty("审核理由（拒绝时填写）")
    private String reviewReason;

    @ApiModelProperty("审核人用户ID")
    private Long reviewUserId;

    @ApiModelProperty("审核时间")
    private Date reviewTime;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Integer deleted;

    // 申请状态常量
    public static final int STATUS_PENDING = 0;
    public static final int STATUS_APPROVED = 1;
    public static final int STATUS_REJECTED = 2;
}
