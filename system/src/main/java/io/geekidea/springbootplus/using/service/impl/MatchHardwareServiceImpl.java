package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.entity.MatchHardware;
import io.geekidea.springbootplus.using.mapper.MatchHardwareMapper;
import io.geekidea.springbootplus.using.service.HardwareService;
import io.geekidea.springbootplus.using.service.MatchHardwareService;
import io.geekidea.springbootplus.using.param.MatchHardwarePageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class MatchHardwareServiceImpl extends BaseServiceImpl<MatchHardwareMapper, MatchHardware> implements MatchHardwareService {

    @Autowired
    HardwareService hardwareService;

    @Override
    public List<MatchHardware> getMatchHardwareByMatchId(Long matchId,Long teamId,String matchType) {
        return list(new LambdaQueryWrapper<MatchHardware>().eq(MatchHardware::getMatchId,matchId).eq(MatchHardware::getTeamId,teamId).eq(MatchHardware::getMatchType,matchType));
    }

    @Override
    public List<MatchHardware> getMatchHardwareByMatchId(Long matchId,String matchType) {
        return list(new LambdaQueryWrapper<MatchHardware>().eq(MatchHardware::getMatchId,matchId).eq(MatchHardware::getMatchType,matchType));
    }

}
