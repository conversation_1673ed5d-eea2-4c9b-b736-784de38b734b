package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.enums.UploadedEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Drill对象")
@TableName(autoResultMap = true)
public class Drill extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("班级id")
    private Long teamId;

    @ApiModelProperty("课程id（跑动|篮球...）")
    private Long courseId;

    @ApiModelProperty("训练名")
    private String name;

    @ApiModelProperty("训练时长 分")
    private Integer duration;

    @ApiModelProperty("跑动距离 m")
    private Integer runDistance;

    @ApiModelProperty("完成度 {\"red\":0.6,\"yellow\":0.2,\"green\":0.5}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject completeness;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date stopTime;

    @ApiModelProperty("最后学员启动时间")
    @TableField("last_start_time")
    private Date lastStartTime;

    @ApiModelProperty("第一个学员启动时间")
    @TableField("first_start_time")
    private Date firstStartTime;

    @ApiModelProperty("训练状态  进行中：START 未开始：NOTSTART 结束：FINISHED")
    private MatchStatusEnum status;

    @ApiModelProperty("是否设置过模式")
    private Boolean pattern;

    @ApiModelProperty("创建的学生 为0代表不是学生创建")
    private Long studentId;

    @ApiModelProperty("创建该类型的第几场训练")
    private Integer amount;

    @ApiModelProperty("是否有设备")
    private Boolean haveHard;

    @TableField(exist = false)
    @ApiModelProperty("训练剩余秒")
    private Long drillSec;

    @ApiModelProperty("省电时间 单位min分钟")
    private Long powerSaving;

    @ApiModelProperty("查询者跑动距离 m")
    @TableField(exist = false)
    private Integer personRunDistance;

    @ApiModelProperty("{\"1\":计时数量,\"2\":计数数量,\"3\":场记数量}")
    @TableField(exist = false)
    private Object count;

    @ApiModelProperty("训练是否作废（个人）")
    @TableField(exist = false)
    private Boolean cancel = false;

    @TableField(exist = false)
    @ApiModelProperty("是否设置了出勤队员")
    private Boolean setAttendance;

    @ApiModelProperty("是否有已启动的设备")
    @TableField(exist = false)
    private Boolean haveHardwareStarted = false;

    @TableField(exist = false)
    @ApiModelProperty("是否同步数据 //NOT未同步 - ALL全部同步 - PORTION部分同步")
    private UploadedEnum uploaded;

    @TableField(exist = false)
    @ApiModelProperty("所在场记的小组的小组id getDrillListByStu和getPlayerData查询")
    private Long fieldGroupId;

    @TableField(exist = false)
    @ApiModelProperty("计数/记时")
    private List<DrillExtra> drillExtras;

    @TableField(exist = false)
    @ApiModelProperty("快速场记名字 不需要就不传")
    private String fastFdName;

    @ApiModelProperty("班级所在设备组的id，没有就发-1")
    @TableField(exist = false)
    private Long groupId = -1l;

    @TableField(exist = false)
    @ApiModelProperty("参考 getPlayerData 同字段")
    private JSONObject extra;

    @ApiModelProperty("班级所在设备组的名字，没有就发null")
    @TableField(exist = false)
    private String groupName;

    @ApiModelProperty("卡路里(kcal）")
    @TableField(exist = false)
    private Integer calorie;

    @ApiModelProperty("用户是否启动了这场比赛")
    @TableField(exist = false)
    private Boolean currentUserStatus;

    @ApiModelProperty("用户篮球分数")
    @TableField(exist = false)
    private Integer currentUserFraction;

    @ApiModelProperty("数据实际开始统计时间搓")
    private Long realDataTime;

    @ApiModelProperty("查询者是否查看了这场数据")
    @TableField(exist = false)
    private Boolean appLook;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;
}
