package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TeamJoinApplication;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 * 球队入队申请表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface TeamJoinApplicationService extends BaseService<TeamJoinApplication> {

    /**
     * 申请加入球队
     *
     * @param teamId 球队ID
     * @param applicantUserId 申请人用户ID
     * @param reason 申请理由
     * @return 申请记录ID
     * @throws Exception
     */
    Long applyJoinTeam(Long teamId, Long applicantUserId, String reason) throws Exception;

    /**
     * 审核入队申请
     *
     * @param applicationId 申请ID
     * @param approved 是否通过
     * @param reviewReason 审核理由
     * @param reviewUserId 审核人ID
     * @return 是否成功
     * @throws Exception
     */
    boolean reviewApplication(Long applicationId, Boolean approved, String reviewReason, Long reviewUserId) throws Exception;

    /**
     * 获取球队的待审核申请列表
     *
     * @param teamId 球队ID
     * @return 申请列表
     */
    List<TeamJoinApplication> getPendingApplicationsByTeamId(Long teamId);

    /**
     * 检查用户是否已申请加入该球队
     *
     * @param teamId 球队ID
     * @param userId 用户ID
     * @return 是否已申请
     */
    boolean hasApplied(Long teamId, Long userId);
}
