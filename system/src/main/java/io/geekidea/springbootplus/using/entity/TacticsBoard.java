package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import com.alibaba.fastjson.JSONArray;

import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 战术板主表，存储战术板基本信息
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TacticsBoard对象")
@TableName(autoResultMap = true)
public class TacticsBoard extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("战术板ID，唯一标识")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户id")
    private Long userId;

    @NotBlank(message = "战术板名称不能为空")
    @ApiModelProperty("战术板名称")
    private String boardName;

    @ApiModelProperty("战术板背景图片URL")
    private String bgUrl;

    @ApiModelProperty("战术文件|图片or视频")
    private String contentUrl;

    @ApiModelProperty("战术板宽度，用于坐标定位换算")
    private Float boardWidth;

    @ApiModelProperty("战术板高度，用于坐标定位换算")
    private Float boardHeight;

    @ApiModelProperty("战术板类型：1-静态，2-动态")
    private Integer boardType;

    @ApiModelProperty("录音文件存储路径（无录音则空）")
    private String audioPath;

    @ApiModelProperty("录音时长（秒，无录音则0）")
    private Integer audioDuration;

    @ApiModelProperty("每一帧的时间")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray frameTime;//

    @ApiModelProperty("全局所有帧统-时间")
    private double frameTimePer = 0.8;//

    @ApiModelProperty("总帧数")
    private int frameCount = 1;//

    @ApiModelProperty("人物列表 - 修改时：传入此字段将替换所有球员信息 ；不传入则不修改球员信息")
    @TableField(exist = false)
    private List<TacticsBoardPlayer> playerList;

    @ApiModelProperty("线部分列表 - 修改时：传入此字段将替换所有线条信息 ；不传入则不修改线条信息")
    @TableField(exist = false)
    private List<TacticsBoardLine> lineList;

    @ApiModelProperty("工具列表 - 修改时：传入此字段将替换所有工具信息 ；不传入则不修改工具信息")
    @TableField(exist = false)
    private List<TacticsBoardTool> toolList;

    @ApiModelProperty("文字列表 - 修改时：传入此字段将替换所有文本信息 ；不传入则不修改文本信息")
    @TableField(exist = false)
    private List<TacticsBoardText> textList;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间，自动更新")
    private Date updateTime;

}
