package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.GameExtraGroup;
import io.geekidea.springbootplus.using.mapper.GameExtraGroupMapper;
import io.geekidea.springbootplus.using.service.GameExtraGroupService;
import io.geekidea.springbootplus.using.param.GameExtraGroupPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Slf4j
@Service
public class GameExtraGroupServiceImpl extends BaseServiceImpl<GameExtraGroupMapper, GameExtraGroup> implements GameExtraGroupService {

    @Autowired
    private GameExtraGroupMapper gameExtraGroupMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveGameExtraGroup(GameExtraGroup gameExtraGroup) throws Exception {
        return super.save(gameExtraGroup);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateGameExtraGroup(GameExtraGroup gameExtraGroup) throws Exception {
        return super.updateById(gameExtraGroup);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteGameExtraGroup(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<GameExtraGroup> getGameExtraGroupPageList(GameExtraGroupPageParam gameExtraGroupPageParam) throws Exception {
        Page<GameExtraGroup> page = new PageInfo<>(gameExtraGroupPageParam, OrderItem.desc(getLambdaColumn(GameExtraGroup::getCreateTime)));
        LambdaQueryWrapper<GameExtraGroup> wrapper = new LambdaQueryWrapper<>();
        IPage<GameExtraGroup> iPage = gameExtraGroupMapper.selectPage(page, wrapper);
        return new Paging<GameExtraGroup>(iPage);
    }

    @Override
    public List<GameExtraGroup> getGameExtraGroup(Long gameExtaId) {
        return list(new LambdaQueryWrapper<GameExtraGroup>().eq(GameExtraGroup::getGameExtraId, gameExtaId));
    }

}
