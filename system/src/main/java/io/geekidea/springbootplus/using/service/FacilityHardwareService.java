package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.FacilityHardware;
import io.geekidea.springbootplus.using.param.FacilityHardwarePageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.service.impl.FacilityHardwareServiceImpl;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-08-16
 */
public interface FacilityHardwareService extends BaseService<FacilityHardware> {

    /**
     * 保存
     *
     * @param facilityHardware
     * @return
     * @throws Exception
     */
    List<FacilityHardware> saveFacilityHardware(List<FacilityHardware> facilityHardware) throws Exception;

    /**
     * 修改
     *
     * @param facilityHardware
     * @return
     * @throws Exception
     */
    boolean updateFacilityHardware(FacilityHardware facilityHardware) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteFacilityHardware(Long id) throws Exception;

    List<FacilityHardware> getFacilityHardwareList(Long groupId) ;

    int getFacilityHardwareCount(Long groupId) ;

    Boolean groupHardUpdate(List<FacilityHardware> facilityHardwares);

    Boolean groupHardReplace(String mac,FacilityHardware facilityHardware);


}
