package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.using.entity.TacticsBoardCourse;
import io.geekidea.springbootplus.using.service.TacticsBoardCourseService;
import io.geekidea.springbootplus.using.service.UserFavoriteTacticsService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.TacticsBoardPageParam;
import io.geekidea.springbootplus.using.param.UserFavoriteTacticsPageParam;
import io.geekidea.springbootplus.framework.common.controller.BaseController;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 战术板综合信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-22
 */
@Slf4j
@RestController
@RequestMapping("/using/tacticsBoardCourse")
@Module("${cfg.module}")
@Api(value = "战术板课件相关", tags = {"战术板课件相关"})
public class TacticsBoardCourseController extends BaseController {

    @Autowired
    private TacticsBoardCourseService tacticsBoardCourseService;

    @Autowired
    private UserFavoriteTacticsService userFavoriteTacticsService;

    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class)
    public ApiResult addTacticsBoard(@RequestBody TacticsBoardCourse tacticsBoardCourse) throws Exception {
        return ApiResult.ok(tacticsBoardCourseService.saveTacticsBoard(tacticsBoardCourse));
    }


    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    public ApiResult<Boolean> updateTacticsBoard(@RequestBody TacticsBoardCourse tacticsBoardCourse) throws Exception {
        boolean flag = tacticsBoardCourseService.updateTacticsBoard(tacticsBoardCourse);
        return ApiResult.result(flag);
    }


    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteTacticsBoard(@PathVariable("id") Long id) throws Exception {
        boolean flag = tacticsBoardCourseService.deleteTacticsBoard(id);
        return ApiResult.result(flag);
    }


    @GetMapping("/getById/{id}")
    @ApiOperation(value = "根据ID获取战术板课件详情", response = TacticsBoardCourse.class)
    public ApiResult<TacticsBoardCourse> getTacticsBoardById(@PathVariable("id") Long id) throws Exception {
        TacticsBoardCourse tacticsBoard = tacticsBoardCourseService.getTacticsBoardById(id);
        return ApiResult.ok(tacticsBoard);
    }


    @PostMapping("/getPageList")
    @OperationLog(name = "战术板课件分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "战术板课件分页列表", response = TacticsBoardCourse.class)
    public ApiResult<Paging<TacticsBoardCourse>> getTacticsBoardPageList(@RequestBody TacticsBoardPageParam tacticsBoardPageParam) throws Exception {
        tacticsBoardPageParam.setBoardName(tacticsBoardPageParam.getKeyword());
        Paging<TacticsBoardCourse> paging = tacticsBoardCourseService.getTacticsBoardPageList(tacticsBoardPageParam);
        return ApiResult.ok(paging);
    }


    @PostMapping("/deletes")
    @OperationLog(name = "批量删除战术板", type = OperationLogType.DELETE)
    @ApiOperation(value = "批量删除战术板", response = ApiResult.class, notes = "body传入战术板id集合[{\"id\":1}]")
    public ApiResult<Boolean> deleteTacticsBoardList(@RequestBody List<TacticsBoardCourse> tacticsBoardCourseList) throws Exception {
        boolean flag = tacticsBoardCourseService.deleteTacticsBoardList(tacticsBoardCourseList.stream().map(TacticsBoardCourse::getId).collect(Collectors.toList()));
        return ApiResult.result(flag);
    }


    @PostMapping("/toggleFavorite")
    @OperationLog(name = "收藏或取消收藏战术板", type = OperationLogType.UPDATE)
    @ApiOperation(value = "收藏或取消收藏战术板", response = ApiResult.class,
            notes = "传入用户ID、战术板ID和收藏状态。isFavorite=true表示收藏，false表示取消收藏")
    public ApiResult<Boolean> toggleTacticsBoardFavorite(@Validated @RequestBody UserFavoriteTacticsPageParam favoriteParam) throws Exception {
        boolean flag = tacticsBoardCourseService.toggleTacticsBoardFavorite(favoriteParam);
        return ApiResult.result(flag);
    }

}