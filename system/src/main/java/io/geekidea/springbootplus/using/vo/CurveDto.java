package io.geekidea.springbootplus.using.vo;

import io.geekidea.springbootplus.using.enums.CurveEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Data
public class CurveDto {
    private CurveEnum curve;
    private List<Double> axisX = new ArrayList<>();
    private List<Integer> axisY = new ArrayList<>();
    private double maxX;
    private int maxY;
    private int sumY;
    private long startTime;

    public void setAxisX(List<Double> axisX) {
        List<Double> x = new ArrayList<>();
        if (this.axisX.size() > 0 && this.axisX.size() == axisX.size()) {
            for (int i = 0; i < this.axisX.size(); i++) {
                x.add(Double.parseDouble(String.format("%.1f", (this.axisX.get(i) + axisX.get(i) / 2))));
            }
            this.axisX = x;
        } else {
            this.axisX = axisX;
            x = axisX;
        }
        if(x.size()>0) {
            this.maxX = x.stream().max(Comparator.comparing(e -> e)).get();
        }
    }

    public void setAxisY(List<Integer> axisY) {
        List<Integer> y = new ArrayList<>();
        if (this.axisY.size() > 0 && this.axisY.size() == axisY.size()) {
            for (int i = 0; i < this.axisY.size(); i++) {
                y.add((this.axisY.get(i) + axisY.get(i)) / 2);
            }
            this.axisY = y;
        } else {
            this.axisY = axisY;
            y = axisY;
        }
        if(y.size()>0) {
            this.sumY = y.stream().mapToInt(e -> e).sum();
            this.maxY = y.stream().max(Comparator.comparing(e -> e)).get();
        }
    }
}
