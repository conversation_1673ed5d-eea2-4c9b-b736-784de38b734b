package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.TacticsBoardLine;
import io.geekidea.springbootplus.using.mapper.TacticsBoardLineMapper;
import io.geekidea.springbootplus.using.service.TacticsBoardLineService;
import io.geekidea.springbootplus.using.param.TacticsBoardLinePageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 战术板线条表，存储各种线条信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@Service
public class TacticsBoardLineServiceImpl extends BaseServiceImpl<TacticsBoardLineMapper, TacticsBoardLine> implements TacticsBoardLineService {

    @Autowired
    private TacticsBoardLineMapper tacticsBoardLineMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveTacticsBoardLine(TacticsBoardLine tacticsBoardLine) throws Exception {
        return super.save(tacticsBoardLine);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTacticsBoardLine(TacticsBoardLine tacticsBoardLine) throws Exception {
        return super.updateById(tacticsBoardLine);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTacticsBoardLine(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<TacticsBoardLine> getTacticsBoardLinePageList(TacticsBoardLinePageParam tacticsBoardLinePageParam) throws Exception {
        Page<TacticsBoardLine> page = new PageInfo<>(tacticsBoardLinePageParam);
        LambdaQueryWrapper<TacticsBoardLine> wrapper = new LambdaQueryWrapper<>();
        IPage<TacticsBoardLine> iPage = tacticsBoardLineMapper.selectPage(page, wrapper);
        return new Paging<TacticsBoardLine>(iPage);
    }

}
