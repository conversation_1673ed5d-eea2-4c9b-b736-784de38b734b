package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.TeacherInfo;
import io.geekidea.springbootplus.using.mapper.TeacherInfoMapper;
import io.geekidea.springbootplus.using.service.TeacherInfoService;
import io.geekidea.springbootplus.using.param.TeacherInfoPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class TeacherInfoServiceImpl extends BaseServiceImpl<TeacherInfoMapper, TeacherInfo> implements TeacherInfoService {

    @Autowired
    private TeacherInfoMapper teacherInfoMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveTeacherInfo(TeacherInfo teacherInfo) throws Exception {
        return super.save(teacherInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTeacherInfo(TeacherInfo teacherInfo) throws Exception {
        return super.updateById(teacherInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTeacherInfo(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<TeacherInfo> getTeacherInfoPageList(TeacherInfoPageParam teacherInfoPageParam) throws Exception {
        Page<TeacherInfo> page = new PageInfo<>(teacherInfoPageParam, OrderItem.desc(getLambdaColumn(TeacherInfo::getCreateTime)));
        LambdaQueryWrapper<TeacherInfo> wrapper = new LambdaQueryWrapper<>();
        IPage<TeacherInfo> iPage = teacherInfoMapper.selectPage(page, wrapper);
        return new Paging<TeacherInfo>(iPage);
    }

    @Override
    public TeacherInfo getByUserId(Long userId) {
        return getOne(new LambdaQueryWrapper<TeacherInfo>().eq(TeacherInfo::getUserId,userId));
    }

    @Override
    public List<TeacherInfo> getBySchoolId(Long scholldId) {
        return list(new LambdaQueryWrapper<TeacherInfo>().eq(TeacherInfo::getSchoolId,scholldId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateBigUrl(Long id, String bigUrl) throws Exception {
        TeacherInfo teacherInfo = new TeacherInfo();
        teacherInfo.setId(id);
        teacherInfo.setBigUrl(bigUrl);
        return super.updateById(teacherInfo);
    }

}
