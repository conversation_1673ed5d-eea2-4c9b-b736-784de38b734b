package io.geekidea.springbootplus.using.service.strategy.impl;

import io.geekidea.springbootplus.using.service.DelayQueueService;
import io.geekidea.springbootplus.using.service.TransparentMessageService;
import io.geekidea.springbootplus.using.service.strategy.TaskStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("TeamPastDelayQueue")
public class TeamPastStrategyImpl implements TaskStrategy {
    @Autowired
    TransparentMessageService transparentMessageService;
    @Autowired
    DelayQueueService delayQueueService;

    @Override
    public void dispose(Object task) {
        transparentMessageService.sendTransparentByGroupPast(Long.parseLong(task.toString()));
        delayQueueService.removeGroupMoreRestrictDelayQueue(Long.parseLong(task.toString()));
    }
}
