package io.geekidea.springbootplus.using.vo;

import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "StudentInfo 封装对象")
public class PlayerAo {
    @ApiModelProperty("学生Id")
    private Long studentId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("学号")
    private String card;

    @ApiModelProperty("头像")
    private String headImg;

    @ApiModelProperty("训练id")
    private Long drillId;

    @ApiModelProperty("课程id")
    private Long courseId;

    @ApiModelProperty("训练时长 分")
    private Integer duration;

    @ApiModelProperty("当前训练剩余秒")
    private Long drillSec;

    @ApiModelProperty("出勤状态 1正常 0缺勤")
    private boolean status;

    @ApiModelProperty("学员当前训练状态 未开始:NOTSTARTED,已开始:STARTED,已结束:FINISHED,UNFINISHED:")
    private MatchStatusEnum matchStatus;

    @ApiModelProperty("训练是否作废（个人）")
    private Boolean cancel;

    @ApiModelProperty("学生实际训练开始时间")
    private Date startTime;

    @ApiModelProperty("学生实际训练结束时间")
    private Date stopTime;

    @ApiModelProperty("后台实时时间搓")
    private Long now;

    @ApiModelProperty("设备编号")
    private Integer hardNum;

    @ApiModelProperty("足球球衣号")
    private Integer footballShirt;

    @ApiModelProperty("篮球球衣号")
    private Integer shirt;

    @ApiModelProperty("学生设备")
    private Hardware hardware;

    public void setDrillSec(Long drillSec) {
        if(drillSec < 0){
            drillSec = 0l;
        }
        this.drillSec = drillSec;
    }
}
