package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;

public interface ProDataService {

    Object getComprehensiveScoreRanking(Long teamId,Long matchId,String matchType, Long stuId, Boolean isNew, JSONObject teamRankingData);

    Object getProTeamData(Long teamId, Long matchId,String matchType);

    Object highLadder(Long teamId,Long matchId,String matchType,Long studentId);

    void delComprehensiveScoreRankingCacheArray(Long teamId, Long matchId,String matchType);
}
