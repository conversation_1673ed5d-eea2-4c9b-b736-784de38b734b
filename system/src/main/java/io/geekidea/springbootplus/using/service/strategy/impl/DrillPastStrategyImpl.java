package io.geekidea.springbootplus.using.service.strategy.impl;

import io.geekidea.springbootplus.using.entity.Drill;
import io.geekidea.springbootplus.using.entity.FieldNotes;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.mapper.FieldNotesMapper;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.service.strategy.TaskStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service("DrillPastDelayQueue")
@Slf4j
public class DrillPastStrategyImpl implements TaskStrategy {

    @Autowired
    DrillService drillService;
    @Autowired
    DrillStudentService drillStudentService;
    @Autowired
    DelayQueueService delayQueueService;
    @Autowired
    FieldNotesMapper fieldNotesMapper;
    @Autowired
    TransparentMessageService transparentMessageService;


    @Override
    public void dispose(Object task) {
        log.info("处理训练自动过期任务id：{}", task);
        // 当前时间
        Long now = System.currentTimeMillis();
        Drill drill = drillService.getById(Long.parseLong(task.toString()));
        if (drill!=null) {
            Long date = drill.getStartTime().getTime() + (drill.getDuration() * (60 * 1000));
            Long time = (date - now) / 1000;
            if (time <= 0) {
                try {
                    transparentMessageService.sendTransparentByDrillPast(drill);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                drill.setStatus(MatchStatusEnum.FINISHED);
                drill.setStopTime(new Date());
                drillService.saveOrUpdate(drill);
                //比赛待补充
                fieldNotesMapper.updateFin(drill.getId(),"DRILL");
                // 删除已经执行的任务
                delayQueueService.removeDrillDelayQueue(drill.getId());
            }
        }else{
            delayQueueService.removeDrillDelayQueue(Long.parseLong(task.toString()));
        }
    }
}
