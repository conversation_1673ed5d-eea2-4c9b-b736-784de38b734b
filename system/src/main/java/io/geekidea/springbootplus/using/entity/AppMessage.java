package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * APP消息表
 *
 * 用于存储APP内的消息通知，主要包括球队相关的各种操作通知
 * 支持的消息类型：
 * - APPLY_JOIN：申请加入球队（通知队长）
 * - JOIN_SUCCESS：成功加入球队（通知申请人）
 * - JOIN_REJECTED：入队申请被拒绝（通知申请人）
 * - MEMBER_JOINED：新成员加入（通知其他现有成员）
 * - TEAM_DISSOLVED：球队解散（通知所有成员）
 * - KICKED_OUT：被踢出球队（通知被删除成员）
 * - MEMBER_QUIT：成员退出（通知队长）
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AppMessage对象")
@TableName(autoResultMap = true)
public class AppMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @NotNull(message = "接收用户ID不能为空")
    @ApiModelProperty("接收用户ID")
    private Long receiverUserId;

    @ApiModelProperty("发送用户ID（系统消息时为null）")
    private Long senderUserId;

    @NotNull(message = "消息类型不能为空")
    @ApiModelProperty("消息类型：APPLY_JOIN-申请加入球队，JOIN_SUCCESS-成功加入球队，JOIN_REJECTED-入队申请被拒绝，MEMBER_JOINED-新成员加入（通知队长），TEAM_DISSOLVED-球队解散，KICKED_OUT-被踢出球队，MEMBER_QUIT-成员退出（通知队长）")
    private String messageType;

    @NotNull(message = "消息标题不能为空")
    @ApiModelProperty("消息标题")
    private String title;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("扩展数据（JSON格式）：存储消息相关的额外信息，如{\"teamId\":1,\"teamName\":\"球队名称\",\"applicantUserId\":2,\"applicantName\":\"申请人姓名\"}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject extraData;

    @ApiModelProperty("是否已读：false-未读，true-已读")
    private Boolean isRead;

    @ApiModelProperty("已读时间")
    private Date readTime;

    @ApiModelProperty("审核状态：0-未审核，1-已同意，2-已拒绝（仅适用于APPLY_JOIN类型消息）")
    private Integer reviewStatus;

    @ApiModelProperty("审核时间")
    private Date reviewTime;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Integer deleted;

    // 消息类型常量定义

    /** 申请加入球队 - 通知队长有新的入队申请 */
    public static final String TYPE_APPLY_JOIN = "APPLY_JOIN";

    /** 成功加入球队 - 通知申请人审核通过，已成功加入球队 */
    public static final String TYPE_JOIN_SUCCESS = "JOIN_SUCCESS";

    /** 入队申请被拒绝 - 通知申请人审核未通过，申请被拒绝 */
    public static final String TYPE_JOIN_REJECTED = "JOIN_REJECTED";

    /** 新成员加入 - 通知除新成员外的所有现有球队成员有新人加入 */
    public static final String TYPE_MEMBER_JOINED = "MEMBER_JOINED";

    /** 球队解散 - 通知所有球队成员球队已被队长解散 */
    public static final String TYPE_TEAM_DISSOLVED = "TEAM_DISSOLVED";

    /** 被踢出球队 - 通知被删除的成员已被队长移出球队 */
    public static final String TYPE_KICKED_OUT = "KICKED_OUT";

    /** 成员退出 - 通知队长有成员主动退出球队 */
    public static final String TYPE_MEMBER_QUIT = "MEMBER_QUIT";

    // 审核状态常量定义

    /** 未审核 - 申请消息的初始状态 */
    public static final int REVIEW_STATUS_PENDING = 0;

    /** 已同意 - 队长同意了入队申请 */
    public static final int REVIEW_STATUS_APPROVED = 1;

    /** 已拒绝 - 队长拒绝了入队申请 */
    public static final int REVIEW_STATUS_REJECTED = 2;
}
