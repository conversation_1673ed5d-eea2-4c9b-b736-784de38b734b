package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FieldGroup对象")
public class FieldGroup extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("编号")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("场记id")
    private Long fieldId;

    @ApiModelProperty("学员 ")
    @TableField(exist = false)
    private List<FieldData> fieldData;

    @ApiModelProperty("小组名称")
    private String groupName;

    @ApiModelProperty("小组分数")
    private Integer groupFraction;

    @ApiModelProperty("小组控球时间")
    private Long groupControl;

    @ApiModelProperty("小组角球数")
    private Integer groupHorn;

    @ApiModelProperty("小组任意球数")
    private Integer groupFree;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty("小组统计场记数据（用于显示，返回跟个人场记数据一样）")
    @TableField(exist = false)
    private JSONObject groupMap;



}
