package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.DemoMacQrcode;
import io.geekidea.springbootplus.using.param.DemoMacQrcodePageParam;
import io.geekidea.springbootplus.using.vo.DemoMacQrcodeAo;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface DemoMacQrcodeService extends BaseService<DemoMacQrcode> {

    /**
     * 保存
     *
     * @param demoMacQrcode
     * @return
     * @throws Exception
     */
    boolean saveDemoMacQrcode(DemoMacQrcode demoMacQrcode,Long userId) throws Exception;

    /**
     * 修改
     *
     * @param demoMacQrcode
     * @return
     * @throws Exception
     */
    boolean updateDemoMacQrcode(List<DemoMacQrcode> demoMacQrcodes,Long userId,String type) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteDemoMacQrcode(List<DemoMacQrcode> demoMacQrcodes,Long userId) throws Exception;


    /**
     * 获取分页对象
     *
     * @param demoMacQrcodeQueryParam
     * @return
     * @throws Exception
     */
    Paging<DemoMacQrcode> getDemoMacQrcodePageList(DemoMacQrcodePageParam demoMacQrcodePageParam) throws Exception;

    DemoMacQrcodeAo findMac(String qrCode);

    List<DemoMacQrcodeAo> findMacBybox(String boxCode);

    List<DemoMacQrcodeAo> findMacByboxNum(String boxCode);

    DemoMacQrcodeAo findByMac(String mac);

    DemoMacQrcodeAo analyzeQRCode(DemoMacQrcode macQrcode);

    int getCountByBox(String box);

    boolean hardformatCheckCode(String qrCode);


    boolean hardformatCheckMac(String mac);

    void test();
}
