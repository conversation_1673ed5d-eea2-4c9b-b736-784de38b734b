package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.TeamData;
import io.geekidea.springbootplus.using.mapper.TeamDataMapper;
import io.geekidea.springbootplus.using.service.TeamDataService;
import io.geekidea.springbootplus.using.param.TeamDataPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Slf4j
@Service
public class TeamDataServiceImpl extends BaseServiceImpl<TeamDataMapper, TeamData> implements TeamDataService {

    @Autowired
    private TeamDataMapper teamDataMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveTeamData(TeamData teamData) throws Exception {
        return super.save(teamData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTeamData(TeamData teamData) throws Exception {
        return super.updateById(teamData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTeamData(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<TeamData> getTeamDataPageList(TeamDataPageParam teamDataPageParam) throws Exception {
        Page<TeamData> page = new PageInfo<>(teamDataPageParam, OrderItem.desc(getLambdaColumn(TeamData::getCreateTime)));
        LambdaQueryWrapper<TeamData> wrapper = new LambdaQueryWrapper<>();
        IPage<TeamData> iPage = teamDataMapper.selectPage(page, wrapper);
        return new Paging<TeamData>(iPage);
    }

    @Override
    public TeamData findOfTeam(Long mactchId,Long teamId,String matchType) {
        return getOne(new LambdaQueryWrapper<TeamData>().eq(TeamData::getMatchId,mactchId).eq(TeamData::getTeamId,teamId).eq(TeamData::getMatchType,matchType));
    }

    @Override
    public List<TeamData> findOfTeamList(Long teamId,String matchType) {
        return list(new LambdaQueryWrapper<TeamData>().eq(TeamData::getTeamId,teamId).eq(TeamData::getMatchType,matchType));
    }

}
