package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.DrillStudent;
import io.geekidea.springbootplus.using.entity.KindergartenMonitor;
import io.geekidea.springbootplus.using.entity.KindergartenMonitorStudent;
import io.geekidea.springbootplus.using.mapper.KindergartenMonitorStudentMapper;
import io.geekidea.springbootplus.using.service.KindergartenMonitorStudentService;
import io.geekidea.springbootplus.using.param.KindergartenMonitorStudentPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
@Slf4j
@Service
public class KindergartenMonitorStudentServiceImpl extends BaseServiceImpl<KindergartenMonitorStudentMapper, KindergartenMonitorStudent> implements KindergartenMonitorStudentService {

    @Autowired
    private KindergartenMonitorStudentMapper kindergartenMonitorStudentMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveKindergartenMonitorStudent(KindergartenMonitorStudent kindergartenMonitorStudent) throws Exception {
        return super.save(kindergartenMonitorStudent);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateKindergartenMonitorStudent(KindergartenMonitorStudent kindergartenMonitorStudent) throws Exception {
        return super.updateById(kindergartenMonitorStudent);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteKindergartenMonitorStudent(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<KindergartenMonitorStudent> getKindergartenMonitorStudentPageList(KindergartenMonitorStudentPageParam kindergartenMonitorStudentPageParam) throws Exception {
        Page<KindergartenMonitorStudent> page = new PageInfo<>(kindergartenMonitorStudentPageParam, OrderItem.desc(getLambdaColumn(KindergartenMonitorStudent::getCreateTime)));
        LambdaQueryWrapper<KindergartenMonitorStudent> wrapper = new LambdaQueryWrapper<>();
        IPage<KindergartenMonitorStudent> iPage = kindergartenMonitorStudentMapper.selectPage(page, wrapper);
        return new Paging<KindergartenMonitorStudent>(iPage);
    }

    @Override
    public KindergartenMonitorStudent getOneByMonitorIdAndStudentId(Long monitorId, Long studentId) {
        return getOne(new LambdaQueryWrapper<KindergartenMonitorStudent>().eq(KindergartenMonitorStudent::getMonitorId, monitorId).eq(KindergartenMonitorStudent::getStudentId, studentId));
    }

    @Override
    public List<KindergartenMonitorStudent> getList(Long monitorId, Long teamId) {
        return list(new LambdaQueryWrapper<KindergartenMonitorStudent>().eq(KindergartenMonitorStudent::getMonitorId, monitorId).eq(KindergartenMonitorStudent::getTeamId, teamId));
    }

    @Override
    public boolean setMoitorCancel(List<String> macs) {
        if (macs.size() == 0) {
            return false;
        }
        List<KindergartenMonitorStudent> drillStudents = getBaseMapper().getNoUploaded(macs);
        List<KindergartenMonitorStudent> drillStudents1 = new ArrayList<>();
        if (drillStudents.size() == 0) {
            return false;
        }
        drillStudents.forEach(e -> {
            KindergartenMonitorStudent drillStudent = getById(e.getId());
            drillStudent.setCancel(true);
            drillStudents1.add(drillStudent);
        });
        return saveOrUpdateBatch(drillStudents1);
    }

    @Override
    public long cancelCount(Long monitorId,Long teamId) {
        return count(new LambdaQueryWrapper<KindergartenMonitorStudent>().eq(KindergartenMonitorStudent::getCancel,1).eq(KindergartenMonitorStudent::getMonitorId,monitorId).eq(KindergartenMonitorStudent::getTeamId,teamId));
    }

}
