package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.geekidea.springbootplus.framework.aop.annotation.TextCensorUserDefined;
import io.geekidea.springbootplus.framework.util.SensitiveWordUtil;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.param.FieldDataPageParam;
import io.geekidea.springbootplus.using.param.FieldNotesPageParam;
import io.geekidea.springbootplus.using.param.GameExtraPageParam;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.vo.GameExtraUserAo;
import io.geekidea.springbootplus.using.vo.StuPersonDataAo;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.GamePageParam;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Slf4j
@RestController
@RequestMapping("/using/game")
@Module("game")
@Api(value = "APP比赛模块", tags = {"APP比赛模块"})
public class GameController extends BaseController {

    @Autowired
    private GameService gameService;
    @Autowired
    private GameDataService gameDataService;
    @Autowired
    private GameStudentService gameStudentService;
    @Autowired
    private GameExtraService gameExtraService;
    @Autowired
    private IconService iconService;
    @Autowired
    private FieldDataService fieldDataService;
    @Autowired
    private FieldNotesService fieldNotesService;


    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = Long.class)
    @TextCensorUserDefined
    public ApiResult<Long> addGame(@Validated(Add.class) @RequestBody Game game) throws Exception {
        boolean flag = gameService.saveGame(game);
        return ApiResult.ok(game.getId());
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    @TextCensorUserDefined
    public ApiResult<Boolean> updateGame(@Validated(Update.class) @RequestBody Game game) throws Exception {
        boolean flag = gameService.updateGame(game);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteGame(@PathVariable("id") Long id) throws Exception {
        boolean flag = gameService.deleteGame(id);
        return ApiResult.result(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}/{userId}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = Game.class)
    public ApiResult<Game> getGame(@PathVariable("id") Long id, @PathVariable("userId") Long userId) throws Exception {
        Game game = gameService.getGame(id, userId);
        return ApiResult.ok(game);
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = Game.class)
    public ApiResult<Paging<Game>> getGamePageList(@RequestBody GamePageParam gamePageParam) throws Exception {
        Paging<Game> paging = gameService.getGamePageList1(gamePageParam);
        return ApiResult.ok(paging);
    }


    @GetMapping("/getStuHardGameStatus/{gameId}/{userId}/{teamId}")
    @ApiOperation(value = "获取某一场训练的某一学生的设备启动状态", response = ApiResult.class, notes = "返回true||false")
    public ApiResult getStuHardGameStatus(@PathVariable("gameId") Long gameId, @PathVariable("userId") Long userId, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(gameService.getStuHardGameStatus(gameId, userId, teamId));
    }

    @GetMapping("/stopGame/{gameId}")
    @OperationLog(name = "结束比赛")
    @ApiOperation(value = "结束比赛", response = Boolean.class, notes = "")
    public ApiResult<Boolean> stopGame(@PathVariable("gameId") Long gameId) throws Exception {
        return ApiResult.result(gameService.stopGame(gameId));
    }

    @PostMapping("/setStudentGameStatus")
    @OperationLog(name = "设置学员出勤状态")
    @ApiOperation(value = "设置学员出勤状态", response = Boolean.class, notes = "")
    public ApiResult<Boolean> setStudentGameStatus(@RequestBody List<GameStudent> gameStudentList) throws Exception {
        boolean flag = gameStudentService.setStudentGameStatus(gameStudentList);
        return ApiResult.result(flag);
    }

    @GetMapping("/getStudentGameStatus/{gameId}/{teamId}")
    @OperationLog(name = "获取学员出勤状态")
    @ApiOperation(value = "获取学员出勤状态", response = GameStudent.class, notes = "")
    public ApiResult getStudentGameStatus(@PathVariable("gameId") Long gameId, @PathVariable("teamId") Long teamId) throws Exception {
        return ApiResult.ok(gameStudentService.getStudentGameStatus(gameId, teamId));
    }

    @GetMapping("/isSetStatus/{gameId}/{teamId}")
    @OperationLog(name = "比赛是否设置过出勤")
    @ApiOperation(value = "比赛是否设置过出勤", response = Boolean.class, notes = "返回true 代表设置过")
    public ApiResult isSetStatus(@PathVariable("gameId") Long gameId, @PathVariable("teamId") Long teamId) throws Exception {
        return ApiResult.ok(gameStudentService.getStudentGameStatus(gameId, teamId).size() > 0);
    }

    @PostMapping(value = "/uploadData/{gameId}")
    @OperationLog(name = "上传数据")
    @ApiOperation(value = "上传数据", response = Boolean.class, notes = "参考训练模块上传数据")
    public ApiResult<Boolean> uploadData(@RequestBody JSONArray strJSONArray, @PathVariable("gameId") Long gameId) {
        boolean flag = gameDataService.uploadData(strJSONArray.toJSONString(), gameId, "GAME");
        return ApiResult.result(flag);
    }

    @GetMapping(value = "/getPlayerData/{matchId}/{studentId}/{teamId}")
    @OperationLog(name = "获取数据")
    @ApiOperation(value = "获取数据", response = Object.class, notes = "参考训练模块获取数据 新增返回信息 userApp返回查询的用户  参考登录接口 \n" +
            "个人数据增加额外返回字段 gameInfo:参考比赛详情")
    public ApiResult<Object> getPlayerData(@PathVariable("matchId") Long matchId, @PathVariable(value = "studentId", required = false) Long studentId, @PathVariable("teamId") Long teamId) {
        if (studentId == -1) {
            studentId = null;
        }
        return ApiResult.ok(gameDataService.getPlayerData(matchId, studentId, teamId, "GAME"));
    }

    @GetMapping(value = "/getPhysicalAnalysis/{matchId}/{teamId}")
    @OperationLog(name = "体能分析排名")
    @ApiOperation(value = "体能分析排名", response = Object.class, notes = "参考训练模块体能分析排名 ")
    public ApiResult<Object> getPhysicalAnalysis(@PathVariable("matchId") Long matchId, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(gameDataService.getPhysicalAnalysis(matchId, teamId, "GAME"));
    }

    @GetMapping(value = "/moveDistanceRanking/{matchId}/{key}/{teamId}")
    @OperationLog(name = "跑动排名")
    @ApiOperation(value = "跑动排名", response = Object.class, notes = "参考训练模块跑动排名")
    public ApiResult<Object> moveDistanceRanking(@PathVariable("matchId") Long matchId, @PathVariable("key") String key, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(gameDataService.moveDistanceRanking(matchId, key, null, teamId, "GAME"));
    }

    @GetMapping("/unUploadOfGame/{teamId}/{gameId}")
    @OperationLog(name = "获取上一场未同步赛事的赛事", type = OperationLogType.INFO)
    @ApiOperation(value = "获取上一场未同步赛事的赛事", response = Game.class, notes = "")
    public ApiResult<Game> unUploadOfGame(@PathVariable("teamId") Long teamId, @PathVariable("gameId") Long gameId) {
        return ApiResult.ok(gameService.unUploadOfGame(teamId, gameId));
    }

    @GetMapping("/unUploadOfGameStu/{stuId}/{gameId}")
    @OperationLog(name = "获取上一场未同步赛事的赛事(APP端)", type = OperationLogType.INFO)
    @ApiOperation(value = "获取上一场未同步赛事的赛事（APP端）", response = Game.class, notes = "")
    public ApiResult<Game> unUploadOfGameStu(@PathVariable("stuId") Long stuId, @PathVariable("gameId") Long gameId) {
        return ApiResult.ok(gameService.unUploadOfGameUser(stuId, gameId));
    }

    @PostMapping("/addGameExtra")
    @OperationLog(name = "添加计数/记时 (修改的话传上id) 上传实际数据也用这个")
    @ApiOperation(value = "添加计数/记时（修改的话传上id）上传实际数据也用这个", response = Long.class, notes = "创建后会返回创建的id")
    public ApiResult<Object> addGameExtra(@RequestBody GameExtra gameExtra) throws Exception {
        return ApiResult.ok(gameExtraService.saveGameExtra(gameExtra));
    }

    @PostMapping("/copyGameExtra/{gameExtraId}")
    @OperationLog(name = "复制计数/记时 ")
    @ApiOperation(value = "复制计数/记时", response = ApiResult.class)
    public ApiResult<Object> copyGameExtra(@PathVariable("gameExtraId") Long gameExtraId) throws Exception {
        return ApiResult.ok(gameExtraService.copyGameExtra(gameExtraId));
    }

    @PostMapping("/deleteGameExtra/{gameExtraId}")
    @OperationLog(name = "删除计数/记时 ")
    @ApiOperation(value = "删除计数/记时", response = ApiResult.class)
    public ApiResult<Object> deleteGameExtra(@PathVariable("gameExtraId") Long gameExtraId) throws Exception {
        return ApiResult.ok(gameExtraService.removeById(gameExtraId));
    }

    @PostMapping("/getGameExtras")
    @OperationLog(name = "查看计数/记时(查看团队的数据也可以调用此接口)")
    @ApiOperation(value = "查看计数/记时", response = GameExtra.class, notes = "")
    public ApiResult<Object> getGameExtras(@RequestBody GameExtraPageParam gameExtraPageParam) throws Exception {
        return ApiResult.ok(gameExtraService.getGameExtraByGameId(gameExtraPageParam));
    }

    @GetMapping("/getGameExtrasCount/{teamId}/{gameId}")
    @OperationLog(name = "查看计数/记时/场记数量")
    @ApiOperation(value = "查看计数/记时/场记数量", response = ApiResult.class, notes = "gameId-训练id  data:{\"1\":\"计时数量\",\"2\":\"计数数量\",\"3\":\"场记数量\",\"4\":\"训练类型\",\"5\":\"已进行时间 毫秒\",\"6\":\"班级名\",\"7\":\"最新场记id 没有返回-1\"}")
    public ApiResult<Object> getGameExtrasCount(@PathVariable("teamId") Long teamId, @PathVariable("gameId") Long gameId) throws Exception {
        return ApiResult.ok(gameExtraService.getGameExtrasCount(teamId, gameId, true));
    }

    @GetMapping("/getGameExtra/{teamId}/{gameExtraId}")
    @OperationLog(name = "根据id获取计数/记时详细数据")
    @ApiOperation(value = "根据id获取计数/记时详细数据", response = GameExtra.class, notes = "")
    public ApiResult<Object> getGameExtra(@PathVariable("teamId") Long teamId, @PathVariable("gameExtraId") Long gameExtraId) throws Exception {
        return ApiResult.ok(gameExtraService.getGameExtra(teamId, gameExtraId));
    }

    @PostMapping("/getGameExtraByStu")
    @OperationLog(name = "查询学生训练的记时记数")
    @ApiOperation(value = "查询学生训练的记时记数", response = GameExtraUserAo.class, notes = "")
    public ApiResult<Object> getGameExtraByStu(@RequestBody GameExtraPageParam gameExtraPageParam) throws Exception {
        return ApiResult.ok(gameExtraService.getGameExtraByUser(gameExtraPageParam));
    }

    @GetMapping("/getGameExtraIcons")
    @OperationLog(name = "计数计时图片")
    @ApiOperation(value = "计数计时图片", response = Icon.class)
    public ApiResult<Object> getGameExtraIcons() throws Exception {
        return ApiResult.ok(iconService.list());
    }

    @PostMapping(value = "/getGrowUpData")
    @OperationLog(name = "获取成长数据")
    @ApiOperation(value = "获取成长数据", response = Object.class, notes = "返回结果 data:{games:参考分页列表字段,count:数量}")
    public ApiResult<Object> getGrowUpData(@Validated @RequestBody GamePageParam gamePageParam) {
        IPage<Game> p = gameService.getGrowUpData(gamePageParam);
        JSONObject o = new JSONObject();
        o.put("games", p.getRecords());
        o.put("count", p.getTotal());
        return ApiResult.ok(o);
    }

    @GetMapping("/unstartCount/{gameId}/{teamId}")
    @OperationLog(name = "未启动的有设备且未被覆盖的球员数量")
    @ApiOperation(value = "未启动的有设备且未被覆盖的球员数量", response = Integer.class)
    public ApiResult<Object> unstartCount(@PathVariable("gameId") Long gameId, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(gameService.unstartCount(gameId, teamId));
    }

    @PostMapping("/getStuField")
    @OperationLog(name = "个人场记列表数据")
    @ApiOperation(value = "个人场记列表数据", response = FieldData.class)
    public ApiResult<Object> getStuField(@Validated @RequestBody FieldDataPageParam fieldDataPageParam) {
        List<FieldData> fieldDataIPage = fieldDataService.getStuField(fieldDataPageParam);
        return ApiResult.ok(fieldDataIPage);
    }

    @GetMapping("/findListByIds/{stuId}/{fieldId}/{groupId}")
    @OperationLog(name = "个人场记详情数据")
    @ApiOperation(value = "个人场记详情数据", response = FieldData.class, notes = "参数 stuId:学员id  fieldId:场记id  groupId:小组id")
    public ApiResult<Object> findListByIds(@PathVariable("stuId") Long stuId, @PathVariable("fieldId") Long fieldId, @PathVariable("groupId") Long groupId) {
        FieldData fieldData = fieldDataService.findListByIdsGame(stuId, fieldId, groupId);
        return ApiResult.ok(fieldData);
    }

    @PostMapping("/getGroupField")
    @OperationLog(name = "团队场记列表")
    @ApiOperation(value = "团队场记列表", response = FieldNotes.class)
    public ApiResult<Object> getGroupField(@Validated @RequestBody FieldNotesPageParam fieldNotesPageParam) {
        List<FieldNotes> fieldNotesIPage = fieldNotesService.getGroupField(fieldNotesPageParam);
        return ApiResult.ok(fieldNotesIPage);
    }


    @GetMapping("/getStuByGroupIdAndFieldId/{groupId}/{fieldId}")
    @OperationLog(name = "查询场记学员")
    @ApiOperation(value = "查询场记学员", response = FieldData.class)
    public ApiResult<Object> getStuByGroupIdAndFieldId(@PathVariable("groupId") Long groupId, @PathVariable("fieldId") Long fieldId) {
        List<FieldData> list = fieldDataService.getStuByGroupIdAndFieldId(groupId, fieldId);
        return ApiResult.ok(list);
    }

    /**
     * 个人最新（篮球/足球）数据
     *
     * @param stuId
     * @param gameId
     * @return
     */
    @GetMapping("/getPersonal/{stuId}/{gameId}")
    @OperationLog(name = "个人（篮球/足球）数据")
    @ApiOperation(value = "个人（篮球/足球）数据", response = StuPersonDataAo.class, notes = "参数 stuId: 学生id  drillId: 训练id  篮球返回数据：\n" +
            "distance ：跑动距离 runTop：跑动距离排名  height：最大高度 heightTop：最大高度排名 score：得分  scoreTop：得分排名  assist：助攻  assistTop：助攻排名  backboard：篮板  backboardTop：篮板排名   dataMap：场记数据json \n" +
            "足球返回数据： goal：进球数  assisting：助攻数  preemption：抢断数  excitingShot：精彩射门")
    public ApiResult getPersonal(@PathVariable("stuId") Long stuId, @PathVariable("gameId") Long gameId) {
        return ApiResult.ok(fieldDataService.getPersonalGame(stuId, gameId));
    }

    /**
     * 团队(篮球/足球)数据
     *
     * @param teamId
     * @param gameId
     * @return
     */
    @GetMapping("/getTeam/{teamId}/{gameId}")
    @OperationLog(name = "团队(篮球/足球)数据")
    @ApiOperation(value = "团队(篮球/足球)数据", response = StuPersonDataAo.class, notes = "篮球数据：\n 参数 teamId: 团队id  drillId:  训练id  返回数据：\n" +
            "runList：跑步排序列表（stuId：学生id ，stuName：学生名字，image：头像，distance：跑动距离 （米）） \n " +
            "heightList：最大纵跳高度列表（stuId：学生id ，stuName：学生名字，image：头像，maxHeight：最大纵跳高度 （厘米））\n" +
            "assistList：助攻列表（stuId：学生id ，stuName：学生名字，image：头像，assist：助攻）\n" +
            "scoreList：得分列表（stuId：学生id ，stuName：学生名字，image：头像，score：得分）\n" +
            "sumSteps：总步数  sumRun：总跑动距离  sumCalorie：总热量   dataMap：场记总和 \n" +
            "sumScore：总得分  sumAssist：总助攻  sumBackboard：总篮板 \n" +
            "motionName：运动健将  motionScore：运动健将得分  motionDistance：运动健将跑动距离 （米） motionAssist：运动健将助攻次数  motionBoard：运动健将篮板次数  motionPreemption：运动健将抢断次数 \n" +
            "足球数据：\n 参数增加 sumGoal: 总进球  sumPreemption: 总抢断数  motionGoal: 运动健将进球数  motionExcitingShot: 运动健将精彩射门次数  sumExcitingShot: 总精彩射门")
    public ApiResult getTeam(@PathVariable("teamId") Long teamId, @PathVariable("gameId") Long gameId) {
        return ApiResult.ok(fieldDataService.getTeamGame(teamId, gameId));
    }

    /**
     * 团队篮球排行榜
     *
     * @param teamId
     * @param gameId
     * @param key
     * @return
     */
    @GetMapping("/teamRank/{teamId}/{gameId}/{key}")
    @OperationLog(name = "团队篮球排行榜")
    @ApiOperation(value = "团队篮球排行榜", response = StuPersonDataAo.class, notes = "参数 teamId : 班级id  date:日期 (2022-12-19)  key: 1-4(得分、跑动、助攻、最大高度)  返回列表数据参考学校篮球数据协议 \n" +
            " avgScore : 平均得分  avgDistance：平均跑动距离 m  avgAssist：平均助攻次数  avgHeight：平均高度")
    public ApiResult teamRank(@PathVariable("teamId") Long teamId, @PathVariable("gameId") Long gameId, @PathVariable("key") Integer key) {
        return ApiResult.ok(gameService.teamRankGame(teamId, gameId, key));
    }

    /**
     * 场记小组数据学员排名
     *
     * @param gameId
     * @param groupId
     * @param key
     * @return
     */
    @GetMapping("/fieldRank/{gameId}/{groupId}/{key}")
    @OperationLog(name = "场记小组数据学员排名")
    @ApiOperation(value = "场记小组数据学员排名", response = StuPersonDataAo.class, notes = "参数 gameId：比赛id  篮球key：对应存储某项数据名称 \n" +
            "（`onePointer`  '罚球（命中）', `twoPointer`  '二分（命中）',  `threePointer`  '三分（命中）',\n" +
            "  `treeBasket`  '三步跨篮次数',  `assist`  '助攻次数',  `backboard`  '篮板次数',\n" +
            "  `snatch`  '抢断次数',  `blockShot`  '盖帽次数',  `walk`  '走步次数',\n" +
            "  `pullPeople`  '拉人次数',  `walkBall` '带球走次数',  `threeViolation`  '3秒违规次数',\n" +
            "  `illegalDribble`  '非法运球次数',  `intentionalBall`  '故意脚球次数',  `hookFoul`  '勾人犯规次数',\n" +
            "  `dribbler`  '带球撞人次数',  `outBall` '球出界次数',  `illegalHands`  '非法用手次数',\n" +
            "  `headFoul`  '击头犯规次数', `flyingElbow`  '过分飞肘次数', `illegalAttack`  '非法进攻次数',\n" +
            "  `illegalDefense`  '非法防守次数', `pushPepole`  '推人次数',  `penaltyExit`  '罚满离场次数',\n" +
            "  `technicalfoul`  '技术犯规', `violationfoul` '违体犯规', `comeback`  '回场',\n" +
            "  `amazing`  '精彩过人', `nicepass`  '秒传', `frontback`  '篮板（前场）', `afterback`  '篮板（后场）'\n" +
            "  `nullone`  '罚球（不中）',  `nulltwo`  '二分（不中）',  `nullthree`  '三分（不中）',  `dunk`  '扣篮', `helpball`  '精彩救球' \n" +
            " 足球key：`goal` '进球',`orthogonality` '射正',`assisting` '助攻',\n " +
            "  `surpass` '过人',`pointsphere` '点球',`cornerkick` '角球',`freekick` '任意球',\n " +
            "  `threatball` '威胁球',`heading` '头球',`raise` '解围',`preemption` '抢断', \n " +
            "  `redcard` '红牌',`shooting` '射偏',`rules` '犯规',`offside` '越位',\n " +
            "  `puking` '冒顶',`yellowcard` '黄牌',`owngoal` '乌龙球',`waveshooting` '浪射',\n " +
            "  `kickair` '踢空',`stoperror` '停球失误',`defensiveerror` '防守失误',`passingerror` '传球失误',`wonderful` '精彩扑救' \n" +
            "  `excitingShot` '精彩射门',`excitingSyeals` '精彩抢断',`longpass` '精彩长传',`headgoal` '头球功门' \n" +
            "  `headclear` '头球解围',`clearance` '精彩解围',`rescue` '扑救',`pointMaking` '造点',`fatalerror` '致命失误' \n" +
            "  `wonderfulstop` '精彩停球',`shoot` '射门',`threatenshoot` '威胁射门',`scoring` '攻入禁区',`seriouserror` '严重失误'  \n" +
            "  返回数据：stuId:学生id  stuName：学生名字  image：学生头像  frequency：次数  wonderful：精彩扑救")
    public ApiResult fieldRank(@PathVariable("gameId") Long gameId, @PathVariable("groupId") Long groupId, @PathVariable("key") String key) {
        return ApiResult.ok(gameService.fieldRank(gameId, groupId, key));
    }

    @GetMapping("/historicBallPark/{courseId}/{search}")
    @OperationLog(name = "历史球场")
    @ApiOperation(value = "历史球场", response = BallPark.class, notes = "")
    public ApiResult historicBallPark(@PathVariable("courseId") Long courseId,@PathVariable("search")String search) {
        return ApiResult.ok(gameService.historicBallPark(courseId, getCurrentApp().getId(),search));
    }

    @PostMapping("/addBallPark")
    @OperationLog(name = "添加球场")
    @ApiOperation(value = "添加球场", response = BallPark.class, notes = "")
    public ApiResult addOrBallPark(@RequestBody BallPark ballPark) {
        return ApiResult.ok(gameService.addOrBallPark(ballPark));
    }

    @DeleteMapping("/delBallPark/{id}")
    @OperationLog(name = "删除球场")
    @ApiOperation(value = "删除球场", response = Boolean.class, notes = "")
    public ApiResult delBallPark(@PathVariable("id") Long id) throws Exception {
        return ApiResult.ok(gameService.delBallPark(id));
    }
}
