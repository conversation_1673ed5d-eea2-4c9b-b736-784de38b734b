package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.TeamPay;
import io.geekidea.springbootplus.using.mapper.TeamPayMapper;
import io.geekidea.springbootplus.using.service.TeamPayService;
import io.geekidea.springbootplus.using.param.FacilityGroupPayPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Slf4j
@Service
public class TeamPayServiceImpl extends BaseServiceImpl<TeamPayMapper, TeamPay> implements TeamPayService {

    @Autowired
    private TeamPayMapper teamPayMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveFacilityGroupPay(TeamPay teamPay) throws Exception {
        return super.save(teamPay);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateFacilityGroupPay(TeamPay teamPay) throws Exception {
        return super.updateById(teamPay);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteFacilityGroupPay(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<TeamPay> getFacilityGroupPayPageList(FacilityGroupPayPageParam facilityGroupPayPageParam) throws Exception {
        Page<TeamPay> page = new PageInfo<>(facilityGroupPayPageParam, OrderItem.desc(getLambdaColumn(TeamPay::getCreateTime)));
        LambdaQueryWrapper<TeamPay> wrapper = new LambdaQueryWrapper<>();
        IPage<TeamPay> iPage = teamPayMapper.selectPage(page, wrapper);
        return new Paging<TeamPay>(iPage);
    }

}
