package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.constant.CacheConsts;
import io.geekidea.springbootplus.framework.util.MobileUtils;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.entity.FieldData;
import io.geekidea.springbootplus.using.entity.PersonData;
import io.geekidea.springbootplus.using.entity.SchoolInfo;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.vo.*;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.SchoolInfoPageParam;
import io.geekidea.springbootplus.framework.common.controller.BaseController;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.common.param.IdParam;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.stream.Collectors;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@RestController
@RequestMapping("/using/schoolInfo")
@Module("teambox")
@Api(value = "学校信息", tags = {"学校信息"})
public class SchoolInfoController extends BaseController {

    @Autowired
    private SchoolInfoService schoolInfoService;
    @Autowired
    private PersonDataService personDataService;
    @Autowired
    private DrillService drillService;
    @Autowired
    private FieldDataService fieldDataService;

    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class)
    public ApiResult<Boolean> addSchoolInfo(@Validated(Add.class) @RequestBody SchoolInfo schoolInfo) throws Exception {
        boolean flag = schoolInfoService.saveSchoolInfo(schoolInfo);
        return ApiResult.result(flag);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改--绑定手机也用这个", response = ApiResult.class,notes = "绑定手机在用户模块 先发送短信 再验证输入的短信是否正确 正确就调用此接口修改")
    public ApiResult<Boolean> updateSchoolInfo(@Validated(Update.class) @RequestBody SchoolInfo schoolInfo) throws Exception {
        boolean flag = schoolInfoService.updateSchoolInfo(schoolInfo);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteSchoolInfo(@PathVariable("id") Long id) throws Exception {
        boolean flag = schoolInfoService.deleteSchoolInfo(id);
        return ApiResult.result(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = SchoolInfo.class)
    public ApiResult<SchoolInfo> getSchoolInfo(@PathVariable("id") Long id) throws Exception {
        SchoolInfo schoolInfo = schoolInfoService.getById(id);
        return ApiResult.ok(schoolInfo);
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = SchoolInfo.class ,notes = "返回是数组")
    public ApiResult<Object> getSchoolInfoPageList(@Validated @RequestBody SchoolInfoPageParam schoolInfoPageParam) throws Exception {
        Paging<SchoolInfo> paging = schoolInfoService.getSchoolInfoPageList(schoolInfoPageParam);
        return ApiResult.ok(paging.getRecords());
    }

    /**
     * 获取学校人员班级消息
     */
    @GetMapping("/getSchoolPersonCount/{id}")
    @OperationLog(name = "获取学校人员班级数量", type = OperationLogType.INFO)
    @ApiOperation(value = "获取学校人员班级数量", response = SchoolPersonCountAo.class ,notes = "id :学校id")
    public ApiResult getSchoolPersonCount(@PathVariable("id") Long id) {
        return ApiResult.ok(schoolInfoService.getSchoolPersonCount(id));
    }

    /**
     * 获取学校每日数据
     */
    @GetMapping("/getDailyData/{teamId}/{date}")
    @OperationLog(name = "获取学校每日数据", type = OperationLogType.INFO)
    @ApiOperation(value = "获取学校每日数据", response = DailyDataAo.class ,notes = "参数teamId :班级id date:2022-2-2 日期\n " +
            " body返回是list 总共四条 按照顺序对应 0每日一小时 1运动步数 2卡路里 3考勤")
    public ApiResult getDailyData(@PathVariable("teamId") Long teamId,@PathVariable("date") String date) {
        return ApiResult.ok(personDataService.getDailyData(teamId,date));
    }

    /**
     * 获取学校每日数据
     */
    @GetMapping("/getDailyDataSynthesizeAndRun/{teamId}/{date}")
    @OperationLog(name = "获取学校每日数据（综合|跑步）", type = OperationLogType.INFO)
    @ApiOperation(value = "获取学校每日数据（综合|跑步）", response = DailyDataSynthesizeAo.class ,notes = "参数teamId :班级id date:2022-2-2 日期\n " +
            " body字段意思可参考 查看团队数据接口")
    public ApiResult getDailyDataSynthesizeAndRun(@PathVariable("teamId") Long teamId,@PathVariable("date") String date) throws ParseException {
        return ApiResult.ok(drillService.getDailyDataSynthesizeAndRun(teamId,date));
    }

    /**
     * 获取学校每日综合数据跑动排名
     */
    @GetMapping("/getDailyDataSynthesizeMoveDistanceRanking/{teamId}/{date}/{key}/{total}")
    @OperationLog(name = "获取学校每日综合数据跑动排名", type = OperationLogType.INFO)
    @ApiOperation(value = "获取学校每日综合数据跑动排名", response = Object.class ,notes = "参数teamId :班级id date:日期 2022-2-2  total:查询的条数 -1 查询全部 \n key: " +
            "            \"wholeMoveDistance：总跑动 (m)\" +\n" +
            "            \"highMoveDistance：高速跑动 (m)\" +\n" +
            "            \"midMoveDistance：中速跑动 (m)\" +\n" +
            "            \"lowMoveDistance：低速跑动 (m)\" +\n" +
            "            \"maxSprintSpeed：最高冲刺速度 (km/h) （转换成百分比保留一位小数）\n " +
            " body字段意思可参考 跑动排名")
    public ApiResult getDailyDataSynthesizeMoveDistanceRanking(@PathVariable("teamId") Long teamId,@PathVariable("date") String date,@PathVariable("key")String rankingKey,@PathVariable("total")Integer total) {
       JSONArray jsonArray = drillService.getDailyDataSynthesizeMoveDistanceRanking(teamId,date,rankingKey);
       if(total == -1){
           return ApiResult.ok(jsonArray);
       }else {
           return ApiResult.ok(jsonArray.stream().limit(total).collect(Collectors.toList()));
       }
    }

    /**
     * 学校篮球数据
     * @param teamId
     * @param date
     * @return
     */
    @GetMapping("/getBestStu/{teamId}/{date}")
    @OperationLog(name = "学校篮球数据")
    @ApiOperation(value = "学校篮球数据",response =StuPersonDataAo.class,notes = "参数 teamId : 班级id date:日期 (2022-12-19)  数据返回：\n" +
            "runList：跑步排序列表（stuId：学生id ，stuName：学生名字，image：头像，distance：跑动距离 （米）） \n " +
            "heightList：最大纵跳高度列表（stuId：学生id ，stuName：学生名字，image：头像，maxHeight：最大纵跳高度 （厘米））\n" +
            "assistList：助攻列表（stuId：学生id ，stuName：学生名字，image：头像，assist：助攻）\n" +
            "scoreList：得分列表（stuId：学生id ，stuName：学生名字，image：头像，score：得分）\n" +
            "sumExercise：总运动时间 (分钟)  sumCalorie：总热量 （kcal） sumSteps：总步数  maxHeight：最大跳跃高度 （厘米）  avgHeight：平均跳跃高度 （厘米）  maxDistance：最大跳跃距离 （厘米）  avgDuration：平均腾空时间 （毫秒） maxDuration：最大腾空时间 （毫秒）\n " +
            "motionName：运动健将  motionScore：运动健将得分  motionDistance：运动健将跑动距离 （米） motionAssist：运动健将助攻次数  motionBoard：运动健将篮板次数  motionPreemption：运动健将抢断次数")
    public ApiResult getBestStu(@PathVariable("teamId") Long teamId,@PathVariable("date") String date){
        return ApiResult.ok(drillService.getBestStu(teamId,date));
    }



    /**
     * 学校排行榜
     * @param teamId
     * @param date
     * @return
     */
    @GetMapping("/getRank/{teamId}/{date}/{key}")
    @OperationLog(name = "学校排行榜")
    @ApiOperation(value = "学校排行榜",response =StuPersonDataAo.class,notes = "参数 teamId : 班级id  date:日期 (2022-12-19)  key: 1-4(得分、跑动、助攻、最大高度)  返回列表数据参考学校篮球数据协议 \n" +
            " avgScore : 平均得分  avgDistance：平均跑动距离 m  avgAssist：平均助攻次数  avgHeight：平均高度")
    public ApiResult getRank(@PathVariable("teamId") Long teamId,@PathVariable("date") String date,@PathVariable("key")Integer key){
        return ApiResult.ok(drillService.getRank(teamId,date,key));
    }


    /**
     * @desc: 老师获取导航文字
     * @author: DH
     * @date: 2023/6/7 10:57
     */
    @GetMapping("/getNavigationText/{id}")
    @OperationLog(name = "老师获取导航文字")
    @ApiOperation(value = "老师获取导航文字",response =JSONArray.class,notes = "id:老师id 返回参数APP导航文字自定义JSON")
    public ApiResult getNavigationText(@PathVariable("id") Long id){
        return ApiResult.ok(schoolInfoService.getNavigationText(id));
    }

    /**
     * @desc: 老师修改导航文字
     * @author: DH
     * @date: 2023/6/7 10:57
     */
    @PostMapping("/setNavigationText/{id}")
    @OperationLog(name = "老师修改导航文字")
    @ApiOperation(value = "老师修改导航文字",response =Boolean.class,notes = "id:老师id body参数 APP导航文字自定义JSON")
    public ApiResult setNavigationText(@PathVariable("id") Long id,@RequestBody JSONArray jsonArray){
        return ApiResult.ok(schoolInfoService.setNavigationText(id,jsonArray));
    }

    @GetMapping("/getSchollNameAndTeamName/{teamId}")
    @OperationLog(name = "获取学校名字和班级名字", type = OperationLogType.PAGE)
    @ApiOperation(value = "获取学校名字和班级名字", response = ApiResult.class,notes = " ")
    public ApiResult getSchollNameAndTeamName(@PathVariable("teamId")Long teamId){
        return ApiResult.ok(schoolInfoService.getSchollNameAndTeamName(teamId));
    }
}

