package io.geekidea.springbootplus.using.service.strategy.impl;

import io.geekidea.springbootplus.using.entity.Game;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.mapper.FieldNotesMapper;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.service.strategy.TaskStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service("GamePastDelayQueue")
@Slf4j
public class GamePastStrategyImpl implements TaskStrategy {
    @Autowired
    GameStudentService gameStudentService;
    @Autowired
    GameService gameService;
    @Autowired
    TransparentMessageService transparentMessageService;
    @Autowired
    FieldNotesMapper fieldNotesMapper;
    @Autowired
    DelayQueueService delayQueueService;

    @Override
    public void dispose(Object task) {
        log.info("处理训练自动过期任务id：{}", task);
        // 当前时间
        Long now = System.currentTimeMillis();
        Game game = gameService.getById(Long.parseLong(task.toString()));
        if (game!=null) {
            Long date = game.getFinishStartTime().getTime() + (game.getDuration() * (60 * 1000));
            Long time = (date - now) / 1000;
            if (time <= 0) {
                try {
                    transparentMessageService.sendTransparentByGamePast(game);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                game.setStatus(MatchStatusEnum.FINISHED);
                game.setFinishStopTime(new Date());
                gameService.saveOrUpdate(game);
                fieldNotesMapper.updateFin(game.getId(),"GAME");
                // 删除已经执行的任务
                delayQueueService.removeGameDelayQueue(game.getId());
            }
        }else{
            delayQueueService.removeGameDelayQueue(Long.parseLong(task.toString()));
        }
    }
}
