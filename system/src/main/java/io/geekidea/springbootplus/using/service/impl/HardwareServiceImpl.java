package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.mapper.HardwareMapper;
import io.geekidea.springbootplus.using.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.using.vo.BatchExchangeBindAo;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class HardwareServiceImpl extends BaseServiceImpl<HardwareMapper, Hardware> implements HardwareService {

    @Autowired
    private DrillService drillService;
    @Autowired
    private DrillStudentService drillStudentService;
    @Autowired
    private GameStudentService gameStudentService;
    @Autowired
    private MatchHardwareService matchHardwareService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private DelayQueueService delayQueueService;
    @Autowired
    private FacilityHardwareService facilityHardwareService;
    @Autowired
    private FacilityGroupService facilityGroupService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private KindergartenMonitorService kindergartenMonitorService;
    @Autowired
    private KindergartenMonitorStudentService kindergartenMonitorStudentService;
    @Autowired
    private DemoMacQrcodeService demoMacQrcodeService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveHardware(Hardware hardware) throws Exception {
        if (!demoMacQrcodeService.hardformatCheckMac(hardware.getMac())) {
            return false;
        }
        Hardware hardware1 = getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getMac, hardware.getMac()));
        if (hardware1.getMac().equals(hardware.getMac()) && hardware1.getStudentId().equals(hardware.getStudentId())) {
            hardware1.setUnbind(false);
            saveOrUpdate(hardware1);
        }
        return super.save(hardware);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateHardware(List<Hardware> hardwares) throws Exception {
        List<String> hards = new ArrayList<>();
        hardwares.forEach(hardware -> {
            if (hardware.getUnbind()) {
                //作废设备启动的所有训练
                hards.add(hardware.getMac());
            }
            super.saveOrUpdate(hardware);
        });
        drillStudentService.setDrillCancel(hards);
        return true;
    }

    @Override
    @Transactional
    public boolean updateBattery(List<Hardware> hardwaresUpdate) throws Exception {
        List<FacilityHardware> facilityHardwares = new ArrayList<>();
        List<Hardware> hardwares = new ArrayList<>();
        hardwaresUpdate.forEach(e -> {
            Hardware hardware = getById(e.getId());
            List<Hardware> hardwareList = list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getMac, hardware.getMac()));
            for (Hardware h : hardwareList) {
                h.setBattery(e.getBattery());
                h.setBatteryTime(new Date());
            }
            hardwares.addAll(hardwareList);
            FacilityHardware facilityHardware = facilityHardwareService.getOne(new LambdaQueryWrapper<FacilityHardware>().eq(FacilityHardware::getMac, hardware.getMac()));
            if (facilityHardware != null) {
                facilityHardware.setBattery(e.getBattery());
                facilityHardware.setBatteryTime(new Date());
                facilityHardwares.add(facilityHardware);
            }
        });
        facilityHardwareService.saveOrUpdateBatch(facilityHardwares);
        hardwares.forEach(e -> e.setMac(null));
        saveOrUpdateBatch(hardwares);
        return true;
    }

    @Override
    @Transactional
    public boolean updateVersion(List<Hardware> hardwaresUpdate) throws Exception {
        List<FacilityHardware> facilityHardwares = new ArrayList<>();
        List<Hardware> hardwares = new ArrayList<>();
        hardwaresUpdate.forEach(e -> {
            Hardware hardware = getById(e.getId());
            List<Hardware> hardwareList = list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getMac, hardware.getMac()));
            for (Hardware h : hardwareList) {
                h.setVersion(e.getVersion());
            }
            hardwares.addAll(hardwareList);
            FacilityHardware facilityHardware = facilityHardwareService.getOne(new LambdaQueryWrapper<FacilityHardware>().eq(FacilityHardware::getMac, hardware.getMac()));
            if (facilityHardware != null) {
                facilityHardware.setVersion(e.getVersion());
                facilityHardwares.add(facilityHardware);
            }
        });
        facilityHardwareService.saveOrUpdateBatch(facilityHardwares);
        hardwares.forEach(e -> e.setMac(null));
        saveOrUpdateBatch(hardwares);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startHardwares(List<Hardware> hardwareList, Long drillId, Long teamId) {
        Drill drill = drillService.getById(drillId);
        hardwareList = listByIds(hardwareList.stream().map(Hardware::getId).collect(Collectors.toList()));
        List<String> macs = hardwareList.stream().map(Hardware::getMac).collect(Collectors.toList());
        drillStudentService.setDrillCancel(macs);
        gameStudentService.setGameCancel(macs);
        List<DrillStudent> drillStudents = new ArrayList<>();

        List<MatchHardware> matchHardwares = matchHardwareService.getMatchHardwareByMatchId(drillId, teamId, "DRILL");
        Map<Long, MatchHardware> matchMap = matchHardwares.stream().collect(Collectors.toMap(MatchHardware::getHardwareId, Function.identity()));
        List<MatchHardware> matchHardwares1 = new ArrayList<>();
        Long now = System.currentTimeMillis();
        hardwareList.forEach(e -> {
            if (e.getFirstStartTime() == null || (e.getFirstStartTime() != null && e.getFirstStartTime().getTime() == 0l)) {
                e.setFirstStartTime(new Date());
            }
            //设置学员实际启动时间
            DrillStudent drillStudent = drillStudentService.getOneByDrillIdAndStudentId(drillId, e.getStudentId());
            if (drillStudent == null) {
                drillStudent = new DrillStudent();
                drillStudent.setDrillId(drillId);
                drillStudent.setStatus(true);
                drillStudent.setStudentId(e.getStudentId());
                drillStudent.setTeamId(teamId);
            }
            drillStudent.setStartTime(drill.getRealDataTime() != null && drill.getRealDataTime() > now ? new Date(drill.getRealDataTime()) : new Date(now));
            long stopTime = drillStudent.getStartTime().getTime() + (drill.getDuration() * (60 * 1000));
            drillStudent.setStopTime(new Date(stopTime));

            //将设备与训练关联OR  将设备设置为启动状态
            MatchHardware matchHardware = new MatchHardware();
            if (matchMap.containsKey(e.getId())) {
                matchHardware = matchMap.get(e.getId());
            }
            matchHardware.setHardwareId(e.getId());
            matchHardware.setMatchId(drillId);
            matchHardware.setMatchType("DRILL");
            matchHardware.setStarted(true);
            matchHardware.setStartTime(new Date());
            matchHardware.setApp(false);
            matchHardware.setTeamId(teamId);
            matchHardwares1.add(matchHardware);
            drillStudents.add(drillStudent);
            e.setMac(null);
        });

        matchHardwareService.saveOrUpdateBatch(matchHardwares1);
        drillStudentService.saveOrUpdateBatch(drillStudents);
        saveOrUpdateBatch(hardwareList);

        //将未启动的鞋子也和比赛关联start
        matchHardwares = matchHardwareService.getMatchHardwareByMatchId(drillId, teamId, "DRILL");
        matchHardwares1.clear();
        Map<Long, MatchHardware> finalMatchMap = matchHardwares.stream().collect(Collectors.toMap(MatchHardware::getHardwareId, Function.identity()));
        List<DrillStudent> drillStudentsNoDrill = drillStudentService.getListByDrillId(drillId, teamId);
        Iterator<DrillStudent> iterator = drillStudentsNoDrill.iterator();
        while (iterator.hasNext()) {//找出未参加比赛球员
            DrillStudent drillStudent = iterator.next();
            if (drillStudent.getStatus() != null && drillStudent.getStatus()) {
                iterator.remove();
            }
        }
        drillStudentsNoDrill.forEach(e -> {
            Hardware hardware = getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getStudentId, e.getStudentId()).eq(Hardware::getUnbind, false));
            if (hardware != null) {
                //将设备与训练关联
                if (!finalMatchMap.containsKey(hardware.getId())) {
                    MatchHardware matchHardware = new MatchHardware();
                    matchHardware.setHardwareId(hardware.getId());
                    matchHardware.setMatchId(drillId);
                    matchHardware.setMatchType("DRILL");
                    matchHardware.setStarted(false);
                    matchHardware.setApp(false);
                    matchHardware.setTeamId(teamId);
                    matchHardwares1.add(matchHardware);
                }
            }
        });
        matchHardwareService.saveOrUpdateBatch(matchHardwares1);
        //将未启动的鞋子也和比赛关联end

        //将训练状态设置为启动
        if (!drill.getStatus().equals(MatchStatusEnum.STARTED)) {
            drill.setStartTime(new Date());
            drill.setStatus(MatchStatusEnum.STARTED);
            drill.setFirstStartTime(new Date());
        }
        drill.setLastStartTime(new Date());
        delayQueueService.addDrillDelayQueue(drill); //每启动一次替换上一次的结束时间
        drillService.saveOrUpdate(drill);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startHardwaresMonitorId(List<Hardware> hardwareList, Long monitorId, Long teamId) {
        KindergartenMonitor kindergartenMonitor = kindergartenMonitorService.getById(monitorId);
        List<String> macs = hardwareList.stream().map(Hardware::getMac).collect(Collectors.toList());
        kindergartenMonitorStudentService.setMoitorCancel(macs);

        List<KindergartenMonitorStudent> kindergartenMonitorStudents = new ArrayList<>();

        List<MatchHardware> matchHardwares = matchHardwareService.getMatchHardwareByMatchId(monitorId, teamId, "MONITOR");
        Map<Long, MatchHardware> matchMap = matchHardwares.stream().collect(Collectors.toMap(MatchHardware::getHardwareId, Function.identity()));
        List<MatchHardware> matchHardwares1 = new ArrayList<>();
        hardwareList.forEach(e -> {
            if (e.getFirstStartTime() == null || (e.getFirstStartTime() != null && e.getFirstStartTime().getTime() == 0l)) {
                e.setFirstStartTime(new Date());
            }
            //设置学员实际启动时间
            KindergartenMonitorStudent drillStudent = kindergartenMonitorStudentService.getOneByMonitorIdAndStudentId(monitorId, e.getStudentId());
            if (drillStudent == null) {
                drillStudent = new KindergartenMonitorStudent();
                drillStudent.setMonitorId(monitorId);
                drillStudent.setStatus(true);
                drillStudent.setCancel(false);
                drillStudent.setStudentId(e.getStudentId());
                drillStudent.setTeamId(teamId);
            }
            //将设备与训练关联OR  将设备设置为启动状态
            MatchHardware matchHardware = new MatchHardware();
            if (matchMap.containsKey(e.getId())) {
                matchHardware = matchMap.get(e.getId());
            }else{
                matchHardware.setHardwareId(e.getId());
                matchHardware.setMatchId(monitorId);
                matchHardware.setMatchType("MONITOR");
                matchHardware.setApp(false);
                matchHardware.setTeamId(teamId);
            }
            matchHardware.setStarted(true);
            matchHardware.setStartTime(new Date());

            matchHardwares1.add(matchHardware);
            kindergartenMonitorStudents.add(drillStudent);
            e.setMac(null);
        });

        matchHardwareService.saveOrUpdateBatch(matchHardwares1);
        kindergartenMonitorStudentService.saveOrUpdateBatch(kindergartenMonitorStudents);
        saveOrUpdateBatch(hardwareList);

        //将未启动的鞋子也和比赛关联start
        matchHardwares = matchHardwareService.getMatchHardwareByMatchId(monitorId, teamId, "MONITOR");
        matchHardwares1.clear();
        Map<Long, MatchHardware> finalMatchMap = matchHardwares.stream().collect(Collectors.toMap(MatchHardware::getHardwareId, Function.identity()));

        List<KindergartenMonitorStudent> kindergartenMonitorStudentsNo = kindergartenMonitorStudentService.getList(monitorId, teamId);
        Iterator<KindergartenMonitorStudent> iterator = kindergartenMonitorStudentsNo.iterator();
        while (iterator.hasNext()) {//找出未参加比赛球员
            KindergartenMonitorStudent kindergartenMonitorStudent = iterator.next();
            if (kindergartenMonitorStudent.getStatus() != null && kindergartenMonitorStudent.getStatus()) {
                iterator.remove();
            }
        }
        kindergartenMonitorStudentsNo.forEach(e -> {
            Hardware hardware = getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getStudentId, e.getStudentId()).eq(Hardware::getUnbind, false));
            if (hardware != null) {
                //将设备与训练关联
                if (!finalMatchMap.containsKey(hardware.getId())) {
                    MatchHardware matchHardware = new MatchHardware();
                    matchHardware.setHardwareId(hardware.getId());
                    matchHardware.setMatchId(monitorId);
                    matchHardware.setMatchType("MONITOR");
                    matchHardware.setStarted(false);
                    matchHardware.setApp(false);
                    matchHardware.setTeamId(teamId);
                    matchHardwares1.add(matchHardware);
                }
            }
        });
        matchHardwareService.saveOrUpdateBatch(matchHardwares1);
        //将未启动的鞋子也和比赛关联end

        //将训练状态设置为启动
        if (!kindergartenMonitor.getStatus().equals(MatchStatusEnum.STARTED)) {
            kindergartenMonitor.setStartTime(new Date());
            kindergartenMonitor.setStatus(MatchStatusEnum.STARTED);
        }
        kindergartenMonitorService.saveOrUpdate(kindergartenMonitor);
        return true;
    }

    @Override
    public int countByTeamAndBoxNum(Long teamId, String boxNum) {
        return count(new LambdaQueryWrapper<Hardware>().eq(Hardware::getUnbind, 0).eq(Hardware::getBox, boxNum).eq(Hardware::getTeamId, teamId));
    }

    @Override
    public int getResidue(String box) {
        List<Hardware> hardwares = list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getUnbind, 0).eq(Hardware::getBox, box).groupBy(Hardware::getMac));
        return hardwares.size();
    }

    @Override
    public List<Hardware> addHardwares(List<Hardware> hardwares, Long schoolId, Long teamId) {
        for (Hardware hardware : hardwares) {
            if (!demoMacQrcodeService.hardformatCheckMac(hardware.getMac())) {
                return null;
            }
        }
        hardwares.forEach(e -> {
            FacilityHardware facilityHardware = facilityHardwareService.getOne(new LambdaQueryWrapper<FacilityHardware>().eq(FacilityHardware::getMac, e.getMac()));
            Hardware hardware = getByStuId(e.getStudentId(), e.getMac());
            if (hardware != null) {
                e.setId(hardware.getId());
                e.setUnbind(false);
            } else {
                e.setSchoollId(schoolId);
                e.setTeamId(teamId);
            }
            e.setVersion(facilityHardware.getVersion());
        });
        saveOrUpdateBatch(hardwares);
        return hardwares;
    }

    @Override
    public List<FacilityHardware> mayBindHards(Long groupId, Long teamId) {
        FacilityGroup facilityGroup = facilityGroupService.getById(groupId);
        List<FacilityHardware> facilityHardwares = facilityHardwareService.getFacilityHardwareList(groupId);
        LambdaQueryWrapper<Hardware> wrapper = new LambdaQueryWrapper<Hardware>();
        wrapper.eq(Hardware::getUnbind, 0);
        wrapper.eq(Hardware::getBox, facilityGroup.getBox());
        if (facilityGroup.getMore()) {
            wrapper.eq(Hardware::getTeamId, teamId);
        }
        List<Hardware> hardwares = list(wrapper);
        facilityHardwares = facilityHardwares.stream().filter(e -> {
            for (Hardware h : hardwares) {
                if (h.getMac().equals(e.getMac())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        return facilityHardwares.stream().sorted(Comparator.comparingInt(FacilityHardware::getNum)).collect(Collectors.toList());
    }

    @Transactional
    public Object exchangeBind(Long groupId, Long studentId, Long exchangeStuId, Integer num, Boolean ct) {
        FacilityGroup facilityGroup = facilityGroupService.getById(groupId);
        StudentInfo studentInfo = studentInfoService.getById(studentId);
        List<StudentInfo> studentInfos = new ArrayList<>();
        Hardware hard = getByStuId(studentId, null);//换绑的人的设备
        if (exchangeStuId.longValue() == -1l) { //根据num换绑
            Hardware exchangeHard = getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getSchoollId, studentInfo.getSchoollId()).eq(Hardware::getBox, facilityGroup.getBox()).eq(Hardware::getNum, num).eq(Hardware::getUnbind, 0));
            FacilityHardware facilityHardware = facilityHardwareService.getOne(new LambdaQueryWrapper<FacilityHardware>().eq(FacilityHardware::getGroupId, groupId).eq(FacilityHardware::getNum, num));
            if (!ct && !facilityGroup.getMore()) { //拆分模式判断要换绑的设备 是否被其他班级绑定
                if (getOne(new LambdaQueryWrapper<Hardware>()
                        .eq(Hardware::getBox, facilityHardware.getBox())
                        .eq(Hardware::getNum, facilityHardware.getNum())
                        .eq(Hardware::getUnbind, false)
                        .eq(Hardware::getSchoollId, studentInfo.getSchoollId())
                        .ne(Hardware::getTeamId, studentInfo.getTeamId())) != null)
                    return null;
            }

            if (hard == null && exchangeHard == null) {//换绑的人没有设备和被换绑的设备没人绑定的情况
                Hardware hard1 = getByStuId(studentId, facilityHardware.getMac());
                if (hard1 != null && hard1.getMac().equals(facilityHardware.getMac())) {//判断是否之前绑定过此设备
                    hard1.setUnbind(false);
                } else {
                    hard1 = new Hardware().setSchoollId(studentInfo.getSchoollId()).setTeamId(studentInfo.getTeamId()).setStudentId(studentId).setMac(facilityHardware.getMac()).setBox(facilityHardware.getBox()).setNum(facilityHardware.getNum()).setVersion(facilityHardware.getVersion()).setBattery(facilityHardware.getBattery()).setBatteryTime(facilityHardware.getBatteryTime());
                }
                saveOrUpdate(hard1);
            }

            if (hard == null && exchangeHard != null) {//换绑人没有设备 但是被换绑设备被人绑定
                exchangeHard.setUnbind(true); //被换绑的用户设备解绑
                saveOrUpdate(exchangeHard);

                Hardware hard1 = getByStuId(studentId, exchangeHard.getMac());
                if (hard1 != null && hard1.getMac().equals(hard.getMac())) {//判断是否之前绑定过此设备
                    hard1.setUnbind(false);
                } else {
                    hard1 = new Hardware().setSchoollId(hard.getSchoollId()).setTeamId(hard.getTeamId()).setStudentId(studentId).setMac(hard.getMac()).setBox(hard.getBox()).setNum(hard.getNum()).setVersion(hard.getVersion()).setBattery(hard.getBattery()).setBatteryTime(hard.getBatteryTime());
                }
                saveOrUpdate(hard1);
            }

            if (hard != null && exchangeHard == null) {//换绑人有设备 但是被换绑设备没人绑定
                hard.setUnbind(true);
                saveOrUpdate(hard);

                Hardware hard1 = getByStuId(studentId, facilityHardware.getMac());
                if (hard1 != null && hard1.getMac().equals(facilityHardware.getMac())) {//判断是否之前绑定过此设备
                    hard1.setUnbind(false);
                } else {
                    hard1 = new Hardware().setSchoollId(hard.getSchoollId()).setTeamId(hard.getTeamId()).setStudentId(studentId).setMac(facilityHardware.getMac()).setBox(facilityHardware.getBox()).setNum(facilityHardware.getNum()).setVersion(facilityHardware.getVersion()).setBattery(facilityHardware.getBattery()).setBatteryTime(facilityHardware.getBatteryTime());
                }
                saveOrUpdate(hard1);
            }

            if (hard != null && exchangeHard != null) {//正常交换情况
                exchangeBindSon(hard, exchangeHard);
            }
            studentInfo.setHardware(getByStuId(studentId, null));
            studentInfos.add(studentInfo);
        } else { //根据学生id换绑
            Hardware exchangeHard = getByStuId(exchangeStuId, null);
            StudentInfo exchangeStu = studentInfoService.getById(exchangeStuId);
            if (exchangeHard == null) { //换绑人没有设备情况
                hard.setUnbind(true);
                saveOrUpdate(hard);

                Hardware hard1 = getByStuId(exchangeStuId, hard.getMac());
                if (hard1 != null && hard1.getMac().equals(hard.getMac())) {//判断是否之前绑定过此设备
                    hard1.setUnbind(false);
                    saveOrUpdate(hard1);
                } else {
                    exchangeHard = new Hardware().setSchoollId(exchangeStu.getSchoollId())
                            .setTeamId(exchangeStu.getTeamId()).setStudentId(exchangeStu.getId())
                            .setMac(hard.getMac()).setBox(hard.getBox()).setNum(hard.getNum())
                            .setVersion(hard.getVersion()).setBattery(hard.getBattery()).setBatteryTime(hard.getBatteryTime());
                    saveOrUpdate(exchangeHard);
                }
            } else {
                //正常交换
                exchangeBindSon(hard, exchangeHard);
            }

            exchangeStu.setHardware(getByStuId(exchangeStuId, null));
            studentInfo.setHardware(getByStuId(hard.getStudentId(), null));
            studentInfos.add(studentInfo);
            studentInfos.add(exchangeStu);
        }
        return studentInfos;
    }

    @Override
    @Transactional
    public Object batchExchangeBind(List<BatchExchangeBindAo> batchExchangeBindAos, List<StudentInfo> studentInfos) {
        for (BatchExchangeBindAo e : batchExchangeBindAos) {
            FacilityGroup facilityGroup = facilityGroupService.getById(e.getGroupId());
            StudentInfo studentInfo = studentInfoService.getById(e.getStudentId());
            Hardware hard = getByStuId(e.getStudentId(), null);//换绑的人的设备
            if (e.getExchangeStuId().longValue() == -1l) { //根据num换绑
                Hardware exchangeHard = getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getSchoollId, studentInfo.getSchoollId()).eq(Hardware::getBox, facilityGroup.getBox()).eq(Hardware::getNum, e.getExchangeNum()).eq(Hardware::getUnbind, 0));
                FacilityHardware facilityHardware = facilityHardwareService.getOne(new LambdaQueryWrapper<FacilityHardware>().eq(FacilityHardware::getGroupId, e.getGroupId()).eq(FacilityHardware::getNum, e.getExchangeNum()));
                if (!e.getCt() && !facilityGroup.getMore()) { //拆分模式判断要换绑的设备 是否被其他班级绑定
                    Hardware HARDWAREGROUPBIND = getOne(new LambdaQueryWrapper<Hardware>()
                            .eq(Hardware::getBox, facilityHardware.getBox())
                            .eq(Hardware::getNum, facilityHardware.getNum())
                            .eq(Hardware::getUnbind, false)
                            .eq(Hardware::getSchoollId, studentInfo.getSchoollId())
                            .ne(Hardware::getTeamId, studentInfo.getTeamId()));
                    if (HARDWAREGROUPBIND != null)
                        return HARDWAREGROUPBIND;
                }

                if (hard == null && exchangeHard == null) {//换绑的人没有设备和被换绑的设备没人绑定的情况
                    Hardware hard1 = getByStuId(e.getStudentId(), facilityHardware.getMac());
                    if (hard1 != null && hard1.getMac().equals(facilityHardware.getMac())) {//判断是否之前绑定过此设备
                        hard1.setUnbind(false);
                    } else {
                        hard1 = new Hardware().setSchoollId(studentInfo.getSchoollId()).setTeamId(studentInfo.getTeamId()).setStudentId(e.getStudentId()).setMac(facilityHardware.getMac()).setBox(facilityHardware.getBox()).setNum(facilityHardware.getNum()).setVersion(facilityHardware.getVersion()).setBattery(facilityHardware.getBattery()).setBatteryTime(facilityHardware.getBatteryTime());
                    }
                    saveOrUpdate(hard1);
                }

                if (hard == null && exchangeHard != null) {//换绑人没有设备 但是被换绑设备被人绑定
                    exchangeHard.setUnbind(true); //被换绑的用户设备解绑
                    saveOrUpdate(exchangeHard);

                    Hardware hard1 = getByStuId(e.getStudentId(), exchangeHard.getMac());
                    if (hard1 != null && hard1.getMac().equals(hard.getMac())) {//判断是否之前绑定过此设备
                        hard1.setUnbind(false);
                    } else {
                        hard1 = new Hardware().setSchoollId(hard.getSchoollId()).setTeamId(hard.getTeamId()).setStudentId(e.getStudentId()).setMac(hard.getMac()).setBox(hard.getBox()).setNum(hard.getNum()).setVersion(hard.getVersion()).setBattery(hard.getBattery()).setBatteryTime(hard.getBatteryTime());
                    }
                    saveOrUpdate(hard1);
                }

                if (hard != null && exchangeHard == null) {//换绑人有设备 但是被换绑设备没人绑定
                    hard.setUnbind(true);
                    saveOrUpdate(hard);

                    Hardware hard1 = getByStuId(e.getStudentId(), facilityHardware.getMac());
                    if (hard1 != null && hard1.getMac().equals(facilityHardware.getMac())) {//判断是否之前绑定过此设备
                        hard1.setUnbind(false);
                    } else {
                        hard1 = new Hardware().setSchoollId(hard.getSchoollId()).setTeamId(hard.getTeamId()).setStudentId(e.getStudentId()).setMac(facilityHardware.getMac()).setBox(facilityHardware.getBox()).setNum(facilityHardware.getNum()).setVersion(facilityHardware.getVersion()).setBattery(facilityHardware.getBattery()).setBatteryTime(facilityHardware.getBatteryTime());
                    }
                    saveOrUpdate(hard1);
                }

                if (hard != null && exchangeHard != null) {//正常交换情况
                    exchangeBindSon(hard, exchangeHard);
                }
                studentInfo.setHardware(getByStuId(studentInfo.getId(), null));
                studentInfos.add(studentInfo);
            } else { //根据学生id换绑
                Hardware exchangeHard = getByStuId(e.getExchangeStuId(), null);
                StudentInfo exchangeStu = studentInfoService.getById(e.getExchangeStuId());
                if (exchangeHard == null) { //换绑人没有设备情况
                    hard.setUnbind(true);
                    saveOrUpdate(hard);

                    Hardware hard1 = getByStuId(e.getExchangeStuId(), hard.getMac());
                    if (hard1 != null && hard1.getMac().equals(hard.getMac())) {//判断是否之前绑定过此设备
                        hard1.setUnbind(false);
                        saveOrUpdate(hard1);
                    } else {
                        exchangeHard = new Hardware().setSchoollId(exchangeStu.getSchoollId())
                                .setTeamId(exchangeStu.getTeamId()).setStudentId(exchangeStu.getId())
                                .setMac(hard.getMac()).setBox(hard.getBox()).setNum(hard.getNum())
                                .setVersion(hard.getVersion()).setBattery(hard.getBattery()).setBatteryTime(hard.getBatteryTime());
                        saveOrUpdate(exchangeHard);
                    }
                } else {
                    //正常交换
                    exchangeBindSon(hard, exchangeHard);
                }
                exchangeStu.setHardware(getByStuId(exchangeHard.getStudentId(), null));
                studentInfo.setHardware(getByStuId(hard.getStudentId(), null));
                studentInfos.add(studentInfo);
                studentInfos.add(exchangeStu);
            }
        }
        return null;
    }


    //exchangeBind子方法
    public void exchangeBindSon(Hardware hard, Hardware exchangeHard) { //首先全部解绑 再重新添加
        if (hard.getMac().equals(exchangeHard.getMac())) {//自己跟自己换 测试的时候出现
            return;
        }

        hard.setUnbind(true);
        exchangeHard.setUnbind(true);
        saveOrUpdate(hard);
        saveOrUpdate(exchangeHard);

        Hardware hard1 = getByStuId(hard.getStudentId(), exchangeHard.getMac());
        if (hard1 != null && hard1.getMac().equals(exchangeHard.getMac())) {//判断是否之前绑定过此设备
            hard1.setUnbind(false);
            saveOrUpdate(hard1);
        } else {
            Hardware hardA = new Hardware().setSchoollId(hard.getSchoollId()).setTeamId(hard.getTeamId()).setStudentId(hard.getStudentId()).setMac(exchangeHard.getMac()).setBox(exchangeHard.getBox()).setNum(exchangeHard.getNum()).setVersion(exchangeHard.getVersion()).setBattery(exchangeHard.getBattery()).setBatteryTime(exchangeHard.getBatteryTime());
            saveOrUpdate(hardA);
        }

        Hardware exchangeHard1 = getByStuId(exchangeHard.getStudentId(), hard.getMac());
        if (exchangeHard1 != null && exchangeHard1.getMac().equals(hard.getMac())) {//判断是否之前绑定过此设备
            exchangeHard1.setUnbind(false);
            saveOrUpdate(exchangeHard1);
        } else {
            Hardware hardB = new Hardware().setSchoollId(exchangeHard.getSchoollId()).setTeamId(exchangeHard.getTeamId()).setStudentId(exchangeHard.getStudentId()).setMac(hard.getMac()).setBox(hard.getBox()).setNum(hard.getNum()).setVersion(hard.getVersion()).setBattery(hard.getBattery()).setBatteryTime(hard.getBatteryTime());
            saveOrUpdate(hardB);
        }
    }

    @Override
    public JSONObject getPrintStuHard(Long teamId) {
        Team team = teamService.getById(teamId);
        JSONObject object = new JSONObject();
        JSONArray array = new JSONArray();
        List<Hardware> hardwares = list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getTeamId, teamId).eq(Hardware::getUnbind, 0));
        hardwares.forEach(e -> {
            JSONObject o = new JSONObject();
            StudentInfo studentInfo = studentInfoService.getById(e.getStudentId());
            o.put("name", studentInfo.getName());
            o.put("num", e.getNum());
            array.add(o);
        });
        object.put("teamName", team.getFullName());
        object.put("stuHard", array);
        return object;
    }

    @Override
    public Hardware getByStuId(Long stuId, String mac) {
        return getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getStudentId, stuId).eq(mac != null, Hardware::getMac, mac).eq(mac != null, Hardware::getUnbind, true).eq(mac == null, Hardware::getUnbind, false));//
    }

    @Override
    public void test() {
        List<DemoMacQrcode> demoMacQrcodes = demoMacQrcodeService.list();
        for (DemoMacQrcode demoMacQrcode:demoMacQrcodes) {
            update(new LambdaUpdateWrapper<Hardware>().eq(Hardware::getMac,demoMacQrcode.getMac()).set(Hardware::getLongNum,demoMacQrcode.getLongNum()));
        }
    }

}
