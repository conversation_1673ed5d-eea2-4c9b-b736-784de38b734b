package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.TacticsBoardTool;
import io.geekidea.springbootplus.using.mapper.TacticsBoardToolMapper;
import io.geekidea.springbootplus.using.service.TacticsBoardToolService;
import io.geekidea.springbootplus.using.param.TacticsBoardToolPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 战术板工具表，存储各种工具信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@Service
public class TacticsBoardToolServiceImpl extends BaseServiceImpl<TacticsBoardToolMapper, TacticsBoardTool> implements TacticsBoardToolService {

    @Autowired
    private TacticsBoardToolMapper tacticsBoardToolMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveTacticsBoardTool(TacticsBoardTool tacticsBoardTool) throws Exception {
        return super.save(tacticsBoardTool);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTacticsBoardTool(TacticsBoardTool tacticsBoardTool) throws Exception {
        return super.updateById(tacticsBoardTool);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTacticsBoardTool(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<TacticsBoardTool> getTacticsBoardToolPageList(TacticsBoardToolPageParam tacticsBoardToolPageParam) throws Exception {
        Page<TacticsBoardTool> page = new PageInfo<>(tacticsBoardToolPageParam);
        LambdaQueryWrapper<TacticsBoardTool> wrapper = new LambdaQueryWrapper<>();
        IPage<TacticsBoardTool> iPage = tacticsBoardToolMapper.selectPage(page, wrapper);
        return new Paging<TacticsBoardTool>(iPage);
    }

}
