package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 *  分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-07-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FieldNotesPageParam分页参数")
public class FieldNotesPageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("训练or比赛id")
    private Long matchId;

    @ApiModelProperty("GAME||DRILL")
    private String matchType;

    @ApiModelProperty("学生id 查询个人需要传")
    private Long studentId;
}
