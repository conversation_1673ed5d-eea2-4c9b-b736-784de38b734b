package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 *  分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2023-11-04
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "KindergartenMonitorPageParam")
public class KindergartenMonitorPageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("学校id")
    private Long schoolId;

    @ApiModelProperty("学生id")
    private Long studentId;

    @ApiModelProperty("是否完成监测 查询全部就不传")
    private Boolean finish;

    @ApiModelProperty("是否上传数据 查询全部就不传")
    private Boolean upload;
}
