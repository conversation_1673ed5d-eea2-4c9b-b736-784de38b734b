package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.TacticsBoardText;
import io.geekidea.springbootplus.using.mapper.TacticsBoardTextMapper;
import io.geekidea.springbootplus.using.service.TacticsBoardTextService;
import io.geekidea.springbootplus.using.param.TacticsBoardTextPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 战术板文本表，存储文本信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@Service
public class TacticsBoardTextServiceImpl extends BaseServiceImpl<TacticsBoardTextMapper, TacticsBoardText> implements TacticsBoardTextService {

    @Autowired
    private TacticsBoardTextMapper tacticsBoardTextMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveTacticsBoardText(TacticsBoardText tacticsBoardText) throws Exception {
        return super.save(tacticsBoardText);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTacticsBoardText(TacticsBoardText tacticsBoardText) throws Exception {
        return super.updateById(tacticsBoardText);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTacticsBoardText(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<TacticsBoardText> getTacticsBoardTextPageList(TacticsBoardTextPageParam tacticsBoardTextPageParam) throws Exception {
        Page<TacticsBoardText> page = new PageInfo<>(tacticsBoardTextPageParam);
        LambdaQueryWrapper<TacticsBoardText> wrapper = new LambdaQueryWrapper<>();
        IPage<TacticsBoardText> iPage = tacticsBoardTextMapper.selectPage(page, wrapper);
        return new Paging<TacticsBoardText>(iPage);
    }

}
