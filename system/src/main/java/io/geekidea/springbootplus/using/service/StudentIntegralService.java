package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.StudentIntegral;
import io.geekidea.springbootplus.using.param.StudentIntegralPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2024-06-29
 */
public interface StudentIntegralService extends BaseService<StudentIntegral> {

    void computeIntegralAll();

    void computeIntegralPerson();

    JSONObject getIntegralLevel(Long studentId);

}
