package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import com.alibaba.fastjson.JSONObject;

/**
 * 战术板工具表，存储各种工具信息
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TacticsBoardTool对象")
@TableName(autoResultMap = true)
public class TacticsBoardTool extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("工具ID，唯一标识")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @NotNull(message = "关联的战术板ID不能为空")
    @ApiModelProperty("关联的战术板ID")
    private Long boardId;

    @NotBlank(message = "工具名称不能为空")
    @ApiModelProperty("工具名称")
    private String name;

    @ApiModelProperty("缩放比例，百分比表示")
    private Integer scale;

    @ApiModelProperty("旋转角度，0-360度")
    private Integer angle;

    @ApiModelProperty("位置 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject position;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    @ApiModelProperty("异动轨迹对象         private boolean noPathAble;//默认false (可以产生轨迹)   ture : 禁止产生轨迹\n" +
            "        private Point start;//移动轨迹起点\n" +
            "        private Point control;//控制点（如果移动轨迹是曲线时）\n" +
            "        private Point end;//移动轨迹终点")
    private JSONArray movePathList;

    @ApiModelProperty("是否禁止移动：0-可移动，1-不可移动")
    private Boolean noMove;

    @ApiModelProperty("工具图标图片URL")
    private String url;

    @ApiModelProperty("工具类型：0-球，1-网，2-设备")
    private Integer type;

    /**
     * 自定义setter方法，对movePathList进行数据预处理
     * 补充缺失的字段为null，确保数据结构完整性
     */
    public void setMovePathList(JSONArray movePathList) {
        if (movePathList == null) {
            this.movePathList = null;
            return;
        }

        // 创建新的JSONArray来存储标准化后的数据
        JSONArray standardizedArray = new JSONArray();

        for (int i = 0; i < movePathList.size(); i++) {
            Object item = movePathList.get(i);
            if (item instanceof JSONObject) {
                JSONObject pathObj = (JSONObject) item;
                JSONObject standardizedObj = new JSONObject();

                // 设置noPathAble字段，默认为false
                standardizedObj.put("noPathAble", pathObj.getBoolean("noPathAble") != null ?
                    pathObj.getBoolean("noPathAble") : false);

                // 设置start字段，如果不存在则为null
                standardizedObj.put("start", pathObj.get("start"));

                // 设置control字段，如果不存在则为null
                standardizedObj.put("control", pathObj.get("control"));

                // 设置end字段，如果不存在则为null
                standardizedObj.put("end", pathObj.get("end"));

                standardizedArray.add(standardizedObj);
            } else {
                // 如果不是JSONObject，直接添加原始数据
                standardizedArray.add(item);
            }
        }

        this.movePathList = standardizedArray;
    }

}
