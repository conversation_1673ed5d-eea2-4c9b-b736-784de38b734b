package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.AppTeamUser;
import io.geekidea.springbootplus.using.param.TeamStudentPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface AppTeamUserService extends BaseService<AppTeamUser> {

    /**
     * 保存
     *
     * @param appTeamUser
     * @return
     * @throws Exception
     */
    boolean saveTeamStudent(AppTeamUser appTeamUser) throws Exception;

    /**
     * 修改
     *
     * @param appTeamUser
     * @return
     * @throws Exception
     */
    boolean updateTeamStudent(AppTeamUser appTeamUser) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTeamStudent(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param teamStudentQueryParam
     * @return
     * @throws Exception
     */
    Paging<AppTeamUser> getTeamStudentPageList(TeamStudentPageParam teamStudentPageParam) throws Exception;

}
