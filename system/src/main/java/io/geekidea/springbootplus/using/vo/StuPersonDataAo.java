package io.geekidea.springbootplus.using.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.geekidea.springbootplus.using.entity.PersonData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.checkerframework.checker.units.qual.A;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "StuPersonDataAo")
public class StuPersonDataAo {
    //主键id
    private Long id;
    @ApiModelProperty("学生id")
    private Long stuId;
    @ApiModelProperty("学生名字")
    private String stuName;
    @ApiModelProperty("学生头像")
    private String image;
    @ApiModelProperty("得分")
    private Integer score;
    @ApiModelProperty("得分排名")
    private Integer scoreTop;
    @ApiModelProperty("跑动距离")
    private Integer distance;
    @ApiModelProperty("跑动距离排名")
    private Integer runTop;
    @ApiModelProperty("助攻")
    private Integer assist;
    @ApiModelProperty("助攻排名")
    private Integer assistTop;
    @ApiModelProperty("最大跳跃高度")
    private Integer maxHeight;
    @ApiModelProperty("最大高度排名")
    private Integer heightTop;
    @ApiModelProperty("总步数")
    private Integer sumSteps;
    @ApiModelProperty("总跑动距离")
    private Integer sumRun;
    @ApiModelProperty("总热量")
    private Integer sumCalorie;
    @ApiModelProperty("总运动时间")
    private Integer sumExercise;
    @ApiModelProperty("总人数")
    private Integer stuCount;
    @ApiModelProperty("平均跳跃高度")
    private Integer avgHeight;
    @ApiModelProperty("最大跳跃距离")
    private Integer maxDistance;
    @ApiModelProperty("平均腾空时间")
    private Integer avgDuration;
    @ApiModelProperty("最大腾空时间")
    private Integer maxDuration;
    @ApiModelProperty("运动日期")
    private String date;
    @ApiModelProperty("场记数据 篮球数据json 格式: {\"walk\": 0, \"assist\": 0, \"snatch\": 0, \"outBall\": 0, \"dribbler\": 0, \"headFoul\": 0, \"hookFoul\": 0, \"walkBall\": 0, \"backboard\": 0, \"blockShot\": 0, \"onePointer\": 0, \"pullPeople\": 0, \"pushPepole\": 0, \"treeBasket\": 0, \"twoPointer\": 0, \"flyingElbow\": 0, \"penaltyExit\": 0, \"illegalHands\": 0, \"threePointer\": 0, \"illegalAttack\": 0, \"illegalDefense\": 0, \"illegalDribble\": 0, \"threeViolation\": 0, \"intentionalBall\": 0, \"technicalfoul\": 0, \"violationfoul\": 0, \"comeback\": 0,\"amazing\": 0, \"nicepass\":0, \"frontback\":0, \"afterback\":0, \"nullone\":0, \"nulltwo\":0, \"nullthree\":0, \"dunk\":0, \"helpball\":0} \n" +
                                "足球数据json格式: {\"goal\":0,\"orthogonality\":0,\"assisting\":0,\"surpass\":0,\"pointsphere\":0,\"cornerkick\":0,\"freekick\":0,\"threatball\":0,\"heading\":0,\"raise\":0,\"preemption\":0,\"redcard\":0,\"shooting\":0,\"rules\":0,\"offside\":0,\"puking\":0,\"yellowcard\":0,\"owngoal\":0,\"waveshooting\":0,\"kickair\":0,\"stoperror\":0,\"defensiveerror\":0,\"passingerror\":0,\"wonderful\":0,\"excitingShot\":0,\"excitingSyeals\":0,\"longpass\":0,\"headgoal\":0,\"headclear\":0,\"clearance\":0,\"rescue\":0,\"pointMaking\":0,\"fatalerror\":0,\"wonderfulstop\":0,\"shoot\":0,\"threatenshoot\":0,\"scoring\":0,\"seriouserror\":0  }")
    private JSONObject dataMap;
    @ApiModelProperty("学生列表")
    private List<PersonData> stuList;
    @ApiModelProperty("运动健将名字")
    private String motionName;
    @ApiModelProperty("运动健将得分")
    private Integer motionScore;
    @ApiModelProperty("运动健将跑动距离")
    private Integer motionDistance;
    @ApiModelProperty("运动健将进球")
    private Integer motionGoal;
    @ApiModelProperty("运动健将助攻")
    private Integer motionAssist;
    @ApiModelProperty("运动健将抢断")
    private Integer motionPreemption;
    @ApiModelProperty("次数")
    private Integer frequency;
    @ApiModelProperty("场记id")
    private Long fieldId;
    @ApiModelProperty("篮球球衣号")
    private Integer shirt;
    @ApiModelProperty("足球球衣号")
    private Integer footballShirt;
}
