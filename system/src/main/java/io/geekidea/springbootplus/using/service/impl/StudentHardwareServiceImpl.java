package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.StudentHardware;
import io.geekidea.springbootplus.using.mapper.StudentHardwareMapper;
import io.geekidea.springbootplus.using.service.StudentHardwareService;
import io.geekidea.springbootplus.using.param.StudentHardwarePageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class StudentHardwareServiceImpl extends BaseServiceImpl<StudentHardwareMapper, StudentHardware> implements StudentHardwareService {

    @Autowired
    private StudentHardwareMapper studentHardwareMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveStudentHardware(StudentHardware studentHardware) throws Exception {
        return super.save(studentHardware);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateStudentHardware(StudentHardware studentHardware) throws Exception {
        return super.updateById(studentHardware);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteStudentHardware(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<StudentHardware> getStudentHardwarePageList(StudentHardwarePageParam studentHardwarePageParam) throws Exception {
        Page<StudentHardware> page = new PageInfo<>(studentHardwarePageParam, OrderItem.desc(getLambdaColumn(StudentHardware::getCreateTime)));
        LambdaQueryWrapper<StudentHardware> wrapper = new LambdaQueryWrapper<>();
        IPage<StudentHardware> iPage = studentHardwareMapper.selectPage(page, wrapper);
        return new Paging<StudentHardware>(iPage);
    }

}
