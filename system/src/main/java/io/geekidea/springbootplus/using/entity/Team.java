package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.geekidea.springbootplus.using.vo.ExerciseConditionAo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Team对象")
@TableName(autoResultMap = true)
public class Team extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("学校id")
    private Long schoolId;

    @ApiModelProperty("老师id")
    private Long teacherId;

    @ApiModelProperty("班级logo")
    private String logo;

    @ApiModelProperty("班级全称")
    private String fullName;

    @ApiModelProperty("班级简称")
    private String shortName;

    @ApiModelProperty("成立时间")
    private Date formedTime;

    @ApiModelProperty("属性")
    private String property;

    @ApiModelProperty("联系人")
    private String contacts;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("简介")
    private String introduction;

    @ApiModelProperty("人数")
    @TableField(exist = false)
    private int peopleNum;

    @ApiModelProperty("平均年龄")
    @TableField(exist = false)
    private int avgAge;

    @ApiModelProperty("2篮球 3足球 4数字体育课")
    private Long courseId;

    @ApiModelProperty("赛事等级1-20")
    private Integer grade;

    @ApiModelProperty("{grade:17,status:0：审核中 1：审核通过 2：不通过} 赛事等级审核状态")
    @TableField(exist = false)
    private JSONObject gradeAudit;

    @ApiModelProperty("训练场次 ")
    @TableField(exist = false)
    private int drillCount;

    @ApiModelProperty("监测场次 ")
    @TableField(exist = false)
    private int monitorCount;

    @ApiModelProperty("区域 {\"country\":\"国家代码\",\"province\":\"省代码\",\"city\":\"市代码\",\"county\":\"区代码\"}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject region;

    @ApiModelProperty("球场")
    private String court;

    @ApiModelProperty("保护套颜色")
    private String caseColor;

    @ApiModelProperty("是否体育监测模式")
    private Boolean sportsMonitor;

    @ApiModelProperty("班级所在设备组的id，没有就发-1")
    @TableField(exist = false)
    private Long groupId;

    @ApiModelProperty("班级所在设备组的名字，没有就发null")
    @TableField(exist = false)
    private String groupName;

    @ApiModelProperty("班级到期时间时间戳 如果是0代表永久")
    private Long restrictDate;

    @ApiModelProperty("是否过期")
    @TableField(exist = false)
    private Boolean pastDue = false;

    @ApiModelProperty("是否绑定了设备组")
    @TableField(exist = false)
    private Boolean bindFacilityGroup;

    @ApiModelProperty("最近（有数据）的体育监测 时间戳 或 今天体育监测时间戳（即创建过的、不用管是否有上传数据）(优先返回 今天体育监测时间戳), 没有给-1")
    @TableField(exist = false)
    private Long recentMonitorData = -1l;

    public void setRecentMonitorData(Date recentData) {
        if (recentData != null) {
            this.recentMonitorData = recentData.getTime();
        } else {
            this.recentMonitorData = -1l;
        }
    }

    @ApiModelProperty("班级中 是否有进行中状态的训练")
    @TableField(exist = false)
    private Boolean startedCount;

    @ApiModelProperty("班级中 是否有未同步的训练 只算一个球员都没有同步的训练")
    @TableField(exist = false)
    private Boolean unUploadCount;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Boolean deleted;

    @ApiModelProperty(" 设置为体育监测班级的时间")
    private Date sportsMonitorTime;

    @ApiModelProperty("监测排序号")
    private Integer monitorSort;

    public void setRecentData(Date date) {
        if (date!=null) {
            this.recentData = date.getTime();
        }else{
            this.recentData = -1l;
        }
    }

    @ApiModelProperty("最近训练有数据的一天时间戳，没有数据就返回-1")
    @TableField(exist = false)
    private Long recentData;

    @ApiModelProperty("学生信息")
    @TableField(exist = false)
    List<ExerciseConditionAo> exerciseConditionAos;


    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
