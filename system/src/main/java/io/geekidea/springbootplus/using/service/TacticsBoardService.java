package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TacticsBoard;
import io.geekidea.springbootplus.using.entity.User;
import io.geekidea.springbootplus.using.param.TacticsBoardPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 * 战术板主表，存储战术板基本信息 服务类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface TacticsBoardService extends BaseService<TacticsBoard> {

    /**
     * 保存
     *
     * @param tacticsBoard
     * @param user
     * @return 保存后的战术板ID
     * @throws Exception
     */
    Long saveTacticsBoard(TacticsBoard tacticsBoard) throws Exception;

    /**
     * 修改
     *
     * @param tacticsBoard
     * @return
     * @throws Exception
     */
    boolean updateTacticsBoard(TacticsBoard tacticsBoard) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTacticsBoard(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param tacticsBoardQueryParam
     * @return
     * @throws Exception
     */
    Paging<TacticsBoard> getTacticsBoardPageList(TacticsBoardPageParam tacticsBoardPageParam) throws Exception;

    /**
     * 根据ID获取战术板详情，包含关联数据
     *
     * @param id 战术板ID
     * @return 战术板详情
     * @throws Exception
     */
    TacticsBoard getTacticsBoardById(Long id) throws Exception;

}
