package io.geekidea.springbootplus.using.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.using.entity.AppMessage;
import io.geekidea.springbootplus.using.entity.TeamJoinApplication;
import io.geekidea.springbootplus.using.entity.TeamApp;
import io.geekidea.springbootplus.using.entity.TeamUserApp;
import io.geekidea.springbootplus.using.mapper.TeamJoinApplicationMapper;
import io.geekidea.springbootplus.using.service.TeamJoinApplicationService;
import io.geekidea.springbootplus.using.service.TeamAppService;
import io.geekidea.springbootplus.using.service.TeamUserAppService;
import io.geekidea.springbootplus.using.service.AppMessageService;
import io.geekidea.springbootplus.using.service.TransparentMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 球队入队申请表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Service
public class TeamJoinApplicationServiceImpl extends BaseServiceImpl<TeamJoinApplicationMapper, TeamJoinApplication> implements TeamJoinApplicationService {

    @Autowired
    private TeamAppService teamAppService;

    @Autowired
    private TeamUserAppService teamUserAppService;

    @Autowired
    private AppMessageService appMessageService;

    @Autowired
    private TransparentMessageService transparentMessageService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long applyJoinTeam(Long teamId, Long applicantUserId, String reason) throws Exception {
        // 检查球队是否存在
        TeamApp team = teamAppService.getById(teamId);
        if (team == null) {
            throw new Exception("球队不存在");
        }

        // 检查用户是否已经是球队成员
        LambdaQueryWrapper<TeamUserApp> memberWrapper = new LambdaQueryWrapper<>();
        memberWrapper.eq(TeamUserApp::getTeamId, teamId)
                    .eq(TeamUserApp::getUserId, applicantUserId);
        TeamUserApp existingMember = teamUserAppService.getOne(memberWrapper);
        if (existingMember != null) {
            throw new Exception("您已经是该球队成员");
        }

        // 检查是否已有待审核的申请
        if (hasApplied(teamId, applicantUserId)) {
            throw new Exception("您已提交过申请，请等待审核");
        }

        // 创建申请记录
        TeamJoinApplication application = new TeamJoinApplication();
        application.setApplicantUserId(applicantUserId);
        application.setTeamId(teamId);
        application.setReason(reason);
        application.setStatus(TeamJoinApplication.STATUS_PENDING);
        
        save(application);

        // 发送消息给队长
        Long captainId = team.getCaptainUserId() != null ? team.getCaptainUserId() : team.getCreateUserId();
        appMessageService.sendTeamMessage(
            captainId,
            "APPLY_JOIN",
            teamId,
            team.getFullName(),
            team.getLogo(),
            "有新的入队申请"
        );

        // 发送透传消息
        transparentMessageService.sendTransparentByApplyJoin(captainId, teamId, applicantUserId);

        return application.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewApplication(Long applicationId, Boolean approved, String reviewReason, Long reviewUserId) throws Exception {
        TeamJoinApplication application = getById(applicationId);
        if (application == null) {
            throw new Exception("申请记录不存在");
        }

        if (application.getStatus() != TeamJoinApplication.STATUS_PENDING) {
            throw new Exception("该申请已被处理");
        }

        // 更新申请状态
        application.setStatus(approved ? TeamJoinApplication.STATUS_APPROVED : TeamJoinApplication.STATUS_REJECTED);
        application.setReviewReason(reviewReason);
        application.setReviewUserId(reviewUserId);
        application.setReviewTime(new Date());
        
        updateById(application);

        TeamApp team = teamAppService.getById(application.getTeamId());

        if (approved) {
            // 通过申请，添加到球队成员
            TeamUserApp teamUser = new TeamUserApp();
            teamUser.setUserId(application.getApplicantUserId());
            teamUser.setTeamId(application.getTeamId());
            teamUserAppService.save(teamUser);

            // 更新申请消息的审核状态为已同意
            appMessageService.updateApplicationMessageReviewStatus(
                application.getTeamId(),
                application.getApplicantUserId(),
                AppMessage.REVIEW_STATUS_APPROVED
            );

            // 发送成功消息给申请人
            appMessageService.sendTeamMessage(
                application.getApplicantUserId(),
                "JOIN_SUCCESS",
                application.getTeamId(),
                team.getFullName(),
                team.getLogo(),
                "恭喜您成功加入球队"
            );

            // 通知队长有新成员加入
            appMessageService.sendTeamMessage(
                team.getCaptainUserId() != null ? team.getCaptainUserId() : team.getCreateUserId(),
                "MEMBER_JOINED",
                application.getTeamId(),
                team.getFullName(),
                team.getLogo(),
                "新成员已加入球队"
            );

            // 发送透传消息给申请人
            transparentMessageService.sendTransparentByJoinTeam(application.getApplicantUserId(), application.getTeamId());

            // 通知球队其他成员有新成员加入
            transparentMessageService.sendTransparentByMemberJoined(application.getTeamId(), application.getApplicantUserId());
        } else {
            // 更新申请消息的审核状态为已拒绝
            appMessageService.updateApplicationMessageReviewStatus(
                application.getTeamId(),
                application.getApplicantUserId(),
                AppMessage.REVIEW_STATUS_REJECTED
            );

            // 拒绝申请，发送拒绝消息
            appMessageService.sendTeamMessage(
                application.getApplicantUserId(),
                "JOIN_REJECTED",
                application.getTeamId(),
                team.getFullName(),
                team.getLogo(),
                "很遗憾，您的入队申请被拒绝：" + (reviewReason != null ? reviewReason : "")
            );

            // 发送透传消息通知申请被拒绝
            transparentMessageService.sendTransparentByJoinRejected(application.getApplicantUserId(), application.getTeamId());
        }

        return true;
    }

    @Override
    public List<TeamJoinApplication> getPendingApplicationsByTeamId(Long teamId) {
        LambdaQueryWrapper<TeamJoinApplication> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TeamJoinApplication::getTeamId, teamId)
               .eq(TeamJoinApplication::getStatus, TeamJoinApplication.STATUS_PENDING)
               .orderByDesc(TeamJoinApplication::getCreateTime);
        return list(wrapper);
    }

    @Override
    public boolean hasApplied(Long teamId, Long userId) {
        LambdaQueryWrapper<TeamJoinApplication> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TeamJoinApplication::getTeamId, teamId)
               .eq(TeamJoinApplication::getApplicantUserId, userId)
               .eq(TeamJoinApplication::getStatus, TeamJoinApplication.STATUS_PENDING);
        return count(wrapper) > 0;
    }
}
