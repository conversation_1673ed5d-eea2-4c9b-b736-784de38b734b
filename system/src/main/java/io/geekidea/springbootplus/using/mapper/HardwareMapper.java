package io.geekidea.springbootplus.using.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.param.HardwarePageParam;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.io.Serializable;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Repository
public interface HardwareMapper extends BaseMapper<Hardware> {


}
