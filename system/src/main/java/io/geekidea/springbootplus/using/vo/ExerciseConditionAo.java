package io.geekidea.springbootplus.using.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.geekidea.springbootplus.using.entity.Hardware;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "ExerciseConditionAo")
public class ExerciseConditionAo {
    @ApiModelProperty("学员id")
    private Long studentId;
    @ApiModelProperty("球队id")
    private Long teamId;
    @ApiModelProperty("学员名字")
    private String studentName;
    @ApiModelProperty("学员头像")
    private String headImg;
    @ApiModelProperty("年龄")
    private Integer age;
    @ApiModelProperty("身高（厘米）")
    private Double height;
    @ApiModelProperty("体重（kg）")
    private Double weight;
    @ApiModelProperty("日期 2022-02-26")
    private String date;
    @ApiModelProperty("卡路里(kcal）")
    private int calorie;
    @ApiModelProperty("步数")
    private int stepCount;
    @ApiModelProperty("跑动距离m")
    private int runDistance;
    @ApiModelProperty("排名")
    private Integer ranking;
    @ApiModelProperty("运动时间（min）")
    private int exerciseTime;
    @ApiModelProperty("0未达标 1达标 2超棒")
    private Integer status;
    @ApiModelProperty("是否启动了本场训练")
    private boolean start;
    @ApiModelProperty("是否同步了本场训练")
    private boolean upload;
    @ApiModelProperty("训练是否作废（个人）")
    private boolean cancel;

    @JsonIgnore
    private int sortNum = 99999;


    @ApiModelProperty("设备信息")
    private Hardware hardware;

    public void setHardware(Hardware hardware) {
        if (hardware != null) {
            this.sortNum = hardware.getNum();
        }
        this.hardware = hardware;
    }

    public void setExerciseTime(int exerciseTime) {
        if (exerciseTime < 60) {
            this.status = 0;
        } else if (exerciseTime >= 80) {
            this.status = 2;
        } else if (exerciseTime >= 60 && exerciseTime < 80) {
            this.status = 1;
        }

        if (exerciseTime == -1) {
            upload = false;
            exerciseTime = 0;
        } else {
            upload = true;
        }
        this.exerciseTime = exerciseTime;
    }
}
