package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TacticsBoardCourse;
import io.geekidea.springbootplus.using.param.TacticsBoardPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.param.UserFavoriteTacticsPageParam;

import java.util.List;

/**
 * 战术板综合信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-22
 */
public interface TacticsBoardCourseService extends BaseService<TacticsBoardCourse> {

    /**
     * 保存
     *
     * @param tacticsBoardCourse
     * @return
     * @throws Exception
     */
    Long saveTacticsBoard(TacticsBoardCourse tacticsBoardCourse) throws Exception;

    /**
     * 修改
     *
     * @param tacticsBoardCourse
     * @return
     * @throws Exception
     */
    boolean updateTacticsBoard(TacticsBoardCourse tacticsBoardCourse) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTacticsBoard(Long id) throws Exception;

    /**
     * 批量删除
     *
     * @param ids
     * @return
     * @throws Exception
     */
    boolean deleteTacticsBoardList(List<Long> ids) throws Exception;


    /**
     * 获取分页对象
     *
     * @param tacticsBoardQueryParam
     * @return
     * @throws Exception
     */
    Paging<TacticsBoardCourse> getTacticsBoardPageList(TacticsBoardPageParam tacticsBoardPageParam) throws Exception;

    /**
     * 收藏或取消收藏战术板（新接口）
     *
     * @param favoriteParam
     * @return
     * @throws Exception
     */
    boolean toggleTacticsBoardFavorite(UserFavoriteTacticsPageParam favoriteParam) throws Exception;

    /**
     * 获取战术板课件详情
     *
     * @param id 战术板课件ID
     * @return 战术板课件详情
     * @throws Exception
     */
    TacticsBoardCourse getTacticsBoardById(Long id) throws Exception;
}
