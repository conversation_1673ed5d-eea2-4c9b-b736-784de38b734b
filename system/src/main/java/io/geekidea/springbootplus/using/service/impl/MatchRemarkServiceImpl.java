package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.MatchRemark;
import io.geekidea.springbootplus.using.mapper.MatchRemarkMapper;
import io.geekidea.springbootplus.using.service.MatchRemarkService;
import io.geekidea.springbootplus.using.param.MatchRemarkPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.service.TransparentMessageService;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-12
 */
@Slf4j
@Service
public class MatchRemarkServiceImpl extends BaseServiceImpl<MatchRemarkMapper, MatchRemark> implements MatchRemarkService {

    @Autowired
    private MatchRemarkMapper matchRemarkMapper;
    @Autowired
    private TransparentMessageService transparentMessageService;

    @Override
    public MatchRemark getMatchRemark(String matchType, Long matchId, Long studentId) {
        MatchRemark matchRemark = getOne(new LambdaQueryWrapper<MatchRemark>().eq(MatchRemark::getMatchType, matchType).eq(MatchRemark::getMatchId, matchId).eq(MatchRemark::getStudentId, studentId));
        return matchRemark;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveMatchRemark(MatchRemark matchRemark) throws Exception {
        MatchRemark matchRemark1 = getMatchRemark(matchRemark.getMatchType(),matchRemark.getMatchId(),matchRemark.getStudentId());
        if(matchRemark1!=null){
            matchRemark.setId(matchRemark1.getId());
        }
        if (super.saveOrUpdate(matchRemark)) {
            org.json.JSONObject jsonObject = new org.json.JSONObject();
            jsonObject.put("studentId", matchRemark.getStudentId());
            jsonObject.put("matchType", matchRemark.getMatchType());
            jsonObject.put("matchId", matchRemark.getMatchId());
            jsonObject.put("remarkTag", matchRemark.getRemarkTag());
            jsonObject.put("remarkText", matchRemark.getRemarkText());
            transparentMessageService.sendTrasparentByRemark(jsonObject);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateMatchRemark(MatchRemark matchRemark) throws Exception {
        if (super.saveOrUpdate(matchRemark)) {
            org.json.JSONObject jsonObject = new org.json.JSONObject();
            jsonObject.put("studentId", matchRemark.getStudentId());
            jsonObject.put("matchType", matchRemark.getMatchType());
            jsonObject.put("matchId", matchRemark.getMatchId());
            jsonObject.put("remarkTag", matchRemark.getRemarkTag());
            jsonObject.put("remarkText", matchRemark.getRemarkText());
            transparentMessageService.sendTrasparentByRemark(jsonObject);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteMatchRemark(String matchType, Long matchId, Long stuId) throws Exception {
        MatchRemark matchRemark = getMatchRemark(matchType, matchId, stuId);
        return super.removeById(matchRemark.getId());
    }

}
