package io.geekidea.springbootplus.using.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.mapper.HardwareAppMapper;
import io.geekidea.springbootplus.using.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Slf4j
@Service
public class HardwareAppServiceImpl extends BaseServiceImpl<HardwareAppMapper, HardwareApp> implements HardwareAppService {

    @Autowired
    private DrillService drillService;
    @Autowired
    private GameService gameService;
    @Autowired
    @Lazy
    private DrillStudentService drillStudentService;
    @Autowired
    private GameStudentService gameStudentService;
    @Autowired
    private MatchHardwareService matchHardwareService;
    @Autowired
    private MatchHardwareAppService matchHardwareAppService;
    @Autowired
    private DelayQueueService delayQueueService;
    @Autowired
    private UserAppService userAppService;
    @Autowired
    private DemoMacQrcodeService demoMacQrcodeService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveHardwareApp(HardwareApp hardwareApp) throws Exception {
        if (!demoMacQrcodeService.hardformatCheckMac(hardwareApp.getMac())) {
            return false;
        }
        return super.save(hardwareApp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateHardwareApp(List<HardwareApp> hardwareApps) throws Exception {
        List<String> hards = new ArrayList<>();
        hardwareApps.forEach(hardware -> {
            if (hardware.getUnbind()) {
                //作废设备启动的所有训练
                hards.add(hardware.getMac());
            }
            super.saveOrUpdate(hardware);
        });
        drillStudentService.setDrillCancel(hards);
        gameStudentService.setGameCancel(hards);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startHardwares(List<HardwareApp> hardwareList, Long drillId, Long teamId) {
        Drill drill = drillService.getById(drillId);
        hardwareList = listByIds(hardwareList.stream().map(HardwareApp::getId).collect(Collectors.toList()));
        List<String> macs = hardwareList.stream().map(HardwareApp::getMac).collect(Collectors.toList());
        drillStudentService.setDrillCancel(macs);
        gameStudentService.setGameCancel(macs);
        List<DrillStudent> drillStudents = new ArrayList<>();

        List<MatchHardware> matchHardwares = matchHardwareService.getMatchHardwareByMatchId(drillId, teamId, "DRILL");
        Map<Long, MatchHardware> matchMap = matchHardwares.stream().collect(Collectors.toMap(MatchHardware::getHardwareId, Function.identity()));
        List<MatchHardware> matchHardwares1 = new ArrayList<>();

        hardwareList.forEach(e -> {
            if (e.getFirstStartTime() == null || (e.getFirstStartTime() != null && e.getFirstStartTime().getTime() == 0l)) {
                e.setFirstStartTime(new Date());
            }
            //设置学员实际启动时间
            DrillStudent drillStudent = drillStudentService.getOneByDrillIdAndStudentId(drillId, e.getStudentId());
            if (drillStudent == null) {
                UserApp userApp = userAppService.getById(e.getStudentId());
                drillStudent = new DrillStudent();
                drillStudent.setDrillId(drillId);
                drillStudent.setStatus(true);
                drillStudent.setStudentId(userApp.getHdStudentId());
                drillStudent.setTeamId(drill.getTeamId());
            }
            drillStudent.setStartTime(new Date());
            long stopTime = System.currentTimeMillis() + (drill.getDuration() * (60 * 1000));
            drillStudent.setStopTime(new Date(stopTime));

            //将设备与训练关联OR  将设备设置为启动状态
            MatchHardware matchHardware = new MatchHardware();
            if (matchMap.containsKey(e.getId())) {
                matchHardware = matchMap.get(e.getId());
            }
            matchHardware.setHardwareId(e.getId());
            matchHardware.setMatchId(drillId);
            matchHardware.setStarted(true);
            matchHardware.setStartTime(new Date());
            matchHardware.setApp(true);
            matchHardware.setTeamId(teamId);
            matchHardware.setMatchType("DRILL");

            matchHardwares1.add(matchHardware);
            drillStudents.add(drillStudent);
            e.setMac(null);
        });

        matchHardwareService.saveOrUpdateBatch(matchHardwares1);
        drillStudentService.saveOrUpdateBatch(drillStudents);
        saveOrUpdateBatch(hardwareList);

        //将训练状态设置为启动
        if (!drill.getStatus().equals(MatchStatusEnum.STARTED)) {
            drill.setStartTime(new Date());
            drill.setStatus(MatchStatusEnum.STARTED);
            drill.setFirstStartTime(new Date());
        }
        drill.setLastStartTime(new Date());
        delayQueueService.addDrillDelayQueue(drill); //每启动一次替换上一次的结束时间
        drillService.saveOrUpdate(drill);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startHardwaresGame(List<HardwareApp> hardwareList, Long gameId, Long teamId) {
        Game game = gameService.getById(gameId);
        hardwareList = listByIds(hardwareList.stream().map(HardwareApp::getId).collect(Collectors.toList()));
        List<String> macs = hardwareList.stream().map(HardwareApp::getMac).collect(Collectors.toList());
        gameStudentService.setGameCancel(macs);
        drillStudentService.setDrillCancel(macs);
        List<GameStudent> gameStudents = new ArrayList<>();

        List<MatchHardwareApp> matchHardwares = matchHardwareAppService.getMatchHardwareByMatchId("GAME", game.getId(), teamId);
        Map<Long, MatchHardwareApp> matchMap = matchHardwares.stream().collect(Collectors.toMap(MatchHardwareApp::getHardwareId, Function.identity()));
        List<MatchHardwareApp> matchHardwares1 = new ArrayList<>();
        Long now = System.currentTimeMillis();
        hardwareList.forEach(e -> {
            if (e.getFirstStartTime() == null || (e.getFirstStartTime() != null && e.getFirstStartTime().getTime() == 0l)) {
                e.setFirstStartTime(new Date());
            }
            //设置学员实际启动时间
            GameStudent gameStudent = gameStudentService.getOneByGameIdAndStudentId(gameId, e.getStudentId());
            if (gameStudent == null) {
                gameStudent = new GameStudent();
                gameStudent.setGameId(gameId);
                gameStudent.setStatus(true);
                gameStudent.setStudentId(e.getStudentId());
                gameStudent.setTeamId(game.getTeamId());
            }
            gameStudent.setStartTime(game.getRealDataTime() != null && game.getRealDataTime() != 0 ? new Date(game.getRealDataTime()) : new Date(now));
            long stopTime = now + (game.getDuration() * (60 * 1000));
            gameStudent.setStopTime(new Date(stopTime));

            //将设备与训练关联OR  将设备设置为启动状态
            MatchHardwareApp matchHardware = new MatchHardwareApp();
            if (matchMap.containsKey(e.getId())) {
                matchHardware = matchMap.get(e.getId());
            }
            matchHardware.setHardwareId(e.getId());
            matchHardware.setMatchId(gameId);
            matchHardware.setStarted(true);
            matchHardware.setStartTime(new Date());
            matchHardware.setTeamId(teamId);
            matchHardware.setMatchType("GAME");

            matchHardwares1.add(matchHardware);
            gameStudents.add(gameStudent);
        });
        if (!game.getStatus().equals(MatchStatusEnum.STARTED)) {
            game.setFinishStartTime(new Date());
            game.setStatus(MatchStatusEnum.STARTED);
        }
        matchHardwareAppService.saveOrUpdateBatch(matchHardwares1);
        gameStudentService.saveOrUpdateBatch(gameStudents);
        saveOrUpdateBatch(hardwareList);
        game.setLastStartTime(new Date());
        gameService.saveOrUpdate(game);
        delayQueueService.addGameDelayQueue(game);
        return true;
    }

    @Override
    public HardwareApp getByStuId(Long stuId, String mac) {
        return getOne(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getStudentId, stuId).eq(mac != null, HardwareApp::getMac, mac).eq(mac != null, HardwareApp::getUnbind, true).eq(mac == null, HardwareApp::getUnbind, false));//
    }

    @Override
    public void test() {
        List<DemoMacQrcode> demoMacQrcodes = demoMacQrcodeService.list();
        for (DemoMacQrcode demoMacQrcode:demoMacQrcodes) {
            update(new LambdaUpdateWrapper<HardwareApp>().eq(HardwareApp::getMac,demoMacQrcode.getMac()).set(HardwareApp::getLongNum,demoMacQrcode.getLongNum()));
        }
    }

}
