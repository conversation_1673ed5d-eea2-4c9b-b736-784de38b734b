package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 *  分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-08-03
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "分页参数FacilityGroupPageParam")
public class FacilityGroupPageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("是否多班级模式")
    private Boolean more;

    @ApiModelProperty("学校id app无需传")
    private Long schoolId;

}
