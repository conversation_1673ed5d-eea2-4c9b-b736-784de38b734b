package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TacticsBoardText;
import io.geekidea.springbootplus.using.param.TacticsBoardTextPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 * 战术板文本表，存储文本信息 服务类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface TacticsBoardTextService extends BaseService<TacticsBoardText> {

    /**
     * 保存
     *
     * @param tacticsBoardText
     * @return
     * @throws Exception
     */
    boolean saveTacticsBoardText(TacticsBoardText tacticsBoardText) throws Exception;

    /**
     * 修改
     *
     * @param tacticsBoardText
     * @return
     * @throws Exception
     */
    boolean updateTacticsBoardText(TacticsBoardText tacticsBoardText) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTacticsBoardText(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param tacticsBoardTextQueryParam
     * @return
     * @throws Exception
     */
    Paging<TacticsBoardText> getTacticsBoardTextPageList(TacticsBoardTextPageParam tacticsBoardTextPageParam) throws Exception;

}
