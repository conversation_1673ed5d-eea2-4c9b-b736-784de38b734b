package io.geekidea.springbootplus.using.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DailyDataSynthesizeAo")
public class DailyDataSynthesizeAo {
    @ApiModelProperty("总运动时间（秒）")
    private long exerciseTime;
    @ApiModelProperty("MVP 参考团队数据")
    private JSONObject mvp;
    @ApiModelProperty("总步数")
    private Integer sumStep;
    @ApiModelProperty("总跑动 (m)")
    private long sumRun;
    @ApiModelProperty("总卡路里")
    private Integer sumCalorie;
    @ApiModelProperty("总跑动最多学生")
    private String runBestStu;
    @ApiModelProperty("总跑动最多学生的跑动值（m）")
    private long bestStuRun;
    @ApiModelProperty("速度数据 参考团队数据同字段")
    private Map<String, Object> speedDataMap;
    @ApiModelProperty("跑动和消耗的X轴 [\"2020-05-01\",\"2020-05-02\",\"2020-05-03\",\"2020-05-04\"]")
    List<String> x;
    @ApiModelProperty("跑动Y轴 [1,2,3,4]")
    List<Integer> runY;
    @ApiModelProperty("消耗Y轴 [1,2,3,4]")
    List<Integer> calorieY;
    @ApiModelProperty("最大跑动Y轴")
    Integer maxRunY;
    @ApiModelProperty("最大消耗Y轴")
    Integer maxCalorieY;
    @ApiModelProperty("{\"avgSpeedAllocation\":总平均配速/km 时间戳,\"avgSpeed\":总平均速度/小时,\"avgStrideFrequency\":总平均步频/分钟,\"avgStride\":总平均步幅/厘米} 速度步伐")
    private JSONObject pace;
    @ApiModelProperty("{\"avgTouchLandTime\":总平均触地时间 单位 毫秒,\"avgTouchdownImpact\":总平均着地冲击 单位 g,\"avgEctropionRange\":总平均外翻幅度 单位 °,\"avgPendulumAngle\":总平均摆动 单位 °} 跑步姿势")
    private JSONObject runningForm;
    @ApiModelProperty("{\"front\":前脚 单位%,\"whole\":全脚 单位%,\"queen\":脚跟 单位%} 团队着地方式 ")
    private JSONObject touchdownWay;

    public void setRunY(List<Integer> runY) {
        this.maxRunY = runY.stream().mapToInt(e -> e).max().getAsInt();
        this.runY = runY;
    }

    public void setCalorieY(List<Integer> calorieY) {
        this.maxCalorieY = calorieY.stream().mapToInt(e -> e).max().getAsInt();
        this.calorieY = calorieY;
    }
}
