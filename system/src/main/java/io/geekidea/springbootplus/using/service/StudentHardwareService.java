package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.StudentHardware;
import io.geekidea.springbootplus.using.param.StudentHardwarePageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface StudentHardwareService extends BaseService<StudentHardware> {

    /**
     * 保存
     *
     * @param studentHardware
     * @return
     * @throws Exception
     */
    boolean saveStudentHardware(StudentHardware studentHardware) throws Exception;

    /**
     * 修改
     *
     * @param studentHardware
     * @return
     * @throws Exception
     */
    boolean updateStudentHardware(StudentHardware studentHardware) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteStudentHardware(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param studentHardwareQueryParam
     * @return
     * @throws Exception
     */
    Paging<StudentHardware> getStudentHardwarePageList(StudentHardwarePageParam studentHardwarePageParam) throws Exception;

}
