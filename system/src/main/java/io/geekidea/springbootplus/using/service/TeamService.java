package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.Team;
import io.geekidea.springbootplus.using.param.TeamPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface TeamService extends BaseService<Team> {

    Team getById(Long id);

    Team getTeam(Long id);

    /**
     * 保存
     *
     * @param team
     * @return
     * @throws Exception
     */
    boolean saveTeam(Team team) throws Exception;

    /**
     * 修改
     *
     * @param team
     * @return
     * @throws Exception
     */
    boolean updateTeam(Team team) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTeam(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param teamQueryParam
     * @return
     * @throws Exception
     */
    Paging<Team> getTeamPageList(TeamPageParam teamPageParam, Long schoolId, Long teacherId) throws Exception;

    List<Team> getTeamsAndCount(Long schoolId);

    List<Team> getTeamsBySchoolId(Long schoolId);

    Boolean startedCount(Long teamId);

    Boolean unUploadCount(Long teamId);

    JSONObject restrict(Long schoolId, Long teacherId);

    JSONObject getMoreRestrict();

    boolean setMoreRestrict(JSONObject jsonObject);

    Boolean groupDelayed(Long teamId);

    Integer courseIdCount(Long scollId,Long teacherId,Long courseId);

}
