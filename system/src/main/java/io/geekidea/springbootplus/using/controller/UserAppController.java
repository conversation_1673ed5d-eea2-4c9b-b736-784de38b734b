package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.aop.annotation.TextCensorUserDefined;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.util.MD5Utils;
import io.geekidea.springbootplus.framework.util.MobileUtils;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.Audit;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.entity.UserApp;
import io.geekidea.springbootplus.using.service.StudentInfoService;
import io.geekidea.springbootplus.using.service.UserAppService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.UserAppPageParam;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Slf4j
@RestController
@RequestMapping("/using/userApp")
@Module("${cfg.module}")
@Api(value = "APP用户模块", tags = {"APP用户模块"})
public class UserAppController extends BaseController {

    @Autowired
    private UserAppService userAppService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private MobileUtils mobileUtils;

    /**
     * 学员登录
     *
     * @param studentInfo
     * @return
     */
    @PostMapping("/login")
    @OperationLog(name = "APP学员登录")
    @ApiOperation(value = "APP学员登录", response = ApiResult.class, notes = "参数 loginType:1 手机登录 2：邮箱登录 phone：手机登录 email:邮箱登录  passwd：密码 deviceTokens:友盟唯一id \n 错误码 999：用户名不存在  1000：密码错误")
    public ApiResult loginStu(@RequestBody UserApp userApp) {
        if ((!StringUtils.isEmpty(userApp.getPhone()) || !StringUtils.isEmpty(userApp.getEmail())) && !StringUtils.isEmpty(userApp.getPasswd())) {
            UserApp userApp1 = userAppService.examineName(userApp);
            if (userApp1 == null) {
                return ApiResult.result(ApiCode.USERNAME_ERROR);
            }
            UserApp userApp2 = userAppService.loginUserApp(userApp);
            if (userApp2 == null) {
                return ApiResult.result(ApiCode.PASSWORD_ERROR);
            }
            userApp2.setToken(genToken());
            putCurrent(userApp2.getToken(), userApp2);
            return ApiResult.ok(userApp2);
        }
        return ApiResult.result(ApiCode.USERNAME_PASSWORD_ISNULL);
    }

    @PostMapping("/register")
    @OperationLog(name = "注册", type = OperationLogType.ADD)
    @ApiOperation(value = "注册", response = UserApp.class, notes = "手机注册需要传入验证码phoneCode 邮箱注册需要传入验证码emailCode 收不到验证码的情况传‘true’就会通过 错误code 20035手机号重复  20052邮箱号重复  20006：验证码错误 20009：验证码过期" +
            "返回参考 APP学员登录接口")
    @TextCensorUserDefined
    public ApiResult register(@RequestBody UserApp userApp) throws Exception {
        ApiCode checkVerificationCode = null;
        if (userApp.getPhone() != null && userApp.getLoginType() == 1) {
            checkVerificationCode = userAppService.checkVerificationCode(userApp.getPhone(), userApp.getPhoneCode(), true);
        } else {
            checkVerificationCode = userAppService.checkEmailVerificationCode(userApp.getEmail(), userApp.getEmailCode());
            if (userApp.getEmailCode().equals("true")) {
                checkVerificationCode = null;
            }
        }
        if (checkVerificationCode != null) {
            return ApiResult.result(checkVerificationCode);
        }
        ApiCode apiCode = userAppService.onlyValidation(userApp.getPhone());
        if (apiCode != null) {
            return ApiResult.result(apiCode);
        }
        ApiCode apiCode1 = userAppService.onlyValidationEmail(userApp.getEmail());
        if (apiCode1 != null) {
            return ApiResult.result(apiCode1);
        }
        boolean flag = userAppService.register(userApp);
        if (flag) {
            userApp.setToken(genToken());
            putCurrent(userApp.getToken(), userApp);
            return ApiResult.ok(userApp);
        }
        return ApiResult.ok(false);
    }

    @PostMapping("/resetPassword")
    @OperationLog(name = "重置密码")
    @ApiOperation(value = "重置密码", response = ApiResult.class, notes = "body 需要传手机号 邮箱 登录类型 和 密码 手机需要传入验证码phoneCode 邮箱需要传入验证码emailCode" +
            "\n错误code 20006：验证码错误 20009：验证码过期")
    public ApiResult<Boolean> resetPassword(@RequestBody UserApp userApp) throws Exception {
        ApiCode checkVerificationCode = null;

        if (!userAppService.checkEmailExist(userApp.getEmail()) && !userAppService.checkPhoneExist(userApp.getPhone())) {
            return ApiResult.fail(ApiCode.USERNAME_ERROR);
        }
        if (userApp.getPhone() != null && userApp.getLoginType() == 1) {
            checkVerificationCode = userAppService.checkVerificationCode(userApp.getPhone(), userApp.getPhoneCode(), true);
        } else {
            checkVerificationCode = userAppService.checkEmailVerificationCode(userApp.getEmail(), userApp.getEmailCode());
        }
        if (checkVerificationCode != null) {
            return ApiResult.result(checkVerificationCode);
        }
        return ApiResult.result(userAppService.resetPassword(userApp));
    }

    /**
     * APP学员退出登录
     *
     * @param token
     * @return
     */
    @PostMapping("/logoutUser/{token}")
    @OperationLog(name = "APP退出登录")
    @ApiOperation(value = "APP退出登录", response = ApiResult.class)
    public ApiResult logoutUser(@PathVariable("token") String token) {
        if (StringUtils.isEmpty(token)) {
            return ApiResult.fail(ApiCode.TOKEN_ISNULL);
        }
        UserApp userApp = (UserApp) redisUtils.get(CacheConsts.TOKEN_PREFIX + token);
        redisUtils.del(CacheConsts.TOKEN_PREFIX + token);
        redisUtils.del(CacheConsts.USERID_TOKEN_PREFIX_APP + userApp.getId());
        return ApiResult.ok();
    }

    @PostMapping("/writeOff")
    @OperationLog(name = "注销 app端使用")
    @ApiOperation(value = "注销 app端使用", response = ApiResult.class, notes = "")
    public ApiResult writeOff() throws Exception {
        UserApp userApp = getCurrentApp();
        //修改用户名 逻辑删除
        Boolean b = userAppService.logoutUser(userApp);
        if (b) {
            delCurrentApp();
        }
        return ApiResult.ok(b);
    }


    @GetMapping("/getDeviceTokens/{userId}")
    @ApiOperation(value = " 获取账号上次登录的 友盟id", response = ApiResult.class, notes = "")
    public ApiResult getDeviceTokens(@PathVariable("userId") Long userId) {
        UserApp userApp = userAppService.getById(userId);
        return ApiResult.ok(userApp.getDeviceTokens());
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    @TextCensorUserDefined
    public ApiResult<Boolean> updateUserApp(@RequestBody UserApp userApp) throws Exception {
        UserApp userApp1 = userAppService.getById(userApp.getId());
        ApiCode apiCode = null;
        if (userApp.getPhone() != null && !userApp.getPhone().equals(userApp1.getPhone())) {
            apiCode = userAppService.updaValidation(userApp.getPhone(), userApp.getId());
        }
        if (userApp.getEmail() != null && !userApp.getEmail().equals(userApp1.getEmail())) {
            apiCode = userAppService.updaValidationEmail(userApp.getEmail(), userApp.getId());
        }
        if (apiCode != null) {
            return ApiResult.result(apiCode);
        }

        if (userApp.getEmailCode() != null || userApp.getPhoneCode() != null) {
            if (userApp.getPhoneCode() != null) {
                apiCode = userAppService.checkVerificationCode(userApp.getPhone(), userApp.getPhoneCode(), true);
            } else {
                apiCode = userAppService.checkEmailVerificationCode(userApp.getEmail(), userApp.getEmailCode());
            }
            if (apiCode != null) {
                return ApiResult.result(apiCode);
            }

        }

        boolean flag = userAppService.updateUserApp(userApp);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteUserApp(@PathVariable("id") Long id) throws Exception {
        boolean flag = userAppService.deleteUserApp(id);
        return ApiResult.result(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = UserApp.class)
    public ApiResult<UserApp> getUserApp(@PathVariable("id") Long id) throws Exception {
        UserApp userApp = userAppService.getUser(id);
        return ApiResult.ok(userApp);
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = UserApp.class)
    public ApiResult<Paging<UserApp>> getUserAppPageList(@Validated @RequestBody UserAppPageParam userAppPageParam) throws Exception {
        Paging<UserApp> paging = userAppService.getUserAppPageList(userAppPageParam);
        return ApiResult.ok(paging);
    }

    @GetMapping("/getCount/{userId}/{matchType}/{courseId}")
    @OperationLog(name = "获取数量相关数据")
    @ApiOperation(value = "获取数量相关数据", response = String.class, notes = "请求参数userId:用户id(训练的话是hdStuentId) matchType：GAME|DRILL courseId:课程类型id(跑动|篮球)查询全部不传 或者-1 2篮球 3足球 4 查询数字体育课 \n 返回参数{\n" +
    /*        "        \"latestData\": 一周最新数据数量,\n" +
            "        \"latestMatch\": 一周最新比赛|训练数量\n" +*/
            "        \"finish\": 已完成（app端）\n" +
            "        \"noFinish\": 未完成（app端）\n" +
            "        \"upload\": 已同步（app端）\n" +
            "        \"noAppLookDrill\":  app端球员未查看pad端训练数据\n" +
            "        \"noAppLookMonitor\":  app端球员未查看pad端监测数据\n" +
            "    }")
    public ApiResult getCount(@PathVariable("userId") Long userId, @PathVariable("matchType") String matchType, @PathVariable("courseId") Long courseId) {
        return ApiResult.ok(userAppService.getCount(userId, matchType, courseId));
    }

    @PostMapping("/sendPhoneVerificationCode/{phoneNumber}")
    @OperationLog(name = "用户手机发送验证")
    @ApiOperation(value = "用户手机发送验证", response = ApiResult.class, notes = "--返回参数参考pad协议 --参考pad协议 --参考pad协议" +
            "错误code")
    public ApiResult sendPhoneVerificationCode(@PathVariable("phoneNumber") String phoneNumber) {
        String b = userAppService.sendPhoneVerificationCode(phoneNumber);
        if (b.equals("0000")) {
            return ApiResult.ok(redisUtils.get(io.geekidea.springbootplus.framework.constant.CacheConsts.MOBILE_PHONE_MESSAGE_PREFIX + phoneNumber));
        } else if (b.equals("-1")) {
            return ApiResult.result(ApiCode.PHONE_CODE_SEND_ERROR);
        } else if (b.equals("20016")) {
            return ApiResult.result(ApiCode.SMS_ASTRICT_MINUTE);
        } else if (b.equals("20017")) {
            return ApiResult.result(ApiCode.SMS_ASTRICT_HOUR);
        } else if (b.equals("20018")) {
            return ApiResult.result(ApiCode.SMS_ASTRICT_DAY);
        } else {
            return ApiResult.result(ApiCode.PHONE_CODE_SEND_ERROR);
        }
    }

    /**
     * @desc: 验证手机验证码
     * @author: DH
     * @date: 2020/10/22 16:07
     */
    @PostMapping("/checkVerificationCode")
    @OperationLog(name = "验证手机验证码 ")
    @ApiOperation(value = "验证手机验证码", response = ApiResult.class, notes = "--返回参数参考pad协议 --参考pad协议 --参考pad协议" +
            "错误code 20006：验证码错误 20009：验证码过期")
    public ApiResult checkVerificationCode(@RequestBody JSONObject paramJson) {
        String verificationCode = paramJson.getString("verificationCode");
        String phone = paramJson.getString("phone");
        ApiCode checkVerificationCode = userAppService.checkVerificationCode(phone, verificationCode, false);
        if (checkVerificationCode != null) {
            return ApiResult.result(checkVerificationCode);
        }
        return ApiResult.ok();
    }

    @PostMapping("/checkPhoneExist/{phoneNumber}")
    @OperationLog(name = "验证手机是否存在 ")
    @ApiOperation(value = "验证手机是否存在", response = ApiResult.class, notes = "data: true:存在,false:不存在")
    public ApiResult checkPhoneExist(@PathVariable("phoneNumber") String phoneNumber) {
        return ApiResult.ok(userAppService.checkPhoneExist(phoneNumber));
    }

    @PostMapping("/sendEmailVerificationCode/{email}/{en}")
    @OperationLog(name = "用户email发送验证")
    @ApiOperation(value = "用户email发送验证", response = ApiResult.class, notes = "--返回参数参考手机版验证 " +
            "错误code")
    public ApiResult sendEmailVerificationCode(@PathVariable("email") String email, @PathVariable("en") Boolean en) {
        String b = userAppService.sendEmailVerificationCode(email, en);
        if (b.equals("0000")) {
            return ApiResult.ok(redisUtils.get(io.geekidea.springbootplus.framework.constant.CacheConsts.EMAIL_MESSAGE_PREFIX + email));
        } else if (b.equals("-1")) {
            return ApiResult.result(ApiCode.PHONE_CODE_SEND_ERROR);
        } else if (b.equals("20016")) {
            return ApiResult.result(ApiCode.SMS_ASTRICT_MINUTE);
        } else if (b.equals("20017")) {
            return ApiResult.result(ApiCode.SMS_ASTRICT_HOUR);
        } else if (b.equals("20018")) {
            return ApiResult.result(ApiCode.SMS_ASTRICT_DAY);
        } else {
            return ApiResult.result(ApiCode.PHONE_CODE_SEND_ERROR);
        }
    }

    /**
     * @desc: 验证手机验证码
     * @author: DH
     * @date: 2020/10/22 16:07
     */
    @PostMapping("/checkEmailVerificationCode")
    @OperationLog(name = "验证邮箱验证码 ")
    @ApiOperation(value = "验证邮箱验证码", response = ApiResult.class, notes = "--返回参数手机版验证 " +
            "错误code 20006：验证码错误 20009：验证码过期")
    public ApiResult checkEmailVerificationCode(@RequestBody JSONObject paramJson) {
        String verificationCode = paramJson.getString("verificationCode");
        String email = paramJson.getString("email");
        ApiCode checkVerificationCode = userAppService.checkEmailVerificationCode(email, verificationCode);
        if (checkVerificationCode != null) {
            return ApiResult.result(checkVerificationCode);
        }
        return ApiResult.ok();
    }

    @PostMapping("/checkEmailExist/{email}")
    @OperationLog(name = "验证邮箱是否存在 ")
    @ApiOperation(value = "验证邮箱是否存在", response = ApiResult.class, notes = "data: true:存在,false:不存在")
    public ApiResult checkEmailExist(@PathVariable("email") String email) {
        return ApiResult.ok(userAppService.checkPhoneExist(email));
    }

}

