package io.geekidea.springbootplus.using.param;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 * 用户收藏战术板记录表 分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "用户收藏战术板记录表分页参数")
public class UserFavoriteTacticsPageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("战术板ID，关联tactics_board表的id字段")
    private Long tacticsBoardId;

    @ApiModelProperty("是否收藏")
    private Boolean isFavorite;
}
