package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.using.entity.User;
import io.geekidea.springbootplus.using.param.UserPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-05-05
 */
public interface UserService extends BaseService<User> {

    /**
     * 登录
     *
     * @param user
     * @return
     * @throws Exception
     */
    User login(User user) throws Exception;

    /**
     * @desc: 根据名字搜索
     * @author: DH
     * @date: 2022/5/5 10:36
     */
    User getByName(String userNmae);

    /**
     * @desc: 验证密码
     * @author: DH
     * @date: 2022/5/16 14:27
     */
    Boolean checkPassword(String password, String ciphertext);

    /**
     * 修改
     *
     * @param user
     * @return
     * @throws Exception
     */
    boolean updateUser(User user) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteUser(Long id) throws Exception;

    String sendPhoneVerificationCode(String phoneNumber);

    ApiCode checkVerificationCode(String credential, String contentBody, Boolean delete);

    Boolean checkPhoneExist(String phone);

    int count(Long schoollId);

    Boolean logoutUser(User user);

}
