package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TeamApp对象")
@TableName(autoResultMap = true)
public class TeamApp extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("app用户id 如果创建的是客队 那么该字段传0")
    private Long userId;

    @ApiModelProperty("班级logo")
    private String logo;

    @ApiModelProperty("班级全称")
    private String fullName;

    @ApiModelProperty("班级简称")
    private String shortName;

    @ApiModelProperty("成立时间")
    private Date formedTime;

    @ApiModelProperty("属性")
    private String property;

    @ApiModelProperty("联系人")
    private String contacts;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("简介")
    private String introduction;

    @ApiModelProperty("区域 {\"country\":\"国家代码\",\"province\":\"省代码\",\"city\":\"市代码\",\"county\":\"区代码\"}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject region;

    @ApiModelProperty("球场")
    private String court;

    @ApiModelProperty("2篮球 3足球 4数字体育课")
    private Long courseId;

    @ApiModelProperty("创建该球队的用户id")
    private Long createUserId;

    @ApiModelProperty("队长用户ID")
    private Long captainUserId;

    @ApiModelProperty("是否主队：true-主队，false-客队")
    private Boolean isHomeTeam;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Integer deleted;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
