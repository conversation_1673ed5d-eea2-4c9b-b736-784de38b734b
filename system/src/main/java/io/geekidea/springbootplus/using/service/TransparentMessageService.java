package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.param.TransparentMessagePageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import org.json.JSONObject;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
public interface TransparentMessageService extends BaseService<TransparentMessage> {

    /**
     * 通知学校老师新增人数
     * @param schoolId
     */
    void sendSchoolAllPersonCount(Long schoolId);

    /**
     * 通知用户更新
     */
    void sedTransparent(JSONObject message);

    /**
     * 通知用户训练结束
     */
    void sendTransparentByDrillPast(Drill drill) throws Exception;

    /**
     * 通知用户比赛结束
     */
    void sendTransparentByGamePast(Game game) throws Exception;

    /**
     * 通知用户训练作废
     */
    void sendTransparentByDrillCancel(Long drillId) throws Exception;

    void  sendTransparentByGameCancel(Long gameId);

    /**
     * 通知用户设备更换
     */
    void sendTransparentByHardware(Long studentId) throws Exception;

    /**
     * 通知teacher 学生更换设备
     */
    void sendTransparentByTeacherHardware(Hardware hardware) throws Exception;

    /**
     * 通知APP用户训练已经同步
     */
    void sendTransparentByDrillUpload(List<PersonData> personDatas);

    /**
     * 通知APP用户比赛已经同步
     */
    void sendTransparentByGameUpload(List<PersonData> personDatas);

    /**
     * 通知APP用户训练删除
     */
    void sendTransparentByDrillDelete(Long drillId);

    /**
     * 通知APP用户删除
     */
    void sendTransparentByStudentDelete(Long studentId,Long teamId);

    /**
     * 通知APP用户球员加入班级
     */
    void sendTransparentByJoinTeam(Long studentId,Long teamId);

    /**
     * 扫码成功通知老师
     */
    void sendTransparentByTeacherScanQRCode(JSONObject message);

    /**
     * 通知老师（计时器）
     */
    void sendTransparentByTeacherTime(JSONObject message);

    /**
     * 场记修改通知学生
     */
//    void sendFieldNotesByStudent(JSONObject message);

    /**
     * 通知学校教练设备组绑定的班级过期
     */
    void sendTransparentByGroupPast(Long teamId);

    /**
     * 通知学校教练设备组绑定的班级续费
     */
    void sendTransparentByGroupDelayed(Long teamId);

    /**
     * 通知学校和老师hdVersion变动
     */
    void sendTrasparentByHdVersionUpdate(Long userId,String hdVersion);

    void sendTrasparentByRemark(JSONObject message);


    boolean sendTrasparentBykindergartenMonitor(JSONObject message);

    void sendTransparentByMonitorUpload(List<PersonData> personDatas);

    /**
     * 通知APP用户球队解散
     */
    void sendTransparentByTeamDissolve(Long teamId);

    /**
     * 通知APP用户被踢出球队
     */
    void sendTransparentByKickedOut(Long userId, Long teamId);

    /**
     * 通知APP用户有成员退出球队
     */
    void sendTransparentByMemberQuit(Long captainUserId, Long teamId, Long quitUserId);

    /**
     * 通知APP用户有新的入队申请
     */
    void sendTransparentByApplyJoin(Long captainUserId, Long teamId, Long applicantUserId);

    /**
     * 通知球队其他成员有新成员加入
     */
    void sendTransparentByMemberJoined(Long teamId, Long newMemberUserId);

    /**
     * 通知申请人入队申请被拒绝
     */
    void sendTransparentByJoinRejected(Long applicantUserId, Long teamId);

}
