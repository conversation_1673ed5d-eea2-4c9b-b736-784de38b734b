package io.geekidea.springbootplus.using.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.geekidea.springbootplus.using.entity.Drill;
import io.geekidea.springbootplus.using.entity.DrillStudent;
import io.geekidea.springbootplus.using.entity.Game;
import io.geekidea.springbootplus.using.entity.GameStudent;
import io.geekidea.springbootplus.using.mapper.GameStudentMapper;
import io.geekidea.springbootplus.using.service.DrillService;
import io.geekidea.springbootplus.using.service.GameService;
import io.geekidea.springbootplus.using.service.GameStudentService;
import io.geekidea.springbootplus.using.param.GameStudentPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.service.TransparentMessageService;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Slf4j
@Service
public class GameStudentServiceImpl extends BaseServiceImpl<GameStudentMapper, GameStudent> implements GameStudentService {

    @Autowired
    private GameStudentMapper gameStudentMapper;
    @Autowired
    @Lazy
    TransparentMessageService transparentMessageService;
    @Autowired
    @Lazy
    GameService gameService;

    @Override
    public GameStudent getOneByGameIdAndStudentId(Long gameId, Long studentId) {
        return getOne(new LambdaQueryWrapper<GameStudent>().eq(GameStudent::getGameId, gameId).eq(GameStudent::getStudentId, studentId));
    }

    @Override
    public Boolean setStudentGameStatus(List<GameStudent> gameStudentList) {
        if (gameStudentList.isEmpty()) {
            return false;
        }
        Game game=gameService.getById(gameStudentList.get(0).getGameId());

        gameStudentList.forEach(e -> {
            GameStudent gameStudent = getOne(new QueryWrapper<GameStudent>().eq("student_id", e.getStudentId()).eq("team_id", e.getTeamId()).eq("game_id", e.getGameId()));
            if (gameStudent != null) {
                e.setId(gameStudent.getId());
            }
            if(game!=null&&e.getStartTime()==null&&!game.getHaveHard()){
                e.setStartTime(new Date());
                long stopTime = e.getStartTime().getTime() + (game.getDuration() * (60 * 1000));
                e.setStopTime(new Date(stopTime));
            }
            saveOrUpdate(e);
        });
        return true;
    }

    @Override
    public List<GameStudent> getStudentGameStatus(Long gameId, Long userId) {
        return list(new LambdaQueryWrapper<GameStudent>().eq(GameStudent::getGameId, gameId).eq(GameStudent::getStudentId, userId));
    }

    @Override
    public List<GameStudent> getStudentGameStatus(List<Long> gameIds, Long teamId,Long studentId) {
        if(gameIds.size()==0){
            return new ArrayList<>();
        }
        return list(new LambdaQueryWrapper<GameStudent>().in(GameStudent::getGameId, gameIds).eq(teamId!=null,GameStudent::getTeamId, teamId).eq(GameStudent::getStudentId,studentId));
    }

    @Override
    public List<GameStudent> getStudentGameStatusNoCancel(Long gameId, Long teamId) {
        return list(new LambdaQueryWrapper<GameStudent>().eq(GameStudent::getGameId, gameId).eq(GameStudent::getTeamId, teamId).eq(GameStudent::getCancel,0));
    }

    @Override
    public List<GameStudent> getListByGameId(Long gameId) {
        return list(new LambdaQueryWrapper<GameStudent>().eq(GameStudent::getGameId, gameId));
    }

    @Override
    public int attendance(Long gameId, Long teamId) {
        return count(new QueryWrapper<GameStudent>().eq("team_id", teamId).eq("game_id", gameId).isNotNull("status"));
    }

    @Override
    public boolean setGameCancel(List<String> macs) {
        if (macs.size() == 0) {
            return false;
        }
        List<GameStudent> gameStudents = gameStudentMapper.getNoUploaded(macs);
        gameStudents.addAll(gameStudentMapper.getNoUploadedApp(macs));
        List<GameStudent> gameStudents1 = new ArrayList<>();
        if (gameStudents.size() == 0) {
            return false;
        }
        gameStudents.forEach(e -> {
            GameStudent gameStudent = getById(e.getId());
            gameStudent.setCancel(true);
            gameStudents1.add(gameStudent);
        });
        try {
            transparentMessageService.sendTransparentByGameCancel(gameStudents.get(0).getGameId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return saveOrUpdateBatch(gameStudents1);
    }

    @Override
    public boolean stopGame(Long gameId) {
        Date date = new Date();
        return update(new LambdaUpdateWrapper<GameStudent>().isNotNull(GameStudent::getStartTime).eq(GameStudent::getGameId,gameId).set(GameStudent::getStopTime,date));
    }
}
