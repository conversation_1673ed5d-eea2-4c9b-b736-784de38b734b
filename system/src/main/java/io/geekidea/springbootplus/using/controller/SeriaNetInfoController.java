package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController("seriaNetInfoController")
@RequestMapping("/using/seriaNetInfoController")
@Module("teambox")
@Api(value = "透传信息", tags = {"透传信息"})
public class SeriaNetInfoController {

    @PostMapping("/getSeriaNetInfo")
    @OperationLog(name = "获取透传信息文档", type = OperationLogType.ADD)
    @ApiOperation(value = "透传信息文档", response = ApiResult.class, notes =
            "=== 透传消息Action类型说明 ===\n\n" +

            "【学校管理类】\n" +
            "• 学校人数变更: {action:person_count,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,count:变更后的数量}\n" +
            "• 设备组过期通知: {action:group_past,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,teamId:班级id}\n" +
            "• 设备组续费通知: {action:group_delayed,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,teamId:班级id}\n" +
            "• 版本变更通知: {action:hd_version_update,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,teacherId:老师id,hdVersion:版本}\n\n" +

            "【训练管理类】\n" +
            "• 训练结束: {action:drill_past,deviceToken:xxx,receiveStudentId:学生创建的训练才有,receiveUserId:接收人id,schoolId:学校id,drillId:训练id}\n" +
            "• 训练作废: {action:drill_cancel,deviceToken:xxx,receiveUserId:接收人id,drillId:训练id}\n" +
            "• 训练删除: {action:drill_delete,deviceToken:xxx,receiveUserId:学生id}\n" +
            "• 训练上传: {action:drill_upload,deviceToken:xxx,receiveUserId:学生id,drillId:训练id}\n\n" +

            "【比赛管理类】\n" +
            "• 比赛结束: {action:game_past,deviceToken:xxx,receiveStudentId:学生创建的训练才有,receiveUserId:接收人id,schoolId:学校id,gameId:比赛id}\n" +
            "• 比赛作废: {action:game_cancel,deviceToken:xxx,receiveUserId:接收人id,gameId:比赛id}\n" +
            "• 比赛上传: {action:game_upload,deviceToken:xxx,receiveUserId:学生id,gameId:比赛id}\n\n" +

            "【球队管理类】\n" +
            "• 申请加入球队: {action:apply_join,deviceToken:xxx,receiveUserId:队长用户ID,teamId:球队ID,teamName:球队名称,applicantUserId:申请人用户ID,applicantName:申请人姓名}\n" +
            "• 球员加入班级: {action:join_team,deviceToken:xxx,receiveUserId:申请人用户ID,teamId:班级id}\n" +
            "• 学员删除: {action:student_delete,deviceToken:xxx,receiveUserId:学生id,teamId:班级id}\n" +
            "• 入队申请被拒绝: {action:join_rejected,deviceToken:xxx,receiveUserId:申请人用户ID,teamId:球队ID,teamName:球队名称}\n" +
            "• 新成员加入通知: {action:member_joined,deviceToken:xxx,receiveUserId:现有成员用户ID,teamId:球队ID,teamName:球队名称,newMemberUserId:新加入成员用户ID,newMemberName:新成员姓名}\n" +
            "• 被踢出球队: {action:kicked_out,deviceToken:xxx,receiveUserId:被删除成员用户ID,teamId:球队ID,teamName:球队名称}\n" +
            "• 球队解散: {action:team_dissolve,deviceToken:xxx,receiveUserId:成员用户ID,teamId:球队ID,teamName:球队名称}\n" +
            "• 成员退出球队: {action:member_quit,deviceToken:xxx,receiveUserId:队长用户ID,teamId:球队ID,teamName:球队名称,quitUserId:退出成员用户ID,quitUserName:退出成员姓名}\n\n" +

            "【计时管理类】\n" +
            "• 计时员扫码成功: {action:teacher_scan_code,deviceToken:xxx,receiveUserId:老师id,schoolId:学校id,teacherId:老师id,studentId:学生id,drillExtraId:计时计数id}\n" +
            "• 计时员操作通知: {action:teacher_time,deviceToken:xxx,receiveUserId:老师id,studentId:学生id,schoolId:学校id,teacherId:老师id,turns:圈数,drillExtraId:计时计数id,type:start|stop|over,stopTime:结束时间戳}\n\n" +

            "【场记管理类】\n" +
            "• 场记修改通知学生: {action:fieldNotes_update,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,drillId:训练id,fieldNodesId:场记id}\n" +
            "• 场记修改通知用户(APP): {action:fieldNotes_update_app,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,matchId:赛事id,matchType:赛事类型,fieldNodesId:场记id}\n\n" +

            "【监测管理类】\n" +
            "• 监测上传: {action:monitor_upload,deviceToken:xxx,receiveUserId:学生id,monitorId:监测id}\n" +
            "• 体育监测开始: {action:kindergartenMonitor_start,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,startTime:开始时间戳,stopTime:结束时间戳}\n" +
            "• 体育监测结束: {action:kindergartenMonitor_stop,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,monitorId:监测id,startTime:开始时间戳,stopTime:结束时间戳}\n\n" +

            "【教学管理类】\n" +
            "• 教练点评通知: {action:teacher_remark,deviceToken:xxx,receiveUserId:接收人id,matchId:赛事id,matchType:赛事类型,remarkTak:[1],remarkText:点评内容}\n\n" +

            "=== 参数说明 ===\n" +
            "• deviceToken: 设备推送令牌\n" +
            "• receiveUserId: 接收消息的用户ID\n" +
            "• action: 透传动作类型\n" +
            "• 其他参数根据具体业务场景传递相关ID和数据")
    public ApiResult getSeriaNetInfo() {
        String s = "=== 透传消息Action类型说明 ===\n\n" +

                "【学校管理类】\n" +
                "• 学校人数变更: {action:person_count,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,count:变更后的数量}\n" +
                "• 设备组过期通知: {action:group_past,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,teamId:班级id}\n" +
                "• 设备组续费通知: {action:group_delayed,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,teamId:班级id}\n" +
                "• 版本变更通知: {action:hd_version_update,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,teacherId:老师id,hdVersion:版本}\n\n" +

                "【训练管理类】\n" +
                "• 训练结束: {action:drill_past,deviceToken:xxx,receiveStudentId:学生创建的训练才有,receiveUserId:接收人id,schoolId:学校id,drillId:训练id}\n" +
                "• 训练作废: {action:drill_cancel,deviceToken:xxx,receiveUserId:接收人id,drillId:训练id}\n" +
                "• 训练删除: {action:drill_delete,deviceToken:xxx,receiveUserId:学生id}\n" +
                "• 训练上传: {action:drill_upload,deviceToken:xxx,receiveUserId:学生id,drillId:训练id}\n\n" +

                "【比赛管理类】\n" +
                "• 比赛结束: {action:game_past,deviceToken:xxx,receiveStudentId:学生创建的训练才有,receiveUserId:接收人id,schoolId:学校id,gameId:比赛id}\n" +
                "• 比赛作废: {action:game_cancel,deviceToken:xxx,receiveUserId:接收人id,gameId:比赛id}\n" +
                "• 比赛上传: {action:game_upload,deviceToken:xxx,receiveUserId:学生id,gameId:比赛id}\n\n" +

                "【球队管理类】\n" +
                "• 申请加入球队: {action:apply_join,deviceToken:xxx,receiveUserId:队长用户ID,teamId:球队ID,teamName:球队名称,applicantUserId:申请人用户ID,applicantName:申请人姓名}\n" +
                "• 球员加入班级: {action:join_team,deviceToken:xxx,receiveUserId:申请人用户ID,teamId:班级id}\n" +
                "• 学员删除: {action:student_delete,deviceToken:xxx,receiveUserId:学生id,teamId:班级id}\n" +
                "• 入队申请被拒绝: {action:join_rejected,deviceToken:xxx,receiveUserId:申请人用户ID,teamId:球队ID,teamName:球队名称}\n" +
                "• 新成员加入通知: {action:member_joined,deviceToken:xxx,receiveUserId:现有成员用户ID,teamId:球队ID,teamName:球队名称,newMemberUserId:新加入成员用户ID,newMemberName:新成员姓名}\n" +
                "• 被踢出球队: {action:kicked_out,deviceToken:xxx,receiveUserId:被删除成员用户ID,teamId:球队ID,teamName:球队名称}\n" +
                "• 球队解散: {action:team_dissolve,deviceToken:xxx,receiveUserId:成员用户ID,teamId:球队ID,teamName:球队名称}\n" +
                "• 成员退出球队: {action:member_quit,deviceToken:xxx,receiveUserId:队长用户ID,teamId:球队ID,teamName:球队名称,quitUserId:退出成员用户ID,quitUserName:退出成员姓名}\n\n" +

                "【计时管理类】\n" +
                "• 计时员扫码成功: {action:teacher_scan_code,deviceToken:xxx,receiveUserId:老师id,schoolId:学校id,teacherId:老师id,studentId:学生id,drillExtraId:计时计数id}\n" +
                "• 计时员操作通知: {action:teacher_time,deviceToken:xxx,receiveUserId:老师id,studentId:学生id,schoolId:学校id,teacherId:老师id,turns:圈数,drillExtraId:计时计数id,type:start|stop|over,stopTime:结束时间戳}\n\n" +

                "【场记管理类】\n" +
                "• 场记修改通知学生: {action:fieldNotes_update,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,drillId:训练id,fieldNodesId:场记id}\n" +
                "• 场记修改通知用户(APP): {action:fieldNotes_update_app,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,matchId:赛事id,matchType:赛事类型,fieldNodesId:场记id}\n\n" +

                "【监测管理类】\n" +
                "• 监测上传: {action:monitor_upload,deviceToken:xxx,receiveUserId:学生id,monitorId:监测id}\n" +
                "• 体育监测开始: {action:kindergartenMonitor_start,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,startTime:开始时间戳,stopTime:结束时间戳}\n" +
                "• 体育监测结束: {action:kindergartenMonitor_stop,deviceToken:xxx,receiveUserId:接收人id,schoolId:学校id,monitorId:监测id,startTime:开始时间戳,stopTime:结束时间戳}\n\n" +

                "【教学管理类】\n" +
                "• 教练点评通知: {action:teacher_remark,deviceToken:xxx,receiveUserId:接收人id,matchId:赛事id,matchType:赛事类型,remarkTak:[1],remarkText:点评内容}\n\n" +

                "=== 参数说明 ===\n" +
                "• deviceToken: 设备推送令牌\n" +
                "• receiveUserId: 接收消息的用户ID\n" +
                "• action: 透传动作类型\n" +
                "• 其他参数根据具体业务场景传递相关ID和数据";

        return ApiResult.ok(s);
    }
}
