package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.FacilityGroup;
import io.geekidea.springbootplus.using.entity.FacilityHardware;
import io.geekidea.springbootplus.using.param.FacilityGroupPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
public interface FacilityGroupService extends BaseService<FacilityGroup> {

    /**
     * 保存
     *
     * @param facilityGroup
     * @return
     * @throws Exception
     */
    boolean saveFacilityGroup(FacilityGroup facilityGroup) throws Exception;

    /**
     * 修改
     *
     * @param facilityGroup
     * @return
     * @throws Exception
     */
    boolean updateFacilityGroup(FacilityGroup facilityGroup) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteFacilityGroup(Long id) throws Exception;

    FacilityGroup getFacilityGroup(Long id);

    /**
     * 获取分页对象
     *
     * @param hardwareGroupQueryParam
     * @return
     * @throws Exception
     */
    Paging<FacilityGroup> getFacilityGroupPageList(FacilityGroupPageParam hardwareGroupPageParam) throws Exception;

    Boolean dredge(Long groupId, Long teamId, Long originalGroupId);

    Boolean unBind(Long teamId, Long groupId);

    Long getFacilityGroupId(Long teamId);

    Map<String, Object> teamFacilityGroup(Long schoolId, Long teamId, String key);

    List<FacilityHardware> residue(Long groupId);

}
