package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.framework.util.UuidUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.entity.User;
import io.geekidea.springbootplus.using.entity.UserApp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public abstract class BaseController {
    @Autowired
    private RedisUtils redisUtils;
    @Resource
    private HttpServletRequest request;

    private void cacheUser(String token, User user) {
        redisUtils.set(CacheConsts.TOKEN_PREFIX + token, user);
    }

    private void cacheUser(String token, UserApp user) {
        redisUtils.set(CacheConsts.TOKEN_PREFIX + token, user);
    }

    private void cacheUser(String token, StudentInfo studentInfo) {
        redisUtils.set(CacheConsts.TOKEN_PREFIX + token, studentInfo);
    }

    private void cacheToken(Long userId, String token) {
        redisUtils.set(CacheConsts.USERID_TOKEN_PREFIX + userId, token);
    }

    private void cacheTokenApp(Long userId, String token) {
        redisUtils.set(CacheConsts.USERID_TOKEN_PREFIX_APP + userId, token);
    }

    private void cacheTokenStu(Long userId, String token) {
        redisUtils.set(CacheConsts.USERID_TOKEN_PREFIX_STU + userId, token);
    }

    private User getUser(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        return (User) redisUtils.get(CacheConsts.TOKEN_PREFIX + token);
    }

    private UserApp getUserApp(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        return (UserApp) redisUtils.get(CacheConsts.TOKEN_PREFIX + token);
    }

    private StudentInfo getStu(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        return (StudentInfo) redisUtils.get(CacheConsts.TOKEN_PREFIX + token);
    }

    private boolean delUser(String token) {
        if (StringUtils.isBlank(token)) {
            return false;
        }
        return (Boolean) redisUtils.del(CacheConsts.TOKEN_PREFIX + token);
    }

    private boolean delToken(Long id) {
        if (id == null) {
            return false;
        }
        return (Boolean) redisUtils.del(CacheConsts.USERID_TOKEN_PREFIX + id);
    }

    private boolean delTokenApp(Long id) {
        if (id == null) {
            return false;
        }
        return (Boolean) redisUtils.del(CacheConsts.USERID_TOKEN_PREFIX_APP + id);
    }

    private boolean delTokenStu(Long id) {
        if (id == null) {
            return false;
        }
        return (Boolean) redisUtils.del(CacheConsts.USERID_TOKEN_PREFIX_STU + id);
    }

    public void putCurrent(String token, User user) {
        long id = user.getId();
        cacheUser(token, user);
        cacheToken(id, token);
    }

    public void putCurrent(String token, UserApp user) {
        long id = user.getId();
        cacheUser(token, user);
        cacheTokenApp(id, token);
    }

    public void putCurrent(String token, StudentInfo studentInfo) {
        long id = studentInfo.getId();
        cacheUser(token, studentInfo);
        cacheTokenStu(id, token);
    }

    public Long getCurrentId() {
        User user = getCurrent();
        if (user == null) {
            return null;
        }
        return user.getId();
    }

    public User getCurrent() {
        return getUser(getToken());
    }

    public UserApp getCurrentApp() {
        return getUserApp(getToken());
    }

    public StudentInfo getCurrentStu() {
        return getStu(getToken());
    }

    public boolean delCurrent() {
        String token = getToken();
        if (StringUtils.isBlank(token)) {
            return false;
        }
        User user = getUser(token);
        Long id = user.getId();
        delUser(token);
        delToken(id);
        return true;
    }

    public boolean delCurrentApp() {
        String token = getToken();
        if (StringUtils.isBlank(token)) {
            return false;
        }
        UserApp user = getUserApp(token);
        Long id = user.getId();
        delUser(token);
        delTokenApp(id);
        return true;
    }

    public boolean delCurrentStu() {
        String token = getToken();
        if (StringUtils.isBlank(token)) {
            return false;
        }
        StudentInfo user = getStu(token);
        Long id = user.getId();
        delUser(token);
        delTokenStu(id);
        return true;
    }


    public String genToken() {
        return UuidUtils.getUuid();
    }

    public String getToken() {
        return request.getHeader(CacheConsts.TOKEN);
    }

}
