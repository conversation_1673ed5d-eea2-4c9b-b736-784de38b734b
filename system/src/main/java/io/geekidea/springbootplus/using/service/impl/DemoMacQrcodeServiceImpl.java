package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.entity.DemoMacQrcode;
import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.entity.HardwareApp;
import io.geekidea.springbootplus.using.entity.QrOperation;
import io.geekidea.springbootplus.using.mapper.DemoMacQrcodeMapper;
import io.geekidea.springbootplus.using.mapper.HardwareAppMapper;
import io.geekidea.springbootplus.using.mapper.HardwareMapper;
import io.geekidea.springbootplus.using.service.DemoMacQrcodeService;
import io.geekidea.springbootplus.using.param.DemoMacQrcodePageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.using.service.HardwareAppService;
import io.geekidea.springbootplus.using.service.HardwareService;
import io.geekidea.springbootplus.using.service.QrOperationService;
import io.geekidea.springbootplus.using.vo.DemoMacQrcodeAo;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Synchronized;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class DemoMacQrcodeServiceImpl extends BaseServiceImpl<DemoMacQrcodeMapper, DemoMacQrcode> implements DemoMacQrcodeService {

    @Autowired
    private DemoMacQrcodeMapper demoMacQrcodeMapper;
    @Autowired
    private QrOperationService qrOperationService;
    @Autowired
    private HardwareService hardwareService;
    @Autowired
    private HardwareAppService hardwareAppService;
    @Autowired
    private RedisUtils redisUtils;


    @Transactional(rollbackFor = Exception.class)
    @Override
    @Synchronized
    public boolean saveDemoMacQrcode(DemoMacQrcode demoMacQrcode, Long userId) throws Exception {
        DemoMacQrcode demoMacQrcode1 = getOne(new LambdaQueryWrapper<DemoMacQrcode>().eq(DemoMacQrcode::getMac, demoMacQrcode.getMac()));
        if (demoMacQrcode1 != null) {
            removeById(demoMacQrcode1.getId());
        }

        DemoMacQrcode demoMacQrcode2 = getOne(new LambdaQueryWrapper<DemoMacQrcode>().eq(DemoMacQrcode::getQrCode, demoMacQrcode.getQrCode()));
        if (demoMacQrcode2 != null) {
            removeById(demoMacQrcode2.getId());
        }
        demoMacQrcode.setBox(demoMacQrcode.getQrCode().substring(10, 14));
        demoMacQrcode.setLongNum(getLongNum(demoMacQrcode));
        super.saveOrUpdate(demoMacQrcode);
        return qrOperationService.saveOrUpdate(qrOperationService.create(demoMacQrcode.getBox(), "match_version_add_" + demoMacQrcode.getHardType(), userId));

    }

    public String getLongNum(DemoMacQrcode demoMacQrcode) {
        Random random = new Random();
        int num = 0;
        String series = demoMacQrcode.getQrCode().substring(3, 5);
        Object filtration_hardware06 = redisUtils.hGet("filtration_hardware06", demoMacQrcode.getBox());
        Object filtration_hardware07 = redisUtils.hGet("filtration_hardware07", demoMacQrcode.getBox());
        if (series.equals("06") && filtration_hardware06 != null) {
            num = Integer.parseInt(demoMacQrcode.getQrCode().substring(14, 16));
        } else if (series.equals("07") && filtration_hardware07 != null) {
            num = Integer.parseInt(demoMacQrcode.getQrCode().substring(14, 16));
        } else {
            num = Integer.parseInt(demoMacQrcode.getQrCode().substring(14, 16), 16);
        }
        String longNum = "000";
        Integer x = 0;
        if (10 > num) {//小于10
            x = 1000 + random.nextInt(9000);
        } else if (1000 > num) {
            x = 100 + random.nextInt(900);
        } else if (100 > num) {
            x = 10 + random.nextInt(90);
        }
        longNum = longNum + x + num;
        return longNum;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateDemoMacQrcode(List<DemoMacQrcode> demoMacQrcodes, Long userId, String type) throws Exception {
        saveOrUpdateBatch(demoMacQrcodes);
        if (type.equals("match_version_update_1") || type.equals("match_version_update_2")) {
            for (DemoMacQrcode demoMacQrcode : demoMacQrcodes) {
                hardwareService.update(new LambdaUpdateWrapper<Hardware>().set(Hardware::getHardType, demoMacQrcode.getHardType()).eq(Hardware::getMac, demoMacQrcode.getMac()));
                hardwareAppService.update(new LambdaUpdateWrapper<HardwareApp>().set(HardwareApp::getHardType, demoMacQrcode.getHardType()).eq(HardwareApp::getMac, demoMacQrcode.getMac()));
            }
        }
        return qrOperationService.saveOrUpdate(qrOperationService.create(demoMacQrcodes.get(0).getBox(), type, userId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteDemoMacQrcode(List<DemoMacQrcode> demoMacQrcodes, Long userId) throws Exception {
        removeByIds(demoMacQrcodes.stream().map(DemoMacQrcode::getId).collect(Collectors.toList()));
        return qrOperationService.saveOrUpdate(qrOperationService.create(demoMacQrcodes.get(0).getBox(), "match_version_delete", userId));
    }

    @Override
    public Paging<DemoMacQrcode> getDemoMacQrcodePageList(DemoMacQrcodePageParam demoMacQrcodePageParam) throws Exception {
        Page<DemoMacQrcode> page = new PageInfo<>(demoMacQrcodePageParam, OrderItem.desc(getLambdaColumn(DemoMacQrcode::getCreateTime)));
        LambdaQueryWrapper<DemoMacQrcode> wrapper = new LambdaQueryWrapper<>();
        IPage<DemoMacQrcode> iPage = demoMacQrcodeMapper.selectPage(page, wrapper);
        return new Paging<DemoMacQrcode>(iPage);
    }

    @Override
    public DemoMacQrcodeAo findMac(String qrCode) {
        DemoMacQrcode demoMacQrcode = getOne(new LambdaQueryWrapper<DemoMacQrcode>().eq(DemoMacQrcode::getQrCode, qrCode));
        if (demoMacQrcode == null) {
            return null;
        }
        return analyzeQRCode(demoMacQrcode);
    }

    @Override
    public List<DemoMacQrcodeAo> findMacBybox(String boxCode) {
        List<DemoMacQrcodeAo> demoMacQrcodeAos = new ArrayList<>();
        List<DemoMacQrcode> demoMacQrcode = list(new LambdaQueryWrapper<DemoMacQrcode>().eq(DemoMacQrcode::getBox, boxCode));
        demoMacQrcode.forEach(e -> {
            demoMacQrcodeAos.add(analyzeQRCode(e));
        });
        return demoMacQrcodeAos;
    }

    @Override
    public List<DemoMacQrcodeAo> findMacByboxNum(String boxNum) {
        List<DemoMacQrcodeAo> demoMacQrcodeAos = new ArrayList<>();
        List<DemoMacQrcode> demoMacQrcode = list(new LambdaQueryWrapper<DemoMacQrcode>().eq(DemoMacQrcode::getBox, boxNum));
        demoMacQrcode.forEach(e -> {
            demoMacQrcodeAos.add(analyzeQRCode(e));
        });
        return demoMacQrcodeAos;
    }

    @Override
    public DemoMacQrcodeAo findByMac(String mac) {
        DemoMacQrcode demoMacQrcode = getOne(new LambdaQueryWrapper<DemoMacQrcode>().eq(DemoMacQrcode::getMac, mac));
        if (demoMacQrcode == null) {
            return null;
        }
        return analyzeQRCode(demoMacQrcode);
    }

    /**
     * @return Map<String, Object>
     * @Description: 解析二维码序列号（19位）
     * <AUTHOR>
     * @date 2018年2月6日 上午10:54:31
     */
    @Override
    public DemoMacQrcodeAo analyzeQRCode(DemoMacQrcode macQrcode) {
        DemoMacQrcodeAo demoMacQrcodeAo = new DemoMacQrcodeAo();
        String qrCode = macQrcode.getQrCode();
        demoMacQrcodeAo.setId(macQrcode.getId());
        demoMacQrcodeAo.setMac(macQrcode.getMac());
        demoMacQrcodeAo.setName(macQrcode.getName());
        demoMacQrcodeAo.setBox(qrCode.substring(10, 14));
        demoMacQrcodeAo.setHardType(macQrcode.getHardType());
        String series = qrCode.substring(3, 5);
        Object filtration_hardware06 = redisUtils.hGet("filtration_hardware06", demoMacQrcodeAo.getBox());
        Object filtration_hardware07 = redisUtils.hGet("filtration_hardware07", demoMacQrcodeAo.getBox());
        if (series.equals("06") && filtration_hardware06 != null) {
            demoMacQrcodeAo.setNum(Integer.parseInt(qrCode.substring(14, 16)));
        } else if (series.equals("07") && filtration_hardware07 != null) {
            demoMacQrcodeAo.setNum(Integer.parseInt(qrCode.substring(14, 16)));
        } else {
            demoMacQrcodeAo.setNum(Integer.parseInt(qrCode.substring(14, 16), 16));
        }
        demoMacQrcodeAo.setColour(Integer.parseInt(qrCode.substring(8, 10)));
        demoMacQrcodeAo.setQrCode(macQrcode.getQrCode());
        demoMacQrcodeAo.setLongNum(macQrcode.getLongNum());
        return demoMacQrcodeAo;
    }

    @Override
    public int getCountByBox(String box) {
        return count(new LambdaQueryWrapper<DemoMacQrcode>().eq(DemoMacQrcode::getBox, box));
    }

    @Override
    public boolean hardformatCheckCode(String qrCode) {
        if (qrCode.length() < 24) {
            return false;
        }
        return true;
    }

    @Override
    public boolean hardformatCheckMac(String mac) {
        if (mac.length() < 17) {
            return false;
        }
        return true;
    }

    @Override
    public void test() {
        List<DemoMacQrcode> demoMacQrcodes = list();
        for (DemoMacQrcode demoMacQrcode : demoMacQrcodes) {
            demoMacQrcode.setLongNum(getLongNum(demoMacQrcode));
            saveOrUpdate(demoMacQrcode);
        }
    }


}
