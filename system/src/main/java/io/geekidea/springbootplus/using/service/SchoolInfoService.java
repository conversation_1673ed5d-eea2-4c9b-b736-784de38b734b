package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.SchoolInfo;
import io.geekidea.springbootplus.using.param.SchoolInfoPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.vo.SchoolPersonCountAo;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface SchoolInfoService extends BaseService<SchoolInfo> {

    /**
     * 保存
     *
     * @param schoolInfo
     * @return
     * @throws Exception
     */
    boolean saveSchoolInfo(SchoolInfo schoolInfo) throws Exception;

    /**
     * 修改
     *
     * @param schoolInfo
     * @return
     * @throws Exception
     */
    boolean updateSchoolInfo(SchoolInfo schoolInfo) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteSchoolInfo(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param schoolInfoQueryParam
     * @return
     * @throws Exception
     */
    Paging<SchoolInfo> getSchoolInfoPageList(SchoolInfoPageParam schoolInfoPageParam) throws Exception;

    /**
     * @desc: 根据用户id获取学校
     * @author: DH
     * @date: 2022/5/5 10:41
     */
    SchoolInfo getByUserId(Long userId);

    SchoolPersonCountAo getSchoolPersonCount(Long id);

    JSONArray getNavigationText(Long id);

    Boolean setNavigationText(Long id, JSONArray jsonArray);

    List<SchoolInfo> getSchoolInfoList();

    JSONObject getSchollNameAndTeamName(Long teamId);
}
