package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import com.alibaba.fastjson.JSONObject;

/**
 * 战术板文本表，存储文本信息
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TacticsBoardText对象")
@TableName(autoResultMap = true)
public class TacticsBoardText extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("文本ID，唯一标识")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @NotNull(message = "关联的战术板ID不能为空")
    @ApiModelProperty("关联的战术板ID")
    private Long boardId;

    @ApiModelProperty("位置 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject position;

    @ApiModelProperty("是否禁止移动：0-可移动，1-不可移动")
    private Boolean noMove;

    @NotBlank(message = "文本内容不能为空")
    @ApiModelProperty("文本内容")
    private String txt;

    @ApiModelProperty("文本颜色值，存储RGB整数值")
    private Integer color;

    @ApiModelProperty("字体大小，0表示第一个选项，对应字体梯度")
    private Integer fontSize;

    @ApiModelProperty("旋转角度，0-360度")
    private Integer angle;

    @ApiModelProperty("是否有下划线：0-否，1-是")
    private Boolean underline;

    @ApiModelProperty("是否加粗：0-否，1-是")
    private Boolean bold;

    @ApiModelProperty("是否斜体：0-否，1-是")
    private Boolean italic;

    @ApiModelProperty("属于哪一帧  默认 0（第一帧）")
    private int frameIndex;//

}
