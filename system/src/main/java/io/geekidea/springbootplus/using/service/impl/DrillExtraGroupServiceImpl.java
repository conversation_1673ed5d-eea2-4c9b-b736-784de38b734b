package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.DrillExtraGroup;
import io.geekidea.springbootplus.using.mapper.DrillExtraGroupMapper;
import io.geekidea.springbootplus.using.service.DrillExtraGroupService;
import io.geekidea.springbootplus.using.param.DrillExtraGroupPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-21
 */
@Slf4j
@Service
public class DrillExtraGroupServiceImpl extends BaseServiceImpl<DrillExtraGroupMapper, DrillExtraGroup> implements DrillExtraGroupService {

    @Autowired
    private DrillExtraGroupMapper drillExtraGroupMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveDrillExtraGroup(DrillExtraGroup drillExtraGroup) throws Exception {
        return super.save(drillExtraGroup);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateDrillExtraGroup(DrillExtraGroup drillExtraGroup) throws Exception {
        return super.updateById(drillExtraGroup);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteDrillExtraGroup(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<DrillExtraGroup> getDrillExtraGroupPageList(DrillExtraGroupPageParam drillExtraGroupPageParam) throws Exception {
        Page<DrillExtraGroup> page = new PageInfo<>(drillExtraGroupPageParam, OrderItem.desc(getLambdaColumn(DrillExtraGroup::getCreateTime)));
        LambdaQueryWrapper<DrillExtraGroup> wrapper = new LambdaQueryWrapper<>();
        IPage<DrillExtraGroup> iPage = drillExtraGroupMapper.selectPage(page, wrapper);
        return new Paging<DrillExtraGroup>(iPage);
    }

    @Override
    public List<DrillExtraGroup> getDrillExtraGroup(Long drillExtaId) {
        return list(new LambdaQueryWrapper<DrillExtraGroup>().eq(DrillExtraGroup::getDrillExtraId,drillExtaId));
    }

}
