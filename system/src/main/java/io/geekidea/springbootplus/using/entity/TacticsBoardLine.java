package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 战术板线条表，存储各种线条信息
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TacticsBoardLine对象")
@TableName(autoResultMap = true)
public class TacticsBoardLine extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("线条ID，唯一标识")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @NotNull(message = "关联的战术板ID不能为空")
    @ApiModelProperty("关联的战术板ID")
    private Long boardId;

    @ApiModelProperty("是否禁止移动：0-可移动，1-不可移动")
    private Boolean noMove;

    @ApiModelProperty("线条类型：0-直线，1-曲线，2-虚线，3-自由线，4-圆，5-矩形")
    private Integer lineType;

    @ApiModelProperty("起点X坐标 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject positionStart;

    @ApiModelProperty("终点X坐标 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject positionEnd;

    @ApiModelProperty("控制点坐标 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject controlPoint;

    @ApiModelProperty("自由线的点集合 [{\"x\":1,\"y\":2}]")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray pathPointList;

    @ApiModelProperty("位置 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject position;

    @ApiModelProperty("矩形第一个点 左上 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject rectangleTopLeft;//矩形第一个点 左上

    @ApiModelProperty("矩形第2个点 右上 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject rectangleTopRight;//矩形第2个点 右上

    @ApiModelProperty("矩形第3个点 右下 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject rectangleBottomRight;//矩形第3个点 右下

    @ApiModelProperty("矩形第4个点 左下 {\"x\":1,\"y\":2}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject rectangleBottomLeft;//矩形第4个点 左下

    @ApiModelProperty("线条样式：0-实线，1-虚线_1，2-虚线_2，3-双实线，4-实虚线_1，5-实虚线_2，6-波浪线_1，7-波浪线_2")
    private Integer lineStyle;

    @ApiModelProperty("旋转角度，0-360度")
    private Integer angle;

    @ApiModelProperty("线条粗细，范围0-4")
    private Integer boldIndex;

    @ApiModelProperty("曲线平滑度，范围0-4")
    private Integer curIndex;

    @ApiModelProperty("箭头类型，0-无箭头，1-单箭头，2-双箭头等")
    private Integer arrowIndex;

    @ApiModelProperty("线条颜色值，存储RGB整数值")
    private Integer color;

    @ApiModelProperty("填充颜色值，存储RGB整数值")
    private Integer colorFill;

    @ApiModelProperty("水平半径（用于圆、矩形等）")
    private Float radiusW;

    @ApiModelProperty("垂直半径（用于圆、矩形等）")
    private Float radiusH;

    @ApiModelProperty("属于哪一帧  默认 0（第一帧）")
    private int frameIndex;//


}
