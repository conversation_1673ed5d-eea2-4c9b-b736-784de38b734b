package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.GameExtra;
import io.geekidea.springbootplus.using.param.GameExtraPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.vo.GameExtraUserAo;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
public interface GameExtraService extends BaseService<GameExtra> {

    /**
     * 保存
     *
     * @param gameExtra
     * @return
     * @throws Exception
     */
    Long saveGameExtra(GameExtra gameExtra) throws Exception;

    /**
     * 复制
     */

    boolean copyGameExtra(Long gameExtraId) throws Exception;

    /**
     * 修改
     *
     * @param gameExtra
     * @return
     * @throws Exception
     */
    boolean updateGameExtra(GameExtra gameExtra) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteGameExtra(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param gameExtraQueryParam
     * @return
     * @throws Exception
     */
    Paging<GameExtra> getGameExtraPageList(GameExtraPageParam gameExtraPageParam) throws Exception;

    List<GameExtra> getGameExtraByGameId(GameExtraPageParam drillExtraPageParam) throws Exception;

    JSONObject getGameExtrasCount(Long teamId,Long gameId, Boolean prot);

    GameExtra getGameExtra(Long teamId,Long gameExtraId);

    List<GameExtraUserAo> getGameExtraByUser(GameExtraPageParam gameExtraPageParam);

    GameExtraPageParam createGameExtraPageParam(Long gameId, Long studentId, Integer finish, Integer type,Boolean astrict,Long pi,Long ps);
}
