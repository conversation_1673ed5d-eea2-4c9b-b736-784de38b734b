package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * <AUTHOR>
 * @since 2023-11-04
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "KindergartenMonitor对象")
public class KindergartenMonitor extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("监测名")
    private String name;

    @ApiModelProperty("学校id")
    private Long schoolId;

    @ApiModelProperty("参与的班级id集合")
    @TableField(exist = false)
    private List<Long> teams;

    @ApiModelProperty("监测状态(未开始:NOTSTARTED,已开始:STARTED,已结束:FINISHED) 新添加传入未开始状态")
    private MatchStatusEnum status;

    @ApiModelProperty("监测开始时间")
    private Date startTime;

    @ApiModelProperty("监测结束时间")
    private Date stopTime;

    @ApiModelProperty("是否自动启动")
    private Boolean voluntarily;

    @ApiModelProperty("是否启动了所以班级")
    @TableField(exist = false)
    private Boolean start;

    @ApiModelProperty("是否同步了所以班级")
    @TableField(exist = false)
    private Boolean upload;

    @ApiModelProperty("未全部启动设备的班级id数组")
    @TableField(exist = false)
    private List<Long> noStartIds;

    @ApiModelProperty("未全部同步的班级id数组")
    @TableField(exist = false)
    private List<Long> noUploadIds;

    public void setNoStartIds(List<Long> noStartIds) {
        this.noStartIds = noStartIds;
    }

    public void setNoUploadIds(List<Long> noUploadIds) {
        this.noUploadIds = noUploadIds;
    }


    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
