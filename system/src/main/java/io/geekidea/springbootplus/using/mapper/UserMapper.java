package io.geekidea.springbootplus.using.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.geekidea.springbootplus.using.entity.User;
import io.geekidea.springbootplus.using.param.UserPageParam;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.io.Serializable;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-05-05
 */
@Repository
public interface UserMapper extends BaseMapper<User> {


}
