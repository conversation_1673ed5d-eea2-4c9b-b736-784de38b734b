package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * <AUTHOR>
 * @since 2022-05-05
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "User对象")
@TableName(autoResultMap = true)
public class User extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户名")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("角色 1:学校 2:老师")
    private Integer role;

    @ApiModelProperty("友盟设备唯一id")
    private String deviceTokens;

    /*
    账号的版本
    TeamTag校园数宁体育课
    1.1 标准版
    1.2 高级版
    1.3 旗舰版
    TeamTag智能篮球
    2.1 标准版
    2.2 专业版
    2.3 俱乐部版
    2.4 赛事版
    TeamTag智能足球
    3.1 少儿版
    3.2 青少年版
    3.3 成人版
    3.4 专业版
    3.5 俱乐部版
    3.6 赛事版*/
    @ApiModelProperty("   账号的版本集合 没有对应版本返回空数组 ['1.1','1.2']\n" +
            "    TeamTag校园数宁体育课\n" +
            "    1.1 标准版\n" +
            "    1.2 高级版\n" +
            "    1.3 旗舰版\n" +
            "    1.4 幼儿监测\n" +
            "    1.5 初中监测\n" +
            "    TeamTag智能篮球\n" +
            "    2.1 标准版\n" +
            "    2.2 专业版\n" +
            "    2.3 俱乐部版\n" +
            "    2.4 赛事版\n" +
            "    TeamTag智能足球\n" +
            "    3.1 少儿版\n" +
            "    3.2 青少年版\n" +
            "    3.3 成人版\n" +
            "    3.4 专业版\n" +
            "    3.5 俱乐部版\n" +
            "    3.6 赛事版")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray hdVersion;

    @NotNull(message = "创建时间不能为空")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("是否启用")
    private Boolean enable;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Integer deleted;

    @ApiModelProperty("学校信息")
    @TableField(exist = false)
    private SchoolInfo schoolInfo;

    @ApiModelProperty("老师信息 （学校登录不会有）")
    @TableField(exist = false)
    private TeacherInfo teacherInfo;

    @ApiModelProperty("登录时间")
    @TableField(exist = false)
    private Date loginDate;

    @TableField(exist = false)
    @ApiModelProperty("登录成功的token")
    private String token;

    @TableField(exist = false)
    @ApiModelProperty("学校所有人")
    private int count;

    @ApiModelProperty("设备组是否多班级模式")
    @TableField(exist = false)
    private Boolean more;
}
