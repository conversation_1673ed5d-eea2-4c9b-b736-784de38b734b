package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.MatchHardwareApp;
import io.geekidea.springbootplus.using.mapper.MatchHardwareAppMapper;
import io.geekidea.springbootplus.using.service.MatchHardwareAppService;
import io.geekidea.springbootplus.using.param.MatchHardwareAppPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Slf4j
@Service
public class MatchHardwareAppServiceImpl extends BaseServiceImpl<MatchHardwareAppMapper, MatchHardwareApp> implements MatchHardwareAppService {

    @Autowired
    private MatchHardwareAppMapper matchHardwareAppMapper;

    @Override
    public List<MatchHardwareApp> getMatchHardwareByMatchId(String matchType, Long matchId, Long teamId) {
        return list(new LambdaQueryWrapper<MatchHardwareApp>().eq(MatchHardwareApp::getMatchType, matchType).eq(MatchHardwareApp::getMatchId, matchId)
                .and(e -> e.eq(MatchHardwareApp::getTeamId, teamId).or().eq(MatchHardwareApp::getTeamId, 0)));
    }
}
