package io.geekidea.springbootplus.using.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 球队成员信息VO
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "TeamMemberVO球队成员信息")
public class TeamMemberVO {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("昵称")
    private String name;

    @ApiModelProperty("头像")
    private String headImg;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("身高（厘米）")
    private Double height;

    @ApiModelProperty("体重（kg）")
    private Double weight;

    @ApiModelProperty("性别：1-男，2-女")
    private Integer sex;

    @ApiModelProperty("篮球球衣号")
    private Integer shirt;

    @ApiModelProperty("足球球衣号")
    private Integer footballShirt;

    @ApiModelProperty("惯用脚")
    private String habit;

    @ApiModelProperty("是否队长")
    private Boolean isCaptain;

    @ApiModelProperty("是否球队创建者")
    private Boolean isCreator;

    @ApiModelProperty("加入时间")
    private Date joinTime;

    @ApiModelProperty("简介")
    private String introduction;
}
