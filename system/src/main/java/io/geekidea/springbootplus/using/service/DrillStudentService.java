package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.DrillStudent;
import io.geekidea.springbootplus.using.param.DrillStudentPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface DrillStudentService extends BaseService<DrillStudent> {

    /**
     * 保存
     *
     * @param drillStudent
     * @return
     * @throws Exception
     */
    boolean saveDrillStudent(DrillStudent drillStudent) throws Exception;

    /**
     * 修改
     *
     * @param drillStudent
     * @return
     * @throws Exception
     */
    boolean updateDrillStudent(DrillStudent drillStudent) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteDrillStudent(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param drillStudentQueryParam
     * @return
     * @throws Exception
     */
    Paging<DrillStudent> getDrillStudentPageList(DrillStudentPageParam drillStudentPageParam) throws Exception;

    boolean setStudentDrillStatus(List<DrillStudent> drillStudents);

    DrillStudent getOneByDrillIdAndStudentId(Long drillId,Long studentId);

    List<DrillStudent> getListByDrillId(Long drillId,Long teamId);

    int attendance(Long drillId,Long teamId);


    /**
     * @desc: 作废处理
     * @author: DH
     * @date: 2022/5/7 15:07
     */
    boolean setDrillCancel(List<String> macs);

   int unstartCount(Long drillId,Long teamId);
}
