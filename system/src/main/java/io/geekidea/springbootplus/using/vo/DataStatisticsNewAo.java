package io.geekidea.springbootplus.using.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.geekidea.springbootplus.using.entity.PersonData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DataStatisticsNewAo")
public class DataStatisticsNewAo {
    @ApiModelProperty("监测id")
    private Long matchId;

    @ApiModelProperty("学生id")
    private Long studentId;

    @ApiModelProperty("学生id")
    private Long schollId;

    @ApiModelProperty("学生名字")
    private String name;

    @ApiModelProperty("球队id")
    private Long teamId;

    @ApiModelProperty("球队名字")
    private String teamName;

    @ApiModelProperty("学生头像")
    private String headImg;

    @ApiModelProperty("身高（厘米）")
    private Double height;

    @ApiModelProperty("体重（kg）")
    private Double weight;

    @ApiModelProperty("设备编号")
    private Integer hardNum;

    @ApiModelProperty("设备电量")
    private Integer hardBattery;

    public void setHealth(Integer health) {
        this.health = health;
        if (health > 60) {
            healthStatus = 1;
        }else{
            healthStatus = 0;
        }
    }

    @ApiModelProperty("健康指数")
    private Integer health;

    @ApiModelProperty("健康状态")
    private Integer healthStatus;

    @ApiModelProperty("Bmi")
    private Double bmi;

    @ApiModelProperty("bmiType 1 2 3 4四个状态 1瘦 ... 4超重")
    private Integer bmiStatus;

    @ApiModelProperty("步数")
    private Integer stepCount;

    @ApiModelProperty("跨步")
    private Integer stride;

    @ApiModelProperty("起跳")
    private Integer jump;

    @ApiModelProperty("起跳跨步达标情况 1：优秀 2：良好 3：不足")
    private int strideJumpStatus;

    @JsonIgnore
    @ApiModelProperty("起跳+跨步")
    private Integer jumpStride;

    @ApiModelProperty("消耗")
    private Integer calorie;

    @ApiModelProperty("跑动")
    private Integer runDistance;

    @ApiModelProperty("运动时间")
    private Long exerciseTime;

    @ApiModelProperty("跑动运动时间")
    @JsonIgnore
    private Long runExerciseTime = 0l;

    @ApiModelProperty("步数运动时间")
    @JsonIgnore
    private Long stepExerciseTime = 0l;

    @ApiModelProperty("实际运动时间")
    private Long practicalExerciseTime = 0l;

    @ApiModelProperty("运动是否达标")
    private Boolean exerciseTimeStatus = false;

    @ApiModelProperty("达标率")
    private Integer proportiotyn;

    @JsonIgnore
    private List<PersonData> personDataList;

    @ApiModelProperty("个人曲线avg")
    private List<CurveDto> curveDtos = new ArrayList<>();

    @ApiModelProperty("运动习惯")
    private JSONObject habit;

    @ApiModelProperty("防近视")
    private JSONObject antiMyopia;

    @ApiModelProperty("运动习惯 1:优秀 2：良好 3：不足")
    private int habitStatus;

    @ApiModelProperty("防近视 1:优秀 2：良好 3：不足")
    private int antiMyopiaStatus;

    @ApiModelProperty("综合评分 ABCD")
    private String comprehensive;

    @ApiModelProperty("综合评分数字分")
    private int comprehensiveScore;

    @ApiModelProperty("Bmi ABCD")
    private String bmiComprehensive;

    @ApiModelProperty("Bmi评分数字分")
    private int bmiComprehensiveScore;

    @ApiModelProperty("运动习惯评分 ABCD")
    private String habitComprehensive;

    @ApiModelProperty("运动习惯评分数字分")
    private int habitComprehensiveScore;

    @ApiModelProperty("防近视评分 ABCD")
    private String antiMyopiaComprehensive;

    @ApiModelProperty("防近视评分数字分")
    private int antiMyopiaComprehensiveScore;

    @ApiModelProperty("长高防驼背评分 ABCD")
    private String strideJumpComprehensive;

    @ApiModelProperty("长高防驼背评分数字分")
    private int strideJumpComprehensiveScore;
}
