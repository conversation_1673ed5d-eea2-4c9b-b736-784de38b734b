package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.QrOperation;
import io.geekidea.springbootplus.using.param.QrOperationPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
public interface QrOperationService extends BaseService<QrOperation> {

    /**
     * 保存
     *
     * @param qrOperation
     * @return
     * @throws Exception
     */
    boolean saveQrOperation(QrOperation qrOperation) throws Exception;

    /**
     * 修改
     *
     * @param qrOperation
     * @return
     * @throws Exception
     */
    boolean updateQrOperation(QrOperation qrOperation) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteQrOperation(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param qrOperationQueryParam
     * @return
     * @throws Exception
     */
    Paging<QrOperation> getQrOperationPageList(QrOperationPageParam qrOperationPageParam) throws Exception;


    QrOperation create(String box,String type,Long userId);

}
