package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.using.entity.PersonData;
import io.geekidea.springbootplus.using.entity.StudentInfo;
import io.geekidea.springbootplus.using.param.StudentInfoPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.vo.PlayerAo;

import java.util.List;
import java.util.Map;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface StudentInfoService extends BaseService<StudentInfo> {


    StudentInfo getById(Long id);
    /**
     * 保存
     *
     * @param studentInfo
     * @return
     * @throws Exception
     */
    boolean saveStudentInfo(StudentInfo studentInfo) throws Exception;

    /**
     * 修改
     *
     * @param studentInfo
     * @return
     * @throws Exception
     */
    boolean updateStudentInfo(StudentInfo studentInfo) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteStudentInfo(Long id);

    boolean deleteStudentInfos(List<Long> ids);

    /**
     * 获取分页对象
     *
     * @param studentInfoQueryParam
     * @return
     * @throws Exception
     */
    Paging<StudentInfo> getStudentInfoPageList(StudentInfoPageParam studentInfoPageParam) throws Exception;

    Object getStudentInfoPageListByFieldGroup(StudentInfoPageParam studentInfoPageParam) throws Exception;

    /**
     * @desc: 学生基本信息及状态
     * @author: DH
     * @date: 2022/4/29 17:15
     */
    List<PlayerAo> getPlayerAos();

    /**
     * @desc: 球队平均年龄
     * @author: DH
     * @date: 2022/5/7 10:06
     */
    Double avgAgeByTeamId(Long teamId);

    /**
     * 学员验证
     * @param studentInfo
     * @return
     */
    StudentInfo examineName(StudentInfo studentInfo);

    /**
     * MD5验证
     * @param password
     * @param checkpwd
     * @return
     */
    Boolean checkPassword(String password, String checkpwd);

    /**
     * 学员登录
     * @param studentInfo
     * @return
     */
    StudentInfo loginStu(StudentInfo studentInfo);

    Boolean getStuHardDrillStatus(Long drillId,Long studentId,Long teamId);

    StudentInfo getStudentInfo(Long id);

    /**
     * 班级学员数量
     */
    int countByTeam(Long teamId);

    ApiCode onlyValidation(String phone, String card,Long schoolId);

    ApiCode onlyValidationShirt(StudentInfo studentInfo);

    ApiCode updaValidation(String phone,Long stuId);

    List<StudentInfo> getStusByTeamId(Long teamId);

    Boolean resetPassword(StudentInfo studentInfo);

    Boolean logoutUser(StudentInfo studentInfo) throws Exception;

}
