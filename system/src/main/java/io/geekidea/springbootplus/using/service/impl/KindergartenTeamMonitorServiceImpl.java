package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.KindergartenTeamMonitor;
import io.geekidea.springbootplus.using.mapper.KindergartenTeamMonitorMapper;
import io.geekidea.springbootplus.using.service.KindergartenTeamMonitorService;
import io.geekidea.springbootplus.using.param.KindergartenTeamMonitorPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
@Slf4j
@Service
public class KindergartenTeamMonitorServiceImpl extends BaseServiceImpl<KindergartenTeamMonitorMapper, KindergartenTeamMonitor> implements KindergartenTeamMonitorService {

    @Autowired
    private KindergartenTeamMonitorMapper kindergartenTeamMonitorMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveKindergartenTeamMonitor(KindergartenTeamMonitor kindergartenTeamMonitor) throws Exception {
        return super.save(kindergartenTeamMonitor);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateKindergartenTeamMonitor(KindergartenTeamMonitor kindergartenTeamMonitor) throws Exception {
        return super.updateById(kindergartenTeamMonitor);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteKindergartenTeamMonitor(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<KindergartenTeamMonitor> getKindergartenTeamMonitorPageList(KindergartenTeamMonitorPageParam kindergartenTeamMonitorPageParam) throws Exception {
        Page<KindergartenTeamMonitor> page = new PageInfo<>(kindergartenTeamMonitorPageParam, OrderItem.desc(getLambdaColumn(KindergartenTeamMonitor::getCreateTime)));
        LambdaQueryWrapper<KindergartenTeamMonitor> wrapper = new LambdaQueryWrapper<>();
        IPage<KindergartenTeamMonitor> iPage = kindergartenTeamMonitorMapper.selectPage(page, wrapper);
        return new Paging<KindergartenTeamMonitor>(iPage);
    }

}
