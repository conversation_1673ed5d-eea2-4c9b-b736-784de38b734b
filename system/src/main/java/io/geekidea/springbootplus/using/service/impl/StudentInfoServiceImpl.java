package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.util.MD5Utils;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.mapper.StudentInfoMapper;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.param.StudentInfoPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.vo.PlayerAo;
import org.checkerframework.checker.units.qual.A;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.text.SimpleDateFormat;
import java.util.*;

import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class StudentInfoServiceImpl extends BaseServiceImpl<StudentInfoMapper, StudentInfo> implements StudentInfoService {

    @Autowired
    private StudentInfoMapper studentInfoMapper;

    @Autowired
    private HardwareService hardwareService;

    @Autowired
    private HardwareAppService hardwareAppService;

    @Autowired
    private PersonDataService personDataService;

    @Autowired
    private TeamService teamService;

    @Autowired
    private SchoolInfoService schoolInfoService;

    @Autowired
    private TransparentMessageService transparentMessageService;

    @Autowired
    private MatchHardwareService matchHardwareService;

    @Autowired
    @Lazy
    private FieldNotesService fieldNotesService;

    @Autowired
    @Lazy
    private FieldDataService fieldDataService;

    @Autowired
    @Lazy
    private FieldGroupService fieldGroupService;

    @Autowired
    @Lazy
    private DrillService drillService;

    @Autowired
    @Lazy
    private DrillStudentService drillStudentService;

    @Autowired
    @Lazy
    private GameStudentService gameStudentService;

    @Autowired
    private UserAppService userAppService;

    @Override
    public StudentInfo getById(Long id) {
        return getBaseMapper().getById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveStudentInfo(StudentInfo studentInfo) throws Exception {
        if (studentInfo.getId() == 0) {
            studentInfo.setId(null);
        }
        if (studentInfo.getPasswd() != null) {
            studentInfo.setPasswd(MD5Utils.getSaltMD5(studentInfo.getPasswd()));
        }
        transparentMessageService.sendSchoolAllPersonCount(studentInfo.getSchoollId());
        if(studentInfo.getJoinDate()==null){
            studentInfo.setJoinDate(new Date());
        }
        return super.saveOrUpdate(studentInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateStudentInfo(StudentInfo studentInfo) throws Exception {
        if (studentInfo.getPasswd() != null) {
            studentInfo.setPasswd(MD5Utils.getSaltMD5(studentInfo.getPasswd()));
        }
        return super.saveOrUpdate(studentInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteStudentInfo(Long id) {
        Hardware hardware = hardwareService.getByStuId(id, null);
        if (hardware != null) {
            hardware.setUnbind(true);
            hardwareService.saveOrUpdate(hardware);
        }
        HardwareApp hardwareApp = hardwareAppService.getByStuId(id, null);
        if (hardwareApp != null) {
            hardwareApp.setUnbind(true);
            hardwareAppService.saveOrUpdate(hardwareApp);
        }
        StudentInfo studentInfo = getStudentInfo(id);
        if (studentInfo != null) {
            userAppService.update(new LambdaUpdateWrapper<UserApp>().set(UserApp::getHdStudentId,null).eq(UserApp::getHdStudentId,studentInfo.getId()));
            studentInfo.setPhone(studentInfo.getPhone() + "(已注销)" + id);
            studentInfo.setName("注销用户" + id);
            studentInfo.setDeleted(true);
            super.saveOrUpdate(studentInfo);
            return removeById(id);
        } else {
            return false;
        }
        /*       删除逻辑
        List<Hardware> hardwares = hardwareService.list(new LambdaQueryWrapper<Hardware>().eq(Hardware::getStudentId, id));
        List<HardwareApp> hardwareApps = hardwareAppService.list(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getStudentId, id));
        if (hardwares.size() > 0) {
            List<Long> hardIds = hardwares.stream().map(Hardware::getId).collect(Collectors.toList());
            matchHardwareService.remove(new LambdaQueryWrapper<MatchHardware>().in(MatchHardware::getHardwareId, hardIds));
            hardwareService.removeByIds(hardIds);
        }
        if (hardwareApps.size() > 0) {
            List<Long> hardIds = hardwareApps.stream().map(HardwareApp::getId).collect(Collectors.toList());
            matchHardwareService.remove(new LambdaQueryWrapper<MatchHardware>().in(MatchHardware::getHardwareId, hardIds));
            hardwareAppService.removeByIds(hardIds);
        }
        personDataService.remove(new LambdaQueryWrapper<PersonData>().eq(PersonData::getStudentId, id));
        drillStudentService.remove(new LambdaQueryWrapper<DrillStudent>().eq(DrillStudent::getStudentId, id));
        fieldDataService.remove(new LambdaQueryWrapper<FieldData>().in(FieldData::getStuId, id));
        rawDataService.remove(new LambdaQueryWrapper<RawData>().eq(RawData::getStudentId, id));*/
    }

    @Transactional
    public boolean deleteStudentInfos(List<Long> ids) {
        List<Hardware> delHard = new ArrayList<>();
        List<HardwareApp> delHardApp = new ArrayList<>();
        List<StudentInfo> delStu = new ArrayList<>();
        for (Long id : ids) {
            Hardware hardware = hardwareService.getByStuId(id, null);
            if (hardware != null) {
                hardware.setUnbind(true);
                delHard.add(hardware);
            }
            HardwareApp hardwareApp = hardwareAppService.getByStuId(id, null);
            if (hardwareApp != null) {
                hardwareApp.setUnbind(true);
                delHardApp.add(hardwareApp);
            }
            StudentInfo studentInfo = getStudentInfo(id);
            if (studentInfo != null) {
                studentInfo.setPhone(studentInfo.getPhone() + "(已注销)" + id);
                studentInfo.setName("注销用户" + id);
                studentInfo.setDeleted(true);
                delStu.add(studentInfo);
            }
        }
        if (delHard.size() > 0) {
            hardwareService.saveOrUpdateBatch(delHard);
        }
        if (delHardApp.size() > 0) {
            hardwareAppService.saveOrUpdateBatch(delHardApp);
        }
        if (delStu.size() > 0) {
            saveOrUpdateBatch(delStu);
        }
        return removeByIds(ids);
    }

    public Paging<StudentInfo> getStudentInfoPageList1(StudentInfoPageParam studentInfoPageParam) throws Exception {
        Page<StudentInfo> page = new PageInfo<>(studentInfoPageParam, OrderItem.desc(getLambdaColumn(StudentInfo::getCreateTime)));
        IPage<StudentInfo> iPage = studentInfoMapper.getStudentInfoPageList(page, studentInfoPageParam.getTeamId(), studentInfoPageParam.getDrillId(), studentInfoPageParam.getKeyword(), studentInfoPageParam.getDeleted());
        List<StudentInfo> studentInfos1 = new ArrayList<>();
        List<StudentInfo> studentInfos2 = new ArrayList<>();
        iPage.getRecords().forEach(e -> {
            if (e.getStatus() == null) {
                e.setStatus(true);
            }
            if (e.getStarter() == null) {
                e.setStarter(false);
            }
            if (e.getHardware() != null && e.getHardware().getNum() != null) {
                studentInfos1.add(e);
            } else {
                studentInfos2.add(e);
            }
        });
        studentInfos1.sort(Comparator.comparingInt(e -> e.getHardware().getNum()));
        studentInfos1.addAll(studentInfos2);
        iPage.setRecords(studentInfos1);

        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String datef = simpleDateFormat.format(date);
        iPage.getRecords().forEach(e -> {
            List<PersonData> personDatas = personDataService.list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getStudentId, e.getId())
                    .ge(PersonData::getCreateTime, datef));
            Long exerciseTime = personDatas.stream().mapToLong(PersonData::getExerciseTime).sum();
            if (exerciseTime >= 60) {
                e.setSportQualified(true);
            } else {
                e.setSportQualified(false);
            }
            e.setExerciseTime(exerciseTime);
            Team team = teamService.getById(studentInfoPageParam.getTeamId());
            if (team != null) {
                e.setTeamName(team.getFullName());
                e.setCourseId(team.getCourseId());

            }
        });
        return new Paging<StudentInfo>(iPage);
    }

    public Paging<StudentInfo> getStudentInfoPageList(StudentInfoPageParam studentInfoPageParam) throws Exception {
        Page<StudentInfo> page = new PageInfo<>(studentInfoPageParam, OrderItem.desc(getLambdaColumn(StudentInfo::getCreateTime)));
        IPage<StudentInfo> iPage = studentInfoMapper.getStudentInfoPageList(page, studentInfoPageParam.getTeamId(), studentInfoPageParam.getDrillId(), studentInfoPageParam.getKeyword(), studentInfoPageParam.getDeleted());

        List<StudentInfo> studentInfos1 = new ArrayList<>();
        List<StudentInfo> studentInfos2 = new ArrayList<>();

        iPage.getRecords().forEach(e -> {
            e.setStatus(e.getStatus() != null ? e.getStatus() : true);
            e.setStarter(e.getStarter() != null ? e.getStarter() : false);
            if (e.getHardware() != null && e.getHardware().getNum() != null) {
                studentInfos1.add(e);
            } else {
                studentInfos2.add(e);
            }
        });

        studentInfos1.sort(Comparator.comparingInt(e -> e.getHardware().getNum()));
        studentInfos1.addAll(studentInfos2);
        iPage.setRecords(studentInfos1);

        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String datef = simpleDateFormat.format(date);

        iPage.getRecords().forEach(e -> {
            List<PersonData> personDatas = personDataService.list(new LambdaQueryWrapper<PersonData>()
                    .eq(PersonData::getStudentId, e.getId())
                    .ge(PersonData::getCreateTime, datef));

            Long exerciseTime = personDatas.stream()
                    .mapToLong(PersonData::getExerciseTime)
                    .sum();

            e.setSportQualified(exerciseTime >= 60);
            e.setExerciseTime(exerciseTime);

            Team team = teamService.getById(studentInfoPageParam.getTeamId());
            if (team != null) {
                e.setTeamName(team.getFullName());
                e.setCourseId(team.getCourseId());
            }
        });
        return new Paging<>(iPage);
    }


    @Override
    public Object getStudentInfoPageListByFieldGroup(StudentInfoPageParam studentInfoPageParam) throws Exception {
        FieldNotes fieldNotes = fieldNotesService.getOne(new LambdaQueryWrapper<FieldNotes>()
                .eq(FieldNotes::getMatchId, studentInfoPageParam.getDrillId())
                .eq(FieldNotes::getMatchType, "DRILL")
                .last(" limit 0,1"));
        Drill drill = drillService.getById(studentInfoPageParam.getDrillId());
        if (fieldNotes == null) {
            return null;
        }
        List<FieldData> fieldData = fieldDataService.getGroupIdAndStuId(fieldNotes.getId());
        if (fieldData == null) {
            return null;
        }
        Map<Long, List<FieldData>> fieldDataMap = fieldData.stream()
                .collect(Collectors.groupingBy(FieldData::getGroupId));

        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String datef = simpleDateFormat.format(date);
        System.out.println(datef);
        JSONArray array = new JSONArray();
        for (Map.Entry<Long, List<FieldData>> map : fieldDataMap.entrySet()) {
            JSONObject object1 = new JSONObject();
            FieldGroup fieldGroup = fieldGroupService.getById(map.getKey());
            object1.put("groupId", fieldGroup.getId());
            object1.put("groupName", fieldGroup.getGroupName());

            List<Long> stuIds = map.getValue().stream()
                    .map(FieldData::getStuId)
                    .collect(Collectors.toList());
            List<StudentInfo> studentInfos = studentInfoMapper.getStudentInfoList(drill.getTeamId(), drill.getId(), stuIds, studentInfoPageParam.getDeleted());
            List<StudentInfo> studentInfos1 = new ArrayList<>();
            List<StudentInfo> studentInfos2 = new ArrayList<>();

            studentInfos.forEach(e -> {
                e.setStatus(e.getStatus() != null ? e.getStatus() : true);
                e.setStarter(e.getStarter() != null ? e.getStarter() : false);
                if (e.getHardware() != null && e.getHardware().getNum() != null) {
                    studentInfos1.add(e);
                } else {
                    studentInfos2.add(e);
                }
            });

            studentInfos1.sort(Comparator.comparingInt(e -> e.getHardware().getNum()));
            studentInfos1.addAll(studentInfos2);

            studentInfos1.forEach(e -> {
                List<PersonData> personDatas = personDataService.list(new LambdaQueryWrapper<PersonData>()
                        .eq(PersonData::getStudentId, e.getId())
                        .ge(PersonData::getCreateTime, datef));

                Long exerciseTime = personDatas.stream()
                        .mapToLong(PersonData::getExerciseTime)
                        .sum();

                e.setSportQualified(exerciseTime >= 60);
                e.setExerciseTime(exerciseTime);

                Team team = teamService.getById(studentInfoPageParam.getTeamId());
                if (team != null) {
                    e.setTeamName(team.getFullName());
                    e.setCourseId(team.getCourseId());

                }
            });

            object1.put("studentIds", studentInfos1);
            array.add(object1);

            if (fieldDataMap.entrySet().size() == 1) {
                FieldGroup fieldGroup1 = fieldGroupService.getOne(new LambdaQueryWrapper<FieldGroup>().eq(FieldGroup::getFieldId, fieldNotes.getId()).ne(FieldGroup::getId, fieldGroup.getId()));
                JSONObject object2 = new JSONObject();
                object2.put("groupId", fieldGroup1.getId());
                object2.put("groupName", fieldGroup1.getGroupName());
                array.add(object2);
            }

        }

        return array;
    }

    @Override
    public List<PlayerAo> getPlayerAos() {
        return null;
    }

    @Override
    public Double avgAgeByTeamId(Long teamId) {
        return getBaseMapper().avgAgeByTeamId(teamId);
    }

    @Override
    public StudentInfo examineName(StudentInfo studentInfo) {
        StudentInfo s = super.getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getPhone, studentInfo.getPhone()));
        return s;
    }

    @Override
    public Boolean checkPassword(String password, String checkpwd) {
        return MD5Utils.getSaltverifyMD5(password, checkpwd);
    }

    @Override
    public StudentInfo loginStu(StudentInfo studentInfo) {
        StudentInfo s = super.getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getPhone, studentInfo.getPhone()));
        if (s.getPasswd() == null || studentInfo.getPasswd() == null || !checkPassword(studentInfo.getPasswd(), s.getPasswd())) {
            return null;
        }

        if (studentInfo.getDeviceTokens() != null) {
            s.setDeviceTokens(studentInfo.getDeviceTokens());
            saveOrUpdate(s);
        }
        s.setHardware(hardwareService.getOne(new LambdaQueryWrapper<Hardware>().eq(Hardware::getStudentId, s.getId()).eq(Hardware::getUnbind, false)));
        s.setHardwareApp(hardwareAppService.getOne(new LambdaQueryWrapper<HardwareApp>().eq(HardwareApp::getStudentId, s.getId()).eq(HardwareApp::getUnbind, false)));
        SchoolInfo schoolInfo = schoolInfoService.getById(s.getSchoollId());
        if (schoolInfo != null) {
            s.setSchoolName(schoolInfo.getName());
            Team team = teamService.getById(s.getTeamId());
            s.setTeamName(team.getShortName());
            s.setCourseId(team.getCourseId());

        }
        return s;
    }

    @Override
    public Boolean getStuHardDrillStatus(Long drillId, Long studentId, Long teamId) {
        List<MatchHardware> matchHardwares = matchHardwareService.getMatchHardwareByMatchId(drillId, teamId, "DRILL");
        for (MatchHardware e : matchHardwares) {
            Hardware hardware = hardwareService.getById(e.getHardwareId());
            if (hardware != null && e.getStarted()) {
                return true;
            }
            HardwareApp hardwareApp = hardwareAppService.getById(e.getHardwareId());
            if (hardwareApp != null && e.getStarted()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public StudentInfo getStudentInfo(Long id) {
        StudentInfo s = super.getById(id);
        if (s != null && s.getTeamId() != null && s.getTeamId() != 0) {
            Team team = teamService.getById(s.getTeamId());
            SchoolInfo schoolInfo = schoolInfoService.getById(s.getSchoollId());
            s.setTeamName(team.getShortName());
            s.setSchoolName(schoolInfo.getName());
            s.setCourseId(team.getCourseId());
        }
        return s;
    }

    @Override
    public int countByTeam(Long teamId) {
        return count(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, teamId));
    }

    @Override
    public ApiCode onlyValidation(String phone, String card, Long schoolId) {
        StudentInfo studentInfo = null;
        studentInfo = getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getCard, card).eq(StudentInfo::getSchoollId, schoolId));
        if (studentInfo != null) {
            return ApiCode.CARD_REPETITION;
        }

        studentInfo = getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getPhone, phone));
        if (studentInfo != null) {
            return ApiCode.PHONE_REPETITION;
        }
        return null;
    }

    @Override
    public ApiCode onlyValidationShirt(StudentInfo studentInfo) {
        if (studentInfo.getShirt() != null) {
            StudentInfo studentInfo1 = getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, studentInfo.getTeamId()).eq(StudentInfo::getSchoollId, studentInfo.getSchoollId())
                    .eq(StudentInfo::getShirt, studentInfo.getShirt()).ne(StudentInfo::getId, studentInfo.getId()));
            if (studentInfo1 != null) {
                return ApiCode.SHIRT_REPETITION;
            }
        }
        if (studentInfo.getFootballShirt() != null) {
            StudentInfo studentInfo2 = getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, studentInfo.getTeamId()).eq(StudentInfo::getSchoollId, studentInfo.getSchoollId())
                    .eq(StudentInfo::getFootballShirt, studentInfo.getFootballShirt()).ne(StudentInfo::getId, studentInfo.getId()));
            if (studentInfo2 != null) {
                return ApiCode.FOOTBALLSHIRT_REPETITION;
            }
        }
        return null;
    }

    @Override
    public ApiCode updaValidation(String phone, Long stuId) {
        StudentInfo studentInfo = getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getPhone, phone));
        if (studentInfo != null) {
            if (studentInfo.getId() != stuId) {
                return ApiCode.PHONE_REPETITION;
            }
        }
        return null;
    }

    @Override
    public List<StudentInfo> getStusByTeamId(Long teamId) {
        return list(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, teamId));
    }

    @Override
    public Boolean resetPassword(StudentInfo studentInfo) {
        String password = studentInfo.getPasswd();
        studentInfo = getOne(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getPhone, studentInfo.getPhone()));
        if (studentInfo != null) {
            studentInfo.setPasswd(MD5Utils.getSaltMD5(password));
        }
        return saveOrUpdate(studentInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean logoutUser(StudentInfo user) throws Exception {
        Boolean b1 = deleteStudentInfo(user.getId());
        if (b1) {
            return true;
        } else {
            //手动强制回滚事务，这里一定要第一时间处理
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
    }


}
