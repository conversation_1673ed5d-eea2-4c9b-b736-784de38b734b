package io.geekidea.springbootplus.using.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SchoolPersonCount接口 返回对象")
public class SchoolPersonCountAo {

    @ApiModelProperty("总人数")
    private int count;
    @ApiModelProperty("男生人数")
    private int boy;
    @ApiModelProperty("女生人数")
    private int girl;
    @ApiModelProperty("班级数")
    private int team;
    @ApiModelProperty("老师数")
    private int teacher;
}
