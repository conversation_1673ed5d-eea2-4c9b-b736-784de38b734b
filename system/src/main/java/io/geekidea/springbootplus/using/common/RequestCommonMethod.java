package io.geekidea.springbootplus.using.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Component
public class RequestCommonMethod {

    @Autowired
    RestTemplate restTemplate;

    public String postVsteam(String url, JSONObject json){
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.add("Authorization","70644b77233770902140c1b64cfc1d9d");
        requestHeaders.add("content-type", "application/json");
        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(json, requestHeaders);
        ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, JSONObject.class);
        if(response.getStatusCode() != HttpStatus.OK)
          return null;
        return StringEscapeUtils.unescapeJava(response.getBody().getString("data"));
    }

    public JSON get(String url) {
        HttpHeaders requestHeaders = new HttpHeaders();
        HttpEntity<MultiValueMap> requestEntity = new HttpEntity<>(new LinkedMultiValueMap<>(),requestHeaders);
        ResponseEntity<JSONObject> response = restTemplate.exchange( url, HttpMethod.GET, requestEntity,JSONObject.class);
        if(response.getStatusCode() != HttpStatus.OK)
            return null;
        return response.getBody();
    }
}
