package io.geekidea.springbootplus.using.constant;

public class CacheConsts {
    public static final String TOKEN = "token";
    public static final String DRILLDXTRASTATUS = "drill_dxtra_status:";
    public static final String TOKEN_PREFIX = "token:";
    public static final String USERID_TOKEN_PREFIX = "userId:";
    public static final String USERID_TOKEN_PREFIX_APP = "userAppId:";
    public static final String USERID_TOKEN_PREFIX_STU = "stuId:";
    public static final String ENTERING = "entering:";
    public static final String REPEATEDREQUESTS = "repeated_requests:";
    public static final int TOKEN_LENGTH = 16;
    public static final int TOKEN_EXPIRED_TIME = 48 * 60 * 60; // second
    public static final String MOBILE_PHONE_MESSAGE_PREFIX = "mobile_phone:";
    public static final long MOBILE_PHONE_MESSAGE_TIME = 3 * 59; // second
    public static final String  DRILLPASTDELAYQUEUE = "DrillPastDelayQueue";
    public static final String  GAMEPASTDELAYQUEUE = "GamePastDelayQueue";
    public static final String  TEAMPASTDELAYQUEUE = "TeamPastDelayQueue";

    public static final String  KINDERGARTENMONITORDELAYQUEUESTART = "kindergartenMonitorDelayQueueStart:";
    public static final String  KINDERGARTENMONITORDELAYQUEUESTOP = "kindergartenMonitorDelayQueueStop:";

    public static final String  METHOD = "method:";

    public static String GETCOMPREHENSIVESCORERANKING = "method:getComprehensiveScoreRanking-";
    public static String GETCOMPREHENSIVESCORERANKINGTEAM = "method:getComprehensiveScoreRankingTeam-";

    public static String GETSCHOOLINFOLIST = "method:getSchoolInfoList";

    public static String MATCHDATAGAMELIKECOUNT="match_like_game";
    public static String MATCHDATADRILLLIKECOUNT="match_like_drill";

}
