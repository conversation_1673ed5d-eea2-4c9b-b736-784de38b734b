package io.geekidea.springbootplus.using.vo;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PersonDataTuoNiaoAo implements Serializable {

    /**
     * 球赛id
     */
    private Long matchId;

    /**
     * 步数
     */
    private int stepCount;

    /**
     * 跑动次数
     */
    private Integer runCount;

    /**
     * 最大冲刺速度(km/h)
     */
    private Double maxSprintSpeed;

    /**
     * 跑动距离
     */
    private Integer runDistance;

    /**
     * 卡路里(kcal）
     */
    private Integer calorie;

    /**
     * 团队平均卡路里
     */
    private Integer avgCalorie;

    /**
     * 运动时间（秒）
     */
    private long exerciseTime;

    /**
     * 速度数据
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON speedDataList;

    /**
     * 个人曲线
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON curveList;

    /**
     * 动作
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSON pose;

    /**
     * 个人历史消耗卡路里
     */
    private Integer historyCalorie;

    /**
     * 排名
     */
    private JSONObject ranking;

    /**
     * 记时/计数/场记 数据
     */
    private JSONObject extra;


    /**
     * 起跳次数
     */
    private Integer jumpCount;

    /**
     * 起跳平均高度 m米
     */
    private Integer jumpAvgHeight;

    /**
     * 速度步伐
     */
    private JSONObject pace;

    /**
     * 平均触地时间
     */
    private Integer avgTouchDownTime;

    /**
     * 跑步姿势
     */
    private JSONObject runningForm;

    /**
     * 着地方式
     */
    private JSONObject touchdownWay;

    /**
     * 配速
     */
    private JSONArray speedAllocation;

    /**
     * 深度数据
     */
    private JSONObject depthData;

    private JSONObject overallSpeed;

    private JSONObject overallSpeedCount;


    //最长滞空时间
    private Integer maxDuration;

    //平均滞空时间
    private Integer avgDuration;

    //最远起跳距离
    private Integer maxTakeOffDistance;

    //最大起跳高度
    private Integer maxTakeOffHeight;

}
