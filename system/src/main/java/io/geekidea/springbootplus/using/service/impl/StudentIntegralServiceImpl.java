package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.StudentIntegral;
import io.geekidea.springbootplus.using.mapper.StudentIntegralMapper;
import io.geekidea.springbootplus.using.service.StudentIntegralService;
import io.geekidea.springbootplus.using.param.StudentIntegralPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-29
 */
@Slf4j
@Service
public class StudentIntegralServiceImpl extends BaseServiceImpl<StudentIntegralMapper, StudentIntegral> implements StudentIntegralService {

    @Autowired
    private StudentIntegralMapper studentIntegralMapper;


    @Override
    public void computeIntegralAll() {

    }

    @Override
    public void computeIntegralPerson() {

    }

    @Override
    public JSONObject getIntegralLevel(Long studentId) {
        return null;
    }
}
