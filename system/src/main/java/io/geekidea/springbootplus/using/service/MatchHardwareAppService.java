package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.MatchHardware;
import io.geekidea.springbootplus.using.entity.MatchHardwareApp;
import io.geekidea.springbootplus.using.param.MatchHardwareAppPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface MatchHardwareAppService extends BaseService<MatchHardwareApp> {

    List<MatchHardwareApp> getMatchHardwareByMatchId(String matchType,Long matchId, Long teamId);
}
