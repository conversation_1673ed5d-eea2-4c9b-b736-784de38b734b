package io.geekidea.springbootplus.using.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "DailyDataAo")
public class DailyDataAo {
    @ApiModelProperty("第一个值")
    private Integer one;

    @ApiModelProperty("第二个值")
    private Integer two;
}
