package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "HardwareGroup对象")
@TableName(autoResultMap = true)
public class FacilityGroup extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("设备组名")
    private String name;

    @ApiModelProperty("学校id")
    private Long schoolId;

    @ApiModelProperty("老师id")
    private Long teacherId;

    @ApiModelProperty("设备箱编号")
    private String box;

    @ApiModelProperty("是否多班级")
    private Boolean more;

    @ApiModelProperty("剩余可绑设备 拆分模式使用")
    @TableField(exist = false)
    private Integer residue;

    @ApiModelProperty("设备组本身的硬件设备数量")
    @TableField(exist = false)
    private Integer count;

    @ApiModelProperty("已扫描连接绑定成功的数量")
    @TableField(exist = false)
    private Integer bindCount;

    @ApiModelProperty("绑定的班级id集合 结构[{\"id\":班级id,\"name\":上传不需要 返回的时候后台会填充,\"bind\":绑定设备的数量,\"count\":班级人数}]")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray teams = new JSONArray();

    @ApiModelProperty("绑定班级的数量")
    @TableField(exist = false)
    private int bindTeamCount;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
