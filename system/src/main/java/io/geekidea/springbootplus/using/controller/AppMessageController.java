package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.using.controller.BaseController;
import io.geekidea.springbootplus.using.entity.AppMessage;
import io.geekidea.springbootplus.using.entity.UserApp;
import io.geekidea.springbootplus.using.service.AppMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * APP消息控制器
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/messages")
@Module("APP消息")
@Api(value = "APP消息模块", tags = {"APP消息模块"})
public class AppMessageController extends BaseController {

    @Autowired
    private AppMessageService appMessageService;

    /**
     * 获取消息统计
     */
    @GetMapping("/count")
    @OperationLog(name = "消息统计", type = OperationLogType.INFO)
    @ApiOperation(value = "消息统计", response = Map.class)
    public ApiResult getMessageCount() throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        Map<String, Long> count = appMessageService.getMessageCount(currentUser.getId());
        return ApiResult.ok(count);
    }

    /**
     * 获取消息列表
     */
    @GetMapping("")
    @OperationLog(name = "消息列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "消息列表", response = AppMessage.class)
    public ApiResult getMessages(
            @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(value = "size", defaultValue = "20") Integer size) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        Paging<AppMessage> paging = appMessageService.getUserMessages(currentUser.getId(), page, size);
        return ApiResult.ok(paging);
    }

    /**
     * 标记消息为已读
     */
    @PutMapping("/{messageId}/read")
    @OperationLog(name = "标记消息已读", type = OperationLogType.UPDATE)
    @ApiOperation(value = "标记消息已读", response = Boolean.class)
    public ApiResult markAsRead(
            @ApiParam("消息ID") @PathVariable("messageId") Long messageId) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        boolean result = appMessageService.markAsRead(messageId, currentUser.getId());
        return ApiResult.ok(result);
    }

    /**
     * 标记所有消息为已读
     */
    @PutMapping("/read-all")
    @OperationLog(name = "标记所有消息已读", type = OperationLogType.UPDATE)
    @ApiOperation(value = "标记所有消息已读", response = Boolean.class)
    public ApiResult markAllAsRead() throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        boolean result = appMessageService.markAllAsRead(currentUser.getId());
        return ApiResult.ok(result);
    }

    /**
     * 获取待审核的申请消息列表
     */
    @GetMapping("/pending-applications")
    @OperationLog(name = "待审核申请消息列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "待审核申请消息列表", response = AppMessage.class)
    public ApiResult getPendingApplications(
            @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(value = "size", defaultValue = "20") Integer size) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }

        Paging<AppMessage> paging = appMessageService.getPendingApplicationMessages(currentUser.getId(), page, size);
        return ApiResult.ok(paging);
    }
}
