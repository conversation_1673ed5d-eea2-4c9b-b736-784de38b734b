package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TeamApp;
import io.geekidea.springbootplus.using.param.TeamAppPageParam;
import io.geekidea.springbootplus.using.param.TeamAppListParam;
import io.geekidea.springbootplus.using.param.TeamMemberPageParam;
import io.geekidea.springbootplus.using.vo.TeamAppVO;
import io.geekidea.springbootplus.using.vo.TeamMemberVO;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface TeamAppService extends BaseService<TeamApp> {
    TeamApp getTeamApp(Long id);

    /**
     * 获取球队详情（包含统计信息）
     *
     * @param id 球队ID
     * @return 球队详情
     */
    TeamAppVO getTeamAppWithStats(Long id,Long userId);

    /**
     * 保存
     *
     * @param teamApp
     * @return
     * @throws Exception
     */
    boolean saveTeamApp(TeamApp teamApp) throws Exception;

    /**
     * 修改
     *
     * @param teamApp
     * @return
     * @throws Exception
     */
    boolean updateTeamApp(TeamApp teamApp) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTeamApp(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param teamAppQueryParam
     * @return
     * @throws Exception
     */
    Paging<TeamAppVO> getTeamAppPageList(TeamAppPageParam teamAppPageParam) throws Exception;

    Boolean startedCount(String matchType,Long teamId,Boolean app);

    /**
     * 获取主队球队列表
     *
     * @param param 查询参数
     * @param currentUserId 当前用户ID
     * @return 球队列表
     * @throws Exception
     */
    Paging<TeamAppVO> getHomeTeamsList(TeamAppListParam param, Long currentUserId) throws Exception;

    /**
     * 获取用户加入的球队列表
     *
     * @param param 查询参数
     * @param currentUserId 当前用户ID
     * @return 球队列表
     * @throws Exception
     */
    Paging<TeamAppVO> getJoinedTeamsList(TeamAppListParam param, Long currentUserId) throws Exception;


    /**
     * 获取球队成员分页列表
     *
     * @param param 分页参数
     * @param currentUserId 当前用户ID
     * @return 成员分页列表
     * @throws Exception
     */
    Paging<TeamMemberVO> getTeamMembersPageList(TeamMemberPageParam param, Long currentUserId) throws Exception;

    /**
     * 删除球队成员
     *
     * @param teamId 球队ID
     * @param userId 要删除的用户ID
     * @param currentUserId 当前用户ID（队长）
     * @return 是否成功
     * @throws Exception
     */
    boolean removeMember(Long teamId, Long userId, Long currentUserId) throws Exception;

    /**
     * 解散球队
     *
     * @param teamId 球队ID
     * @param currentUserId 当前用户ID（队长）
     * @return 是否成功
     * @throws Exception
     */
    boolean dissolveTeam(Long teamId, Long currentUserId) throws Exception;

    /**
     * 退出球队
     *
     * @param teamId 球队ID
     * @param currentUserId 当前用户ID
     * @return 是否成功
     * @throws Exception
     */
    boolean quitTeam(Long teamId, Long currentUserId) throws Exception;

    /**
     * 检查用户是否为球队队长
     *
     * @param teamId 球队ID
     * @param userId 用户ID
     * @return 是否为队长
     */
    boolean isCaptain(Long teamId, Long userId);

    /**
     * 检查用户是否为球队成员
     *
     * @param teamId 球队ID
     * @param userId 用户ID
     * @return 是否为成员
     */
    boolean isMember(Long teamId, Long userId);
}
