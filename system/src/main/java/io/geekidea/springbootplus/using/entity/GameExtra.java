package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GameExtra对象")
public class GameExtra extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long teamId;

    private Long gameId;

    @ApiModelProperty("记时/计数名称")
    private String name;

    @ApiModelProperty("图片")
    private String iconUrl;

    @ApiModelProperty("1记时 2计数")
    private Integer type;

    @ApiModelProperty("记时or计数的值")
    private Long commonValue;

    @ApiModelProperty("是否结束")
    private Boolean finish;

    @ApiModelProperty("小组 ")
    @TableField(exist = false)
    private List<GameExtraGroup> group;

    @ApiModelProperty("全组排名 格式[同上 users 字段] APP不用上传")
    @TableField(exist = false)
    private JSONArray groupAll;

    @ApiModelProperty("总时长 分钟")
    @TableField(exist = false)
    private Integer duration;

    @ApiModelProperty("已进行时间 毫秒")
    @TableField(exist = false)
    private Long runningTime;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
