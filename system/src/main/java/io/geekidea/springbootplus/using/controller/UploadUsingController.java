/*
 * Copyright 2019-2029 geekidea(https://github.com/geekidea)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.util.UploadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 上传控制器
 *
 * <AUTHOR>
 * @date 2019/8/20
 * @since 1.2.1-RELEASE
 */
@Slf4j
@RestController
@RequestMapping("/using/uploadUsing")
@Module("teambox")
@Api(value = "文件上传Using", tags = {"文件上传Using"})
public class UploadUsingController {
    /**
     * @desc: 上传多个文件
     * @author: DH
     * @date: 2022/3/24 10:15
     */
    @PostMapping("/uploadFiles")
    @OperationLog(name = "上传多个文件", type = OperationLogType.UPLOAD)
    @ApiOperation(value = "上传多个文件", response = ArrayList.class,notes = "data返回url集合 [\"url\",\"url\"]")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "files", value = "文件", required = true),
            @ApiImplicitParam(name = "type", value = "microteam-video:视频，microteam-image:图片，microteam-cover:封面,tactics-board:战术板相关,file-microteam:app文件相关",required = true)
    })
    public ApiResult<Object> uploadFiles(List<MultipartFile> files, @RequestParam("type")String type) throws IOException {
        if(files.size()==0){
            return ApiResult.ok(false);
        }
        List<String> urls = new ArrayList<>();
        //上传图片/视频
        for (MultipartFile multfile : files) {
            //String fileName = multfile.getOriginalFilename();
            // 获取文件后缀名
            String suffix = multfile.getOriginalFilename().substring(multfile.getOriginalFilename().lastIndexOf("."));
            // 重新生成文件名

            String fName = UUID.randomUUID().toString() + suffix;
            final File file = File.createTempFile(UUID.randomUUID().toString(), suffix);
            multfile.transferTo(file);
            if("file-microteam".equals(type)){
                fName = multfile.getOriginalFilename();
            }
            String url = UploadUtil.uploadFile(file, type, fName);
            UploadUtil.deleteFile(file);
            urls.add(url);
        }
       return ApiResult.ok(urls);
    }

}
