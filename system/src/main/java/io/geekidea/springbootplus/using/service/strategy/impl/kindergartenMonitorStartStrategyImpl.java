package io.geekidea.springbootplus.using.service.strategy.impl;


import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.SchoolInfo;
import io.geekidea.springbootplus.using.service.TransparentMessageService;
import io.geekidea.springbootplus.using.service.strategy.TaskStrategy;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("kindergartenMonitorDelayQueueStart")
@Slf4j
public class kindergartenMonitorStartStrategyImpl implements TaskStrategy {

    @Autowired
    TransparentMessageService transparentMessageService;
    volatile JSONObject message;
    @Autowired
    RedisUtils redisUtils;
    @Override
    public void dispose(Object task) {
        message = new JSONObject();
        SchoolInfo schoolInfo = (SchoolInfo) task;
        message.put("schoolId", schoolInfo.getId());
        message.put("startTime", schoolInfo.getMonitorTime().getTime());
        message.put("stopTime", schoolInfo.getUploadTime().getTime());
        message.put("action", "kindergartenMonitor_start");
        if (transparentMessageService.sendTrasparentBykindergartenMonitor(message)) {
            redisUtils.set(CacheConsts.KINDERGARTENMONITORDELAYQUEUESTART+ schoolInfo.getId(), 1);
        }
    }
}
