package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.using.controller.BaseController;
import io.geekidea.springbootplus.using.entity.UserApp;
import io.geekidea.springbootplus.using.service.TeamJoinApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 球队入队申请控制器
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/api/team-app/applications")
@Module("球队入队申请")
@Api(value = "球队入队申请模块", tags = {"球队入队申请模块"})
public class TeamJoinApplicationController extends BaseController {

    @Autowired
    private TeamJoinApplicationService teamJoinApplicationService;

    /**
     * 审核入队申请
     */
    @PutMapping("/{applicationId}/review")
    @OperationLog(name = "审核入队申请", type = OperationLogType.UPDATE)
    @ApiOperation(value = "审核入队申请", response = Boolean.class)
    public ApiResult reviewApplication(
            @ApiParam("申请ID") @PathVariable("applicationId") Long applicationId,
            @ApiParam("是否通过") @RequestParam("approved") Boolean approved,
            @ApiParam("审核理由") @RequestParam(value = "reason", required = false) String reason) throws Exception {
        UserApp currentUser = getCurrentApp();
        if (currentUser == null) {
            return ApiResult.fail("用户未登录");
        }
        
        boolean result = teamJoinApplicationService.reviewApplication(
            applicationId, approved, reason, currentUser.getId());
        return ApiResult.ok(result);
    }
}
