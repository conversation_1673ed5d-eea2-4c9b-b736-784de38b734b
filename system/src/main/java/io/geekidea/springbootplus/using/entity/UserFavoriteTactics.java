package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 用户收藏战术板记录表
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserFavoriteTactics对象")
public class UserFavoriteTactics extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("收藏记录主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("战术板ID，关联tactics_board表的id字段")
    private Long tacticsBoardId;

    @ApiModelProperty("收藏时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}
