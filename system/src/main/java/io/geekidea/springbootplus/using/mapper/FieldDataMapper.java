package io.geekidea.springbootplus.using.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.geekidea.springbootplus.using.entity.FieldData;
import io.geekidea.springbootplus.using.param.FieldDataPageParam;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import java.io.Serializable;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Repository
public interface FieldDataMapper extends BaseMapper<FieldData> {
    IPage<FieldData> getStuField(Page<FieldData> page,@Param("stuId")Long stuId);

    IPage<FieldData> getStuFieldByMatchType(Page<FieldData> page,@Param("stuId")Long stuId,@Param("matchType")String matchType);

    FieldData getField(Page<FieldData> page,@Param("stuId")Long stuId,@Param("fieldId")Long fieldId);

    Long getStuFieldByMatchTypeAndId(@Param("stuId")Long stuId,@Param("matchId")Long matchId,@Param("matchType")String matchType);

    FieldData getFieldByMatchType(Page<FieldData> page,@Param("stuId")Long stuId,@Param("fieldId")Long fieldId,@Param("matchType")String matchType);

    List<FieldData> selectByFieldIdAndDataId(@Param("fieldId")Long fieldId,@Param("groupId")Long groupId);

    List<FieldData> getList(@Param("fieldId") Long fieldId);

    List<FieldData> getByGroupId(@Param("fieldId")Long fieldId,@Param("groupId")Long groupId);

    FieldData getByStuId(@Param("stuId") Long stuId,@Param("fieldId") Long fieldId);

    FieldData getId(Long id);

    List<FieldData> getByTime(@Param("stuId")Long stuId,@Param("startTime")String startTime,@Param("stopTime")String stopTime);

    List<FieldData> getGroupIdAndStuId(@Param("fnId")Long fnId);
}
