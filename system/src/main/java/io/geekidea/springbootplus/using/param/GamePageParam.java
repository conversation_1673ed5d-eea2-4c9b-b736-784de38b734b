package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <pre>
 *  分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GamePageParam参数")
public class GamePageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("球队id")
    private Long teamId;
    @ApiModelProperty("0未结束 1已结束 -1全部")
    private Integer finish = -1;
    @ApiModelProperty("true只查询同步过的比赛 false只查询未同步过的比赛")
    private Boolean upload = false;
    @ApiModelProperty("是否查询同步和未同步所有的比赛")
    private Boolean uploadAll = false;
    @ApiModelProperty("查询同步过的比赛")
    private Boolean finishUpload = true;
    @ApiModelProperty("课程类型id（跑动|篮球...）查询全部就不传 或者-1 2篮球 3足球")
    private Long courseId;
    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date befor;
    @ApiModelProperty("结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date after;
}
