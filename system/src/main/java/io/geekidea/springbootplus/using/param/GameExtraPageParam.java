package io.geekidea.springbootplus.using.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import io.geekidea.springbootplus.framework.core.pagination.BasePageOrderParam;

/**
 * <pre>
 *  分页参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2023-06-05
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GameExtraPageParam分页参数")
public class GameExtraPageParam extends BasePageOrderParam {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("比赛id")
    private Long gameId;
    @ApiModelProperty("查询的球队id")
    private Long teamId;
    @ApiModelProperty("用户id 查询个人需要传")
    private Long userId;
    @ApiModelProperty("0未结束 1结束 2全部")
    private Integer finish;
    @ApiModelProperty("type = -1 查询全部 1记时 2计数")
    private Integer type;

    private boolean astrict;
}
