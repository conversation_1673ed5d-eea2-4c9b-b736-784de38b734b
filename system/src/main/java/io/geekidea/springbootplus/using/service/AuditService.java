package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.Audit;
import io.geekidea.springbootplus.using.param.AuditPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
public interface AuditService extends BaseService<Audit> {

    /**
     * 保存
     *
     * @param audit
     * @return
     * @throws Exception
     */
    boolean saveAudit(Audit audit) throws Exception;

    /**
     * 修改
     *
     * @param audit
     * @return
     * @throws Exception
     */
    boolean updateAudit(Audit audit) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteAudit(Long id) throws Exception;

    JSONObject getGradeAudit(Long userId, Long teamId,String type);

    /**
     * 获取分页对象
     *
     * @param auditQueryParam
     * @return
     * @throws Exception
     */
    Paging<Audit> getAuditPageList(AuditPageParam auditPageParam) throws Exception;


    boolean gradeAudit(Audit audit);

}
