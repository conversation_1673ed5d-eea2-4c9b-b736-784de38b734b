package io.geekidea.springbootplus.using.controller;

import io.geekidea.springbootplus.using.entity.FieldData;
import io.geekidea.springbootplus.using.service.FieldDataService;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.FieldDataPageParam;
import io.geekidea.springbootplus.framework.common.controller.BaseController;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.common.param.IdParam;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import io.geekidea.springbootplus.framework.core.validator.groups.Add;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Slf4j
@RestController
@RequestMapping("/fieldData")
@Module("${cfg.module}")
public class FieldDataController extends BaseController {

    @Autowired
    private FieldDataService fieldDataService;

    /**
     * 添加·
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class)
    public ApiResult<Boolean> addFieldData(@Validated(Add.class) @RequestBody FieldData fieldData) throws Exception {
        boolean flag = fieldDataService.saveFieldData(fieldData);
        return ApiResult.result(flag);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(name = "修改", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改", response = ApiResult.class)
    public ApiResult<Boolean> updateFieldData(@Validated(Update.class) @RequestBody FieldData fieldData) throws Exception {
        boolean flag = fieldDataService.updateFieldData(fieldData);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteFieldData(@PathVariable("id") Long id) throws Exception {
        boolean flag = fieldDataService.deleteFieldData(id);
        return ApiResult.result(flag);
    }


    /**
     * 获取详情
     */
    @GetMapping("/info/{id}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = FieldData.class)
    public ApiResult<FieldData> getFieldData(@PathVariable("id") Long id) throws Exception {
        FieldData fieldData = fieldDataService.getById(id);
        return ApiResult.ok(fieldData);
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = FieldData.class)
    public ApiResult<Paging<FieldData>> getFieldDataPageList(@Validated @RequestBody FieldDataPageParam fieldDataPageParam) throws Exception {
        Paging<FieldData> paging = fieldDataService.getFieldDataPageList(fieldDataPageParam);
        return ApiResult.ok(paging);
    }


//    /**
//     * 查看单个学生的场记数据
//     * @param fieldId   场记id
//     * @param stuId     学生id
//     * @return
//     */
//    @GetMapping("/getOneData/{fieldId}/{stuId}")
//    @OperationLog(name = "单个学员场记数据")
//    @ApiOperation(value = "单个学员场记数据", response = Object.class,notes = "返回结果 dataMap 是json类型")
//    public ApiResult<FieldData> getOneData(@PathVariable("fieldId") Long fieldId, @PathVariable("stuId")Long stuId){
//        FieldData fieldData = fieldDataService.getByStuId(fieldId,stuId);
//        return ApiResult.ok(fieldData);
//    }

//    /**
//     * 计算小组的场记数据
//     * @param fieldId
//     * @return
//     */
//    @GetMapping("/getDataList/{fieldId}")
//    @OperationLog(name = "小组场记数据")
//    @ApiOperation(value = "小组场记数据",response = Object.class,notes = "返回结果 dataMap 是json类型")
//    public ApiResult<FieldData> getDataList(@PathVariable("fieldId") Long fieldId){
//        FieldData fieldData = fieldDataService.getDataList(fieldId);
//        return ApiResult.ok(fieldData);
//    }
}

