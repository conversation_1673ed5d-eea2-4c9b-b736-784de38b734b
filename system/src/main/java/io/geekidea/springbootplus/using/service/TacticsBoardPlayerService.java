package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.TacticsBoardPlayer;
import io.geekidea.springbootplus.using.param.TacticsBoardPlayerPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 * 战术板球员表，存储球员信息 服务类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface TacticsBoardPlayerService extends BaseService<TacticsBoardPlayer> {

    /**
     * 保存
     *
     * @param tacticsBoardPlayer
     * @return
     * @throws Exception
     */
    boolean saveTacticsBoardPlayer(TacticsBoardPlayer tacticsBoardPlayer) throws Exception;

    /**
     * 修改
     *
     * @param tacticsBoardPlayer
     * @return
     * @throws Exception
     */
    boolean updateTacticsBoardPlayer(TacticsBoardPlayer tacticsBoardPlayer) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteTacticsBoardPlayer(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param tacticsBoardPlayerQueryParam
     * @return
     * @throws Exception
     */
    Paging<TacticsBoardPlayer> getTacticsBoardPlayerPageList(TacticsBoardPlayerPageParam tacticsBoardPlayerPageParam) throws Exception;

}
