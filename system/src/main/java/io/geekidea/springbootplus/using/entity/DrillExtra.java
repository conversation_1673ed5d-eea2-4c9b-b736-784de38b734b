package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;

/**
 * 
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DrillExtra")
@TableName(autoResultMap = true)
public class DrillExtra extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空")
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("训练id")
    private Long drillId;

    @ApiModelProperty("记时/计数名称")
    private String name;

    @ApiModelProperty("图片Url")
    private String iconUrl;

    @ApiModelProperty("1记时 2计数")
    private Integer type;

    @ApiModelProperty("记时or计数的值")
    private Long commonValue;

    @ApiModelProperty("是否结束")
    private Boolean finish;

    @ApiModelProperty("小组 ")
    @TableField(exist = false)
    private List<DrillExtraGroup> group;

    @ApiModelProperty("全组排名 格式[同上 students 字段] APP不用上传")
    @TableField(exist = false)
    private JSONArray groupAll;

    @ApiModelProperty("总时长 分钟")
    @TableField(exist = false)
    private Integer duration;

    @ApiModelProperty("已进行时间 毫秒")
    @TableField(exist = false)
    private Long runningTime;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;
}
