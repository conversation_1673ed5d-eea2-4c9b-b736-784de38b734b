package io.geekidea.springbootplus.using.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.geekidea.springbootplus.framework.aop.annotation.TextCensorUserDefined;
import io.geekidea.springbootplus.framework.util.RedisUtils;
import io.geekidea.springbootplus.using.constant.CacheConsts;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.param.DrillExtraPageParam;
import io.geekidea.springbootplus.using.param.FieldDataPageParam;
import io.geekidea.springbootplus.using.param.FieldNotesPageParam;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.vo.*;
import lombok.extern.slf4j.Slf4j;
import io.geekidea.springbootplus.using.param.DrillPageParam;
import io.geekidea.springbootplus.framework.common.api.ApiResult;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.log.annotation.Module;
import io.geekidea.springbootplus.framework.log.annotation.OperationLog;
import io.geekidea.springbootplus.framework.log.enums.OperationLogType;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@RestController
@RequestMapping("/using/drill")
@Module("teambox")
@Api(value = "训练相关", tags = {"训练相关"})
public class DrillController extends BaseController {

    @Autowired
    private DrillService drillService;
    @Autowired
    private DrillStudentService drillStudentService;
    @Autowired
    private DrillExtraService drillExtraService;
    @Autowired
    private IconService iconService;
    @Autowired
    private FieldDataService fieldDataService;
    @Autowired
    private FieldNotesService fieldNotesService;
    @Autowired
    private PersonDataService personDataService;
    @Autowired
    TransparentMessageService transparentMessageService;
    @Autowired
    RedisUtils redisUtils;


    /**
     * 添加
     */
    @PostMapping("/add")
    @OperationLog(name = "添加", type = OperationLogType.ADD)
    @ApiOperation(value = "添加", response = ApiResult.class, notes = "")
    @TextCensorUserDefined
    public ApiResult<Object> addDrill(@RequestBody Drill drill) throws Exception {
        drillService.saveDrill(drill);
        return ApiResult.ok(drill.getId());
    }

    /**
     * 修改（上传训练模式可用）
     */
    @PostMapping("/update")
    @OperationLog(name = "修改（上传训练模式可用）", type = OperationLogType.UPDATE)
    @ApiOperation(value = "修改（上传训练模式可用）", response = ApiResult.class)
    @TextCensorUserDefined
    public ApiResult<Boolean> updateDrill(@RequestBody Drill drill) throws Exception {
        boolean flag = drillService.updateDrill(drill);
        return ApiResult.result(flag);
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    @OperationLog(name = "删除", type = OperationLogType.DELETE)
    @ApiOperation(value = "删除", response = ApiResult.class)
    public ApiResult<Boolean> deleteDrill(@PathVariable("id") Long id) throws Exception {
        boolean flag = drillService.deleteDrill(id);
        return ApiResult.result(flag);
    }

    /**
     * 获取详情
     */
    @GetMapping("/info/{id}/{teamId}")
    @OperationLog(name = "详情", type = OperationLogType.INFO)
    @ApiOperation(value = "详情", response = Drill.class)
    public ApiResult<Drill> getDrill(@PathVariable("id") Long id, @PathVariable("teamId") Long teamId) throws Exception {
        return ApiResult.ok(drillService.getDrill(id, teamId));
    }

    /**
     * 分页列表
     */
    @PostMapping("/getPageList/{teamId}/{finish}/{courseId}")
    @OperationLog(name = "分页列表", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表", response = Drill.class, notes = "courseId训练类型 传-1查全部 finish -1:查询全部 0：未完成 1：已完成 返回是数组")
    public ApiResult<Object> getDrillPageList(@Validated @RequestBody DrillPageParam drillPageParam, @PathVariable("teamId") Long teamId, @PathVariable("finish") Integer finish, @PathVariable("courseId") Long courseId) throws Exception {
        Paging<Drill> paging = drillService.getDrillPageList(drillPageParam, teamId, finish, courseId);
        return ApiResult.ok(paging.getRecords());
    }

    /**
     * 分页列表(根据学生id查)
     */
    @PostMapping("/getPageListByStu/{studentId}/{finish}")
    @OperationLog(name = "分页列表(根据学生id查) ", type = OperationLogType.PAGE)
    @ApiOperation(value = "分页列表(根据学生id查)", response = Drill.class, notes = "finish -1:查询全部 0：未完成 1：已完成 返回是数组  finish先随便给")
    public ApiResult<Object> getPageListByStu(@Validated @RequestBody DrillPageParam drillPageParam, @PathVariable("studentId") Long studentId, @PathVariable("finish") Integer finish) throws Exception {
        Paging<Drill> paging = drillService.getDrillPageListByStu(drillPageParam, studentId, finish);
        List list = paging.getRecords();
        if (list.size() == 0) {
            return ApiResult.ok(new ArrayList<>());
        }
        return ApiResult.ok(list);
    }

    /**
     * 查询学生训练列表 PAD和个人版都可以使用
     */
    @PostMapping("/getDrillListByStu")
    @OperationLog(name = "查询训练列表 PAD和个人版都可以使用", type = OperationLogType.PAGE)
    @ApiOperation(value = "查询训练列表 PAD和个人版都可以使用", response = Drill.class)
    public ApiResult<Object> getDrillListByStu(@Validated @RequestBody DrillPageParam drillPageParam) throws Exception {
        Paging<Drill> paging = drillService.getDrillPageListByStu1(drillPageParam);
        List list = paging.getRecords();
        if (list.size() == 0) {
            return ApiResult.ok(new ArrayList<>());
        }
        return ApiResult.ok(list);
    }

    @GetMapping("/stopDrill/{drillId}")
    @OperationLog(name = "结束训练")
    @ApiOperation(value = "结束训练", response = Boolean.class, notes = "")
    public ApiResult<Boolean> stopDrill(@PathVariable("drillId") Long drillId) throws Exception {
        return ApiResult.result(drillService.stopDrill(drillId));
    }

    @PostMapping("/setStudentDrillStatus")
    @OperationLog(name = "设置学员出勤状态")
    @ApiOperation(value = "设置学员出勤状态", response = Boolean.class, notes = "")
    public ApiResult<Boolean> setStudentDrillStatus(@RequestBody List<DrillStudent> drillStudents) throws Exception {
        boolean flag = drillStudentService.setStudentDrillStatus(drillStudents);
        return ApiResult.result(flag);
    }

    @GetMapping("/getStudentDrillStatus/{drillId}/{teamId}")
    @OperationLog(name = "获取学员出勤状态")
    @ApiOperation(value = "获取学员出勤状态", response = DrillStudent.class, notes = "")
    public ApiResult getStudentDrillStatus(@PathVariable("drillId") Long drillId, @PathVariable("teamId") Long teamId) throws Exception {
        return ApiResult.ok(drillStudentService.getListByDrillId(drillId, teamId));
    }

    @GetMapping("/isSetStatus/{drillId}/{teamId}")
    @OperationLog(name = "训练是否设置过出勤")
    @ApiOperation(value = "训练是否设置过出勤", response = Boolean.class, notes = "返回true 代表设置过")
    public ApiResult isSetStatus(@PathVariable("drillId") Long drillId, @PathVariable("t" +
            "eamId") Long teamId) throws Exception {
        return ApiResult.ok(drillStudentService.getListByDrillId(drillId, teamId).size() > 0);
    }

    @PostMapping(value = "/uploadData/{drillId}")
    @OperationLog(name = "上传数据")
    @ApiOperation(value = "上传数据", response = Boolean.class, notes = "body与PAD一模一样 \n" +
            "上传额外增加字段 avgTouchDownTime:平均触地时间 单位 s " + "\n " +
            "speedAllocation:[11,22] 配速\n" +
            "wholeMoveDistance:111 总距离m\n" +
            "jumpCount:111 起跳次数\n" +
            "jumpAvgHeight:111 平均起跳高度 cm\n" +
            "liveness:[1,2,3,4,5] 活跃度\n" +
            "livenessMinute:[1,2,3,4,5] 1分钟间隔的活跃度数组\n" +
            "hardwareDataPojoList下添加字段 stepList :[{\"startTime\":开始时间戳,\"endTime\":结束时间戳,\"stepCount\":步数}] 步数数组\n" +
            "hardwareDataPojoList下添加字段 exceedMoveList :大于18-24km/h的的速度数组 参考lowMoveList字段\n" +
            "hardwareDataPojoList下添加字段 supMoveList : 大于24km/h的的速度数组 参考lowMoveList字段\n" +
            "hardwareDataPojoList下添加字段 supCount:1 大于25km/h的次数\n" +
            "hardwareDataPojoList下添加字段 exceedCount:1 大于20km/h的次数\n" +
            "runningForm:{\"avgTouchLandTime\":平均触地时间 单位 毫秒,\"avgTouchdownImpact\":平均着地冲击 单位 g,\"avgEctropionRange:\"平均外翻幅度 单位 °:,\"avgPendulumAngle\":平均摆动 单位 °} 跑步姿势\n" +
            "touchdownWay:{\"front\":前脚 单位%,\"whole\":全脚 单位%,\"queen\":脚跟 单位%} 着地方式 \n" +
            "maxDuration:最长滞空时间 单位ms\n" +
            "avgDuration:平均滞空时间 单位ms\n" +
            "maxTakeOffDistance:最远起跳距离 单位cm\n" +
            "maxTakeOffHeight:最大起跳高度 单位cm\n" +
            "depthData:{\"leftTurning\":左变向次数,\"rightTurning\":右变向次数,\"breakOut\":爆发次数,\"starting\":启动次数,\"brake\":制动次数} 深度数据\n"+
            "hardType:1：标准版 2：专业版\n")
    public ApiResult<Boolean> uploadData(@RequestBody JSONArray strJSONArray, @PathVariable("drillId") Long drillId) {
        boolean flag = drillService.uploadData(strJSONArray.toJSONString(), drillId);
        return ApiResult.result(flag);
    }

    @GetMapping(value = "/getPlayerData/{matchId}/{studentId}/{teamId}")
    @OperationLog(name = "获取数据")
    @ApiOperation(value = "获取数据", response = Object.class, notes = "返回data与PAD一模一样  studentId传-1就是查团队  \n" +
            "团队数据增加额外返回字段\n " +
            "extra:{\"timing\":[记时-- 参考查看计数/记时 接口],\"numeration\":[计数--参考查看计数/记时接口],\"fieldGroup\":[查看场记接口]} " +
            "sumStep:总步数 sumCalorie:总卡路里\n" +
            "pace:{\"avgSpeedAllocation\":总平均配速/km 时间戳,\"avgSpeed\":总平均速度/小时,\"avgStrideFrequency\":总平均步频/分钟,\"avgStride\":总平均步幅/厘米} 速度步伐\n" +
            "runningForm:{\"avgTouchLandTime\":总平均触地时间 单位 毫秒,\"avgTouchdownImpact\":总平均着地冲击 单位 g,\"avgEctropionRange\":总平均外翻幅度 单位 °,\"avgPendulumAngle\":总平均摆动 单位 °} 跑步姿势\n" +
            "touchdownWay:{\"front\":前脚 单位%,\"whole\":全脚 单位%,\"queen\":脚跟 单位%} 团队着地方式 \n" +
            "schoolLog\"学校log\"\n" +
            "schoolPub:\"学校公众号\"\n" +
            "jumpAvgHeight:111 平均起跳高度 cm\n" +
            "maxDuration:最长滞空时间 单位ms\n" +
            "avgDuration:平均滞空时间 单位ms\n" +
            "maxTakeOffDistance:最远起跳距离 单位cm\n" +
            "maxTakeOffHeight:最大起跳高度 单位cm\n" +
            "runDistances:[{\"studentId\":学生id,\"footballShirt\":足球球衣,\"shirt\":篮球球衣,\"card\":学号,\"name\":学生姓名,\"run\":跑动值m,\"avg\":平均跑动m/min}]\n" +
            "maxSprintSpeeds:[{\"studentId\":学生id,\"headImg\":头像,\"footballShirt\":足球球衣,\"shirt\":篮球球衣,\"card\":学号,\"name\":学生姓名,\"value\":最大冲刺速度(km/h)}]\n" +
            "leftOrRightTurnings:[{\"studentId\":学生id,\"headImg\":头像,\"footballShirt\":足球球衣,\"shirt\":篮球球衣,\"card\":学号,\"name\":学生姓名,\"left\":左变向次数,\"right\":右变向次数}]\n" +
            "breakOuts:[{\"studentId\":学生id,\"headImg\":头像,\"footballShirt\":足球球衣,\"shirt\":篮球球衣,\"card\":学号,\"name\":学生姓名,\"breakOut\":爆发次数}]\n" +
            "startingsOrBrakes:[{\"studentId\":学生id,\"headImg\":头像,\"footballShirt\":足球球衣,\"shirt\":篮球球衣,\"card\":学号,\"name\":学生姓名,\"starting\":启动次数,\"brake\":制动次数}]\n" +
            "overallSpeeds:[{\"studentId\":学生id,\"headImg\":头像,\"footballShirt\":足球球衣,\"shirt\":篮球球衣,\"card\":学号,\"name\":学生姓名,\"LOW\":0-6km/h的距离,\"MID\":5-15km/h的距离,\"HIGH\":15-18,\"EXCEED\":18-24km/h的距离,\"SUP\":大于24km/h的距离}] 单位m\n" +
            "overallSpeedCounts:[{\"studentId\":学生id,\"headImg\":头像,\"footballShirt\":足球球衣,\"shirt\":篮球球衣,\"card\":学号,\"name\":学生姓名,\"exceedCount\":大于20km/h的速度次数,\"supCount\":大于25km/h的次数}]\n" +
            "teamAvgDepthData:{\"leftTurning\":左变向次数,\"rightTurning\":右变向次数,\"breakOut\":爆发次数,\"starting\":启动次数,\"brake\":制动次数} 此字段是团队平均值 没有球队或班级时，团队的平均数据都给-1\n" +
            "teamAvgOverallSpeed:{\"LOW\":0-6km/h的距离,\"MID\":5-15km/h的距离,\"HIGH\":15-18,\"EXCEED\":18-24km/h的距离,\"SUP\":大于24km/h的距离} 单位m 此字段是团队平均值 没有球队或班级时，团队的平均数据都给-1\n" +
            "teamAvgOverallSpeedCount:{\"exceedCount\":大于20km/h的速度次数,\"supCount\":大于25km/h的次数} 此字段是团队平均值 没有球队或班级时，团队的平均数据都给-1\n" +
            "teamAvgMaxSprintSpeed:团队最高冲刺速度平均值\n" +
            "个人数据增加额外返回字段\n" +
            "extra:{\"timingOrNumeration\":[记时计数-- 查询学生训练的记时记数 接口],\"fieldGroup\":[参考个人场记列表数据接口],\"timing\":[记时-- 查询学生训练的记时记数接口],\"numeration\":[计数--查询学生训练的记时记数接口]}\n" +
            "pace:{\"avgSpeedAllocation\":平均配速/km 时间戳,\"avgSpeed\":平均速度/小时,\"avgStrideFrequency\":平均步频/分钟:,\"avgStride\":平均步幅/厘米 速度步伐\n" +
            "runningForm:{\"avgTouchLandTime\":平均触地时间 单位 毫秒,\"avgTouchdownImpact\":平均着地冲击 单位 g,\"avgEctropionRange\":平均外翻幅度 单位 °,\"avgPendulumAngle\":平均摆动 单位 °,\"avgPendulumAngleStatus\":平均摆动状态 -1偏小 0正常 1偏大 其他三个状态的字段都是对应字段后面加上‘Status’} 跑步姿势\n" +
            "touchdownWay:{\"front\":前脚 单位%,\"whole\":全脚 单位%,\"queen\":脚跟 单位%} 着地方式 \n" +
            "speedAllocation:[11,22] 5公里配速数组 单位秒\n" +
            "jumpAvgHeight:111 平均起跳高度 cm\n" +
            "maxDuration:最长滞空时间 单位ms\n" +
            "avgDuration:平均滞空时间 单位ms\n" +
            "maxTakeOffDistance:最远起跳距离 单位cm\n" +
            "maxTakeOffHeight:最大起跳高度 单位cm\n" +
            "curveList字段新增 PERSON_STRIDE_FREQUENCY 步频 单位 步/分钟 最大步频 取maxY 平均步频 取pace的avgStrideFrequency\n" +
            "ranking字段新增返回highDistance 高速跑动距离的排名\n" +
            "schoolLog\"学校log\"\n" +
            "schoolPub:\"学校公众号\"\n" +
            "fieldGroupId:\"所在场记id\"\n" +
            "depthData:{\"leftTurning\":左变向次数,\"rightTurning\":右变向次数,\"breakOut\":爆发次数,\"starting\":启动次数,\"brake\":制动次数} \n" +
            "overallSpeed:{\"LOW\":0-6km/h的距离,\"MID\":5-15km/h的距离,\"HIGH\":15-18,\"EXCEED\":18-24km/h的距离,\"SUP\":大于24km/h的距离} 单位m\n" +
            "overallSpeedCount:{\"exceedCount\":大于20km/h的速度次数,\"supCount\":大于25km/h的次数}\n" +
            "teamAvgDepthData:{\"leftTurning\":左变向次数,\"rightTurning\":右变向次数,\"breakOut\":爆发次数,\"starting\":启动次数,\"brake\":制动次数} 此字段是团队||学生创建（历史平均）平均值\n" +
            "teamAvgOverallSpeed:{\"LOW\":0-6km/h的距离,\"MID\":5-15km/h的距离,\"HIGH\":15-18,\"EXCEED\":18-24km/h的距离,\"SUP\":大于24km/h的距离} 单位m 此字段是团队||学生创建（历史平均）平均值\n" +
            "teamAvgOverallSpeedCount:{\"exceedCount\":大于20km/h的速度次数,\"supCount\":大于25km/h的次数} 此字段是团队||学生创建（历史平均）平均值\n" +
            "teamAvgMaxSprintSpeed:团队最高冲刺速度平均值 此字段是团队||学生创建（历史平均）平均值\n" +
            "matchRemark:赛后点评 参考点评模块解释\n"+
            "distances:{\"low\":低速,\"mid\":中速,\"high\":高速} 高中低速度m显示\n" +
            "hardType:1：标准版 2：专业版")
    public ApiResult<Object> getPlayerData(@PathVariable("matchId") Long matchId, @PathVariable(value = "studentId", required = false) Long studentId, @PathVariable("teamId") Long teamId) {
        String token = getToken();
        Boolean app = false;
        Object o = redisUtils.get(CacheConsts.TOKEN_PREFIX + token);
        if (o != null && o instanceof UserApp) {
            app = true;
        }
        return ApiResult.ok(drillService.getPlayerData(matchId, studentId, teamId, app));
    }

    @GetMapping(value = "/getPhysicalAnalysis/{matchId}/{teamId}")
    @OperationLog(name = "体能分析排名")
    @ApiOperation(value = "体能分析排名", response = Object.class, notes = "返回data与PAD一模一样 增加返回字段 footballShirt：足球球衣 shirt：篮球球衣 id：学生id")
    public ApiResult<Object> getPhysicalAnalysis(@PathVariable("matchId") Long matchId, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(drillService.getPhysicalAnalysis(matchId, teamId));
    }

    @GetMapping(value = "/moveDistanceRanking/{matchId}/{key}/{teamId}")
    @OperationLog(name = "跑动排名")
    @ApiOperation(value = "跑动排名", response = Object.class, notes = "返回data与PAD一模一样 参数key: " +
            "wholeMoveDistance：总跑动 (m)\n" +
            "highMoveDistance：高速跑动 (m)\n" +
            "midMoveDistance：中速跑动 (m)\n" +
            "lowMoveDistance：低速跑动 (m)\n" +
            "maxSprintSpeed：最高冲刺速度 (km/h) （转换成百分比保留一位小数） 增加返回字段 footballShirt：足球球衣 shirt：篮球球衣")
    public ApiResult<Object> moveDistanceRanking(@PathVariable("matchId") Long matchId, @PathVariable("key") String key, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(drillService.moveDistanceRanking(matchId, key, null, teamId));
    }

    @GetMapping("/trainingPlan/{drillId}/{teamId}")
    @OperationLog(name = "球员训练进度")
    @ApiOperation(value = "球员训练进度", response = PlayerAo.class, notes = "返回的是集合")
    public ApiResult<List<PlayerAo>> trainingPlan(@PathVariable("drillId") Long drillId, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(drillService.trainingPlan(drillId, teamId));
    }

    @GetMapping("/unUploadOfDrill/{teamId}/{drillId}")
    @OperationLog(name = "获取上一场未同步赛事的赛事", type = OperationLogType.INFO)
    @ApiOperation(value = "获取上一场未同步赛事的赛事", response = Drill.class)
    public ApiResult<Drill> unUploadOfDrill(@PathVariable("teamId") Long teamId, @PathVariable("drillId") Long drillId) {
        return ApiResult.ok(drillService.unUploadOfDrill(teamId, drillId));
    }

    @GetMapping("/unUploadOfDrillStu/{stuId}/{drillId}")
    @OperationLog(name = "获取上一场未同步赛事的赛事(APP端)", type = OperationLogType.INFO)
    @ApiOperation(value = "获取上一场未同步赛事的赛事（APP端）", response = Drill.class)
    public ApiResult<Drill> unUploadOfDrillStu(@PathVariable("stuId") Long stuId, @PathVariable("drillId") Long drillId) {
        return ApiResult.ok(drillService.unUploadOfDrillStu(stuId, drillId));
    }

    @PostMapping("/addDrillExtra")
    @OperationLog(name = "添加计数/记时 (修改的话传上id) 上传实际数据也用这个")
    @ApiOperation(value = "添加计数/记时（修改的话传上id）上传实际数据也用这个", response = ApiResult.class, notes = "创建后会返回创建的id")
    public ApiResult<Object> addDrillExtra(@RequestBody DrillExtra drillExtra) throws Exception {
        return ApiResult.ok(drillExtraService.saveDrillExtra(drillExtra));
    }

    @PostMapping("/copyDrillExtra/{drillExtraId}")
    @OperationLog(name = "复制计数/记时 ")
    @ApiOperation(value = "复制计数/记时", response = ApiResult.class)
    public ApiResult<Object> copyDrillExtra(@PathVariable("drillExtraId") Long drillExtraId) throws Exception {
        return ApiResult.ok(drillExtraService.copyDrillExtra(drillExtraId));
    }

    @PostMapping("/deleteDrillExtra/{drillExtraId}")
    @OperationLog(name = "删除计数/记时 ")
    @ApiOperation(value = "删除计数/记时", response = ApiResult.class)
    public ApiResult<Object> deleteDrillExtra(@PathVariable("drillExtraId") Long drillExtraId) throws Exception {
        return ApiResult.ok(drillExtraService.removeById(drillExtraId));
    }

    @PostMapping("/getDrillExtras")
    @OperationLog(name = "查看计数/记时(查看团队的数据也可以调用此接口)")
    @ApiOperation(value = "查看计数/记时", response = DrillExtra.class, notes = "")
    public ApiResult<Object> getDrillExtras(@RequestBody DrillExtraPageParam drillExtraPageParam) throws Exception {
        return ApiResult.ok(drillExtraService.getDrillExtraByDrillId(drillExtraPageParam));
    }

    @GetMapping("/getDrillExtrasCount/{drillId}")
    @OperationLog(name = "查看计数/记时/场记数量")
    @ApiOperation(value = "查看计数/记时/场记数量", response = ApiResult.class, notes = "drillId-训练id  data:{\"1\":\"计时数量\",\"2\":\"计数数量\",\"3\":\"场记数量\",\"4\":\"训练类型\",\"5\":\"已进行时间 毫秒\",\"6\":\"班级名\",\"7\":\"最新场记id 没有返回-1\",\"8\":\"参考训练状态\"}")
    public ApiResult<Object> getDrillExtrasCount(@PathVariable("drillId") Long drillId) throws Exception {
        return ApiResult.ok(drillExtraService.getDrillExtrasCount(drillId, true));
    }

    @GetMapping("/getDrillExtra/{drillExtraId}")
    @OperationLog(name = "根据id获取计数/记时详细数据")
    @ApiOperation(value = "根据id获取计数/记时详细数据", response = DrillExtra.class, notes = "")
    public ApiResult<Object> getDrillExtra(@PathVariable("drillExtraId") Long drillExtraId) throws Exception {
        return ApiResult.ok(drillExtraService.getDrillExtra(drillExtraId));
    }

    @PostMapping("/getDrillExtraByStu")
    @OperationLog(name = "查询学生训练的记时记数")
    @ApiOperation(value = "查询学生训练的记时记数", response = DrillExtraStudentAo.class, notes = "")
    public ApiResult<Object> getDrillExtraByStu(@RequestBody DrillExtraPageParam drillExtraPageParam) throws Exception {
        return ApiResult.ok(drillExtraService.getDrillExtraByStu(drillExtraPageParam));
    }

    @GetMapping("/getDrillExtraIcons")
    @OperationLog(name = "计数计时图片")
    @ApiOperation(value = "计数计时图片", response = Icon.class)
    public ApiResult<Object> getDrillExtraIcons() throws Exception {
        return ApiResult.ok(iconService.list());
    }

    @PostMapping(value = "/getGrowUpData")
    @OperationLog(name = "获取成长数据")
    @ApiOperation(value = "获取成长数据", response = Object.class, notes = "返回结果 data:{drills:参考分页列表字段,count:数量}")
    public ApiResult<Object> getGrowUpData(@Validated @RequestBody DrillPageParam drillPageParam) {
        IPage<Drill> p = drillService.getGrowUpData(drillPageParam);
        JSONObject o = new JSONObject();
        o.put("drills", p.getRecords());
        o.put("count", p.getTotal());
        return ApiResult.ok(o);
    }

    @PostMapping("/getStuField")
    @OperationLog(name = "个人场记列表数据")
    @ApiOperation(value = "个人场记列表数据", response = FieldData.class)
    public ApiResult<Object> getStuField(@Validated @RequestBody FieldDataPageParam fieldDataPageParam) {
        List<FieldData> fieldDataIPage = fieldDataService.getStuField(fieldDataPageParam);
        return ApiResult.ok(fieldDataIPage);
    }

    @GetMapping("/findListByIds/{stuId}/{fieldId}/{teamId}")
    @OperationLog(name = "个人场记详情数据")
    @ApiOperation(value = "个人场记详情数据", response = FieldData.class, notes = "参数 stuId:学员id  fieldId:场记id  teamId:团队id APP创建的训练给groupId的值")
    public ApiResult<Object> findListByIds(@PathVariable("stuId") Long stuId, @PathVariable("fieldId") Long fieldId, @PathVariable("teamId") Long teamId) {
        FieldData fieldData = fieldDataService.findListByIds(stuId, fieldId, teamId);
        return ApiResult.ok(fieldData);
    }

    @PostMapping("/getGroupField")
    @OperationLog(name = "团队场记列表")
    @ApiOperation(value = "团队场记列表", response = FieldNotes.class)
    public ApiResult<Object> getGroupField(@Validated @RequestBody FieldNotesPageParam fieldNotesPageParam) {
        List<FieldNotes> fieldNotesIPage = fieldNotesService.getGroupField(fieldNotesPageParam);
        return ApiResult.ok(fieldNotesIPage);
    }

    @GetMapping("/unstartCount/{drillId}/{teamId}")
    @OperationLog(name = "未启动的有设备且未被覆盖的球员数量")
    @ApiOperation(value = "未启动的有设备且未被覆盖的球员数量", response = FieldData.class)
    public ApiResult<Object> unstartCount(@PathVariable("drillId") Long drillId, @PathVariable("teamId") Long teamId) {
        return ApiResult.ok(drillStudentService.unstartCount(drillId, teamId));
    }

    @GetMapping("/exerciseTime/{teamId}/{start}/{stop}")
    @OperationLog(name = "学生运动情况有运动时间的日期")
    @ApiOperation(value = "学生运动情况有运动时间的日期", response = String.class, notes = "参数teamId：球队id start：开始时间'2022-11-28' stop：结束时间 data 返回一个有数据的日期集合[\"2022-11-28\",\"2022-12-08\"]")
    public ApiResult exerciseTime(@PathVariable("teamId") Long teamId, @PathVariable("start") String start, @PathVariable("stop") String stop) {
        return ApiResult.ok(drillService.exerciseTime(teamId, start, stop));
    }

    @GetMapping("/exerciseConditionByMonth/{studentId}/{date}")
    @OperationLog(name = "学生排名运动情况 （运动一小时）按月查")
    @ApiOperation(value = "学生排名运动情况 （运动一小时）按月查", response = ExerciseConditionAo.class, notes = "data 查询当月的时间戳")
    public ApiResult<List<ExerciseConditionAo>> exerciseConditionByMonth(@PathVariable("studentId") Long studentId, @PathVariable("date") Long date) {
        return ApiResult.ok(drillService.exerciseConditionByMonth(studentId, date));
    }

    @GetMapping("/exerciseConditionByTeam/{teamId}/{date}")
    @OperationLog(name = "学生排名运动情况 （运动一小时）按天查")
    @ApiOperation(value = "学生排名运动情况 （运动一小时）按天查", response = ExerciseConditionAo.class, notes = "data 查询当天的时间戳")
    public ApiResult<List<ExerciseConditionAo>> exerciseConditionByTeam(@PathVariable("teamId") Long teamId, @PathVariable("date") Long date) {
        return ApiResult.ok(drillService.exerciseConditionByTeam(teamId, date));
    }

    @GetMapping("/exerciseConditionByMonthStu/{studentId}/{date}")
    @OperationLog(name = "学生排名运动情况 （运动一小时）按月查 (手机端使用)")
    @ApiOperation(value = "学生排名运动情况 （运动一小时）按月查 (手机端使用)", response = ExerciseConditionAo.class, notes = "data 查询当月的时间戳")
    public ApiResult<List<ExerciseConditionAo>> exerciseConditionByMonthStu(@PathVariable("studentId") Long studentId, @PathVariable("date") Long date) {
        return ApiResult.ok(drillService.exerciseConditionByMonthStu(studentId, date));
    }

    @GetMapping("/exerciseConditionByDayStu/{teamId}/{studentId}/{date}")
    @OperationLog(name = "学生排名运动情况 （运动一小时）按天查  (手机端使用)")
    @ApiOperation(value = "学生排名运动情况 （运动一小时）按天查 (手机端使用)", response = ExerciseConditionAo.class, notes = "data 查询当天的时间戳 , 没有teamId的情况给0")
    public ApiResult<List<ExerciseConditionAo>> exerciseConditionByDayStu(@PathVariable("teamId") Long teamId, @PathVariable("studentId") Long studentId, @PathVariable("date") Long date) {
        return ApiResult.ok(drillService.exerciseConditionByDayStu(teamId, studentId, date));
    }

    @GetMapping("/getStuByGroupIdAndFieldId/{groupId}/{fieldId}")
    @OperationLog(name = "查询场记学员")
    @ApiOperation(value = "查询场记学员", response = FieldData.class)
    public ApiResult<Object> getStuByGroupIdAndFieldId(@PathVariable("groupId") Long groupId, @PathVariable("fieldId") Long fieldId) {
        List<FieldData> list = fieldDataService.getStuByGroupIdAndFieldId(groupId, fieldId);
        return ApiResult.ok(list);
    }

    @GetMapping("/getMotionSteps/{teamId}/{createTime}")
    @OperationLog(name = "团队运动数据")
    @ApiOperation(value = "团队运动数据", response = StuPersonDataAo.class, notes = "teamId : 团队id  createTime : 查找日期(2022-12-20)  返回数据：\n " +
            "personData： sumSteps：总步数 sumRun：总跑动距离 sumExercise：总运动时间  sumCalorie：总消耗  stuCount：总人数\n" +
            "stuList：学生列表 (studentId:学员id  stuName：学生名字 image：学生头像  createTime：日期 stepCount：步数  runDistance：跑动距离  exerciseTime：运动时长  calorie：消耗) ")
    public ApiResult<Object> getMotionSteps(@PathVariable("teamId") Long teamId, @PathVariable("createTime") String createTime) {
        return ApiResult.ok(personDataService.getMotionSteps(teamId, createTime, "DRILL"));
    }

    @GetMapping("/teamSteps/{teamId}/{startTime}/{stopTime}")
    @OperationLog(name = "时间段的运动步数")
    @ApiOperation(value = "时间段的运动步数", notes = "teamId: 团队id  startTime: 开始时间(2022-12-20)  stopTime:结束时间(2022-12-20)")
    public ApiResult<Object> teamSteps(@PathVariable("teamId") Long teamId, @PathVariable("startTime") String startTime, @PathVariable("stopTime") String stopTime) {
        return ApiResult.ok(personDataService.teamSteps(teamId, startTime, stopTime));
    }

    @GetMapping("/attendance/{teamId}/{date}")
    @OperationLog(name = "学员考勤")
    @ApiOperation(value = "学员考勤", response = StudentInfo.class, notes = "teamId :训练id date:时间(2022-12-20)")
    public ApiResult<Object> attendance(@PathVariable("teamId") Long teamId, @PathVariable("date") String date) {
        return ApiResult.ok(drillService.attendance(teamId, date));
    }

    /**
     * 个人最新（篮球/足球）数据
     *
     * @param stuId
     * @param drillId
     * @return
     */
    @GetMapping("/getPersonal/{stuId}/{drillId}")
    @OperationLog(name = "个人（篮球/足球）数据")
    @ApiOperation(value = "个人（篮球/足球）数据", response = StuPersonDataAo.class, notes = "参数 stuId: 学生id  drillId: 训练id  篮球返回数据：\n" +
            "distance ：跑动距离 runTop：跑动距离排名  height：最大高度 heightTop：最大高度排名 score：得分  scoreTop：得分排名  assist：助攻  assistTop：助攻排名  backboard：篮板  backboardTop：篮板排名   dataMap：场记数据json \n" +
            "足球返回数据： goal：进球数  assisting：助攻数  preemption：抢断数  excitingShot：精彩射门")
    public ApiResult getPersonal(@PathVariable("stuId") Long stuId, @PathVariable("drillId") Long drillId) {
        return ApiResult.ok(fieldDataService.getPersonal(stuId, drillId));
    }

    /**
     * 团队(篮球/足球)数据
     *
     * @param teamId
     * @param drillId
     * @return
     */
    @GetMapping("/getTeam/{teamId}/{drillId}")
    @OperationLog(name = "团队(篮球/足球)数据")
    @ApiOperation(value = "团队(篮球/足球)数据", response = StuPersonDataAo.class, notes = "篮球数据：\n 参数 teamId: 团队id  drillId:  训练id  返回数据：\n" +
            "runList：跑步排序列表（stuId：学生id ，stuName：学生名字，image：头像，distance：跑动距离 （米）） \n " +
            "heightList：最大纵跳高度列表（stuId：学生id ，stuName：学生名字，image：头像，maxHeight：最大纵跳高度 （厘米））\n" +
            "assistList：助攻列表（stuId：学生id ，stuName：学生名字，image：头像，assist：助攻）\n" +
            "scoreList：得分列表（stuId：学生id ，stuName：学生名字，image：头像，score：得分）\n" +
            "sumSteps：总步数  sumRun：总跑动距离  sumCalorie：总热量   dataMap：场记总和 \n" +
            "sumScore：总得分  sumAssist：总助攻  sumBackboard：总篮板 \n" +
            "motionName：运动健将  motionScore：运动健将得分  motionDistance：运动健将跑动距离 （米） motionAssist：运动健将助攻次数  motionBoard：运动健将篮板次数  motionPreemption：运动健将抢断次数 \n" +
            "足球数据：\n 参数增加 sumGoal: 总进球  sumPreemption: 总抢断数  motionGoal: 运动健将进球数  motionExcitingShot: 运动健将精彩射门次数  sumExcitingShot: 总精彩射门")
    public ApiResult getTeam(@PathVariable("teamId") Long teamId, @PathVariable("drillId") Long drillId) {
        return ApiResult.ok(fieldDataService.getTeam(teamId, drillId));
    }

    /**
     * 团队篮球排行榜
     *
     * @param teamId
     * @param drillId
     * @param key
     * @return
     */
    @GetMapping("/teamRank/{teamId}/{drillId}/{key}")
    @OperationLog(name = "团队篮球排行榜")
    @ApiOperation(value = "团队篮球排行榜", response = StuPersonDataAo.class, notes = "参数 teamId : 班级id  date:日期 (2022-12-19)  key: 1-4(得分、跑动、助攻、最大高度)  返回列表数据参考学校篮球数据协议 \n" +
            " avgScore : 平均得分  avgDistance：平均跑动距离 m  avgAssist：平均助攻次数  avgHeight：平均高度")
    public ApiResult teamRank(@PathVariable("teamId") Long teamId, @PathVariable("drillId") Long drillId, @PathVariable("key") Integer key) {
        return ApiResult.ok(drillService.teamRank(teamId, drillId, key));
    }

    /**
     * 场记小组数据学员排名
     *
     * @param drillId
     * @param groupId
     * @param key
     * @return
     */
    @GetMapping("/fieldRank/{drillId}/{groupId}/{key}/{zero}")
    @OperationLog(name = "场记小组数据学员排名")
    @ApiOperation(value = "场记小组数据学员排名", response = StuPersonDataAo.class, notes = "参数 drillId：训练id  篮球key：对应存储某项数据名称 zero:是否把为0的也参与排名，并返回\n" +
            "（`onePointer`  '罚球（命中）', `twoPointer`  '二分（命中）',  `threePointer`  '三分（命中）',\n" +
            "  `treeBasket`  '三步跨篮次数',  `assist`  '助攻次数',  `backboard`  '篮板次数',\n" +
            "  `snatch`  '抢断次数',  `blockShot`  '盖帽次数',  `walk`  '走步次数',\n" +
            "  `pullPeople`  '拉人次数',  `walkBall` '带球走次数',  `threeViolation`  '3秒违规次数',\n" +
            "  `illegalDribble`  '非法运球次数',  `intentionalBall`  '故意脚球次数',  `hookFoul`  '勾人犯规次数',\n" +
            "  `dribbler`  '带球撞人次数',  `outBall` '球出界次数',  `illegalHands`  '非法用手次数',\n" +
            "  `headFoul`  '击头犯规次数', `flyingElbow`  '过分飞肘次数', `illegalAttack`  '非法进攻次数',\n" +
            "  `illegalDefense`  '非法防守次数', `pushPepole`  '推人次数',  `penaltyExit`  '罚满离场次数',\n" +
            "  `technicalfoul`  '技术犯规', `violationfoul` '违体犯规', `comeback`  '回场',\n" +
            "  `amazing`  '精彩过人', `nicepass`  '秒传' , `frontback`  '篮板（前场）', `afterback`  '篮板（后场）'\n" +
            "  `nullone`  '罚球（不中）',  `nulltwo`  '二分（不中）',  `nullthree`  '三分（不中）',  `dunk`  '扣篮', `helpball`  '精彩救球' \n" +
            " 足球key：`goal` '进球',`orthogonality` '射正',`assisting` '助攻',\n " +
            "  `surpass` '过人',`pointsphere` '点球',`cornerkick` '角球',`freekick` '任意球',\n " +
            "  `threatball` '威胁球',`heading` '头球',`raise` '解围',`preemption` '抢断', \n " +
            "  `redcard` '红牌',`shooting` '射偏',`rules` '犯规',`offside` '越位',\n " +
            "  `puking` '冒顶',`yellowcard` '黄牌',`owngoal` '乌龙球',`waveshooting` '浪射',\n " +
            "  `kickair` '踢空',`stoperror` '停球失误',`defensiveerror` '防守失误',`passingerror` '传球失误',`wonderful` '精彩扑救' \n" +
            "  `excitingShot` '精彩射门',`excitingSyeals` '精彩抢断',`longpass` '精彩长传',`headgoal` '头球功门' \n" +
            "  `headclear` '头球解围',`clearance` '精彩解围',`rescue` '扑救',`pointMaking` '造点',`fatalerror` '致命失误' \n" +
            "  `wonderfulstop` '精彩停球',`shoot` '射门',`threatenshoot` '威胁射门',`scoring` '攻入禁区',`seriouserror` '严重失误' \n" +
            "   返回数据：stuId:学生id  stuName：学生名字  image：学生头像  frequency：次数  wonderful：精彩扑救 hitRate:命中率")
    public ApiResult fieldRank(@PathVariable("drillId") Long drillId, @PathVariable("groupId") Long groupId, @PathVariable("key") String key, @PathVariable("zero") Boolean zero) {
        return ApiResult.ok(drillService.fieldRank(drillId, groupId, key, zero));
    }

    @GetMapping("/getNoUploadCountByStu/{studentId}")
    @OperationLog(name = "获取某个学生的未完成训练数量")
    @ApiOperation(value = "获取某个学生的未完成训练数量", response = ApiResult.class, notes = "")
    public ApiResult getNoUploadCountByStu(@PathVariable("studentId") Long studentId) {
        return ApiResult.ok(drillService.getNoUploadCountByStu(studentId));
    }

    @PostMapping("/sendTransparentByTeacherScanQRCode")
    @OperationLog(name = "计时员扫码成功通知老师")
    @ApiOperation(value = "计时员扫码成功通知老师", response = ApiResult.class, notes = "Body传" +
            "{studentId:1,drillExtraId:计数计时id,drillExtraGroupId:小组id}")
    public ApiResult sendTransparentByTeacherScanQRCode(@RequestBody JSONObject object) {
        org.json.JSONObject o = new org.json.JSONObject(object.getInnerMap());
        transparentMessageService.sendTransparentByTeacherScanQRCode(o);
        drillExtraService.updatescanQRcodes(object.getLong("drillExtraId"), object.getLong("studentId"));
        return ApiResult.ok();
    }

    @PostMapping("/sendTransparentByTeacherTime")
    @OperationLog(name = "计时员操作通知老师")
    @ApiOperation(value = "计时员操作通知老师", response = ApiResult.class, notes = "Body传" +
            "{studentId:1,turns:圈数,drillExtraId:计数计时id,drillExtraGroupId:小组id, type:start|stop|over,stopTime:结束时间戳}")
    public ApiResult getNoUploadCountByStu(@RequestBody JSONObject object) {
        org.json.JSONObject o = new org.json.JSONObject(object.getInnerMap());
        transparentMessageService.sendTransparentByTeacherTime(o);
        redisUtils.set(CacheConsts.DRILLDXTRASTATUS + object.getLongValue("drillExtraGroupId") + "-" + object.getLongValue("studentId"), object.getString("type"), 43200l);//半天后自动过期
        return ApiResult.ok();
    }

    @PostMapping("/setStudentDrillExtraStatus/{studentId}/{drillExtraGroupId}/{status}")
    @OperationLog(name = "设置学生计时状态")
    @ApiOperation(value = "设置学生计时状态", response = ApiResult.class, notes = "studentId传0 代表整个小组 status:start|stop")
    public ApiResult setStudentDrillExtraStatus(@PathVariable("studentId") Long studentId, @PathVariable("drillExtraGroupId") Long drillExtraGroupId, @PathVariable("status") String status) {
        redisUtils.set(CacheConsts.DRILLDXTRASTATUS + drillExtraGroupId + "-" + studentId, status, 43200l);//半天后自动过期
        return ApiResult.ok();
    }

    @GetMapping("/getStudentDrillExtraStatus/{studentId}/{drillExtraGroupId}")
    @OperationLog(name = "获取学生计时状态")
    @ApiOperation(value = "获取学生计时状态", response = ApiResult.class, notes = "studentId传0 data返回：start|stop")
    public ApiResult getStudentDrillExtraStatus(@PathVariable("studentId") Long studentId, @PathVariable("drillExtraGroupId") Long drillExtraGroupId) {
        Object value = redisUtils.get(CacheConsts.DRILLDXTRASTATUS + drillExtraGroupId + "-" + studentId);
        if (value != null) {
            return ApiResult.ok(value);
        }
        return ApiResult.ok(redisUtils.get(CacheConsts.DRILLDXTRASTATUS + drillExtraGroupId + "-" + 0));
    }

}