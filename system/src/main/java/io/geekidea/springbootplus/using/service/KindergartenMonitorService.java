package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.using.entity.KindergartenMonitor;
import io.geekidea.springbootplus.using.entity.PersonData;
import io.geekidea.springbootplus.using.entity.Team;
import io.geekidea.springbootplus.using.param.KindergartenMonitorPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.using.vo.*;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
public interface KindergartenMonitorService extends BaseService<KindergartenMonitor> {

    KindergartenMonitor getById(Long id);

    KindergartenMonitor infoLately(Long schoolId);

    /**
     * 保存
     *
     * @param kindergartenMonitor
     * @return
     * @throws Exception
     */
    boolean saveKindergartenMonitor(KindergartenMonitor kindergartenMonitor) throws Exception;

    /**
     * 修改
     *
     * @param kindergartenMonitor
     * @return
     * @throws Exception
     */
    boolean updateKindergartenMonitor(KindergartenMonitor kindergartenMonitor) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteKindergartenMonitor(Long id) throws Exception;

    Paging<KindergartenMonitorStuAo> getKindergartenMonitorListByStu(KindergartenMonitorPageParam kindergartenMonitorPageParam);

    Paging<Team> getKindergartenMonitorPageList(KindergartenMonitorPageParam kindergartenMonitorPageParam) throws Exception;

    boolean setMonitorSort(List<Team> teamList);

    List<KindergartenMonitorTeamAo> recentlyLatelyBySchool(Long schoolId);

    List<KindergartenMonitorTeamAo> recentlyLatelyByTeam(Long teamId);

    List<KindergartenMonitorTeamAo> recentlyLatelyByTeamOne(Long teamId, String date);

    List<KindergartenMonitorTeamAo> recently(Long schoolId,Long teamId, String date);

    List<KindergartenMonitorTeamAo> recentlyById(Long monitorId,Long teamId);

    boolean uploadData(String strJSONArray, Long matchId, String matchType);

    Object getPlayerData(Long matchId, Long studentId, Long teamId, String matchType,Boolean app);

    Object getPhysicalAnalysis(Long matchId, Long teamId);

    Object moveDistanceRanking(Long matchId, String rankingKey, List<PersonData> personDatas, Long teamId);

    List<DataStatisticsAo> statistics(Long teamId, Long studentId, Integer type, JSONArray array);

    Object sportsRanking(Long schoolId,String start, String stop, Long teamId, Integer type,String key);

    Object statisticsNew(Long teamId, Long studentId, String startTime, String endTime,Integer type);

    Object personColumnar(String start, String stop, Long teamId,Long studentId, Integer type);

    Object teamExercise(String start, String stop, Long teamId, Integer type,String key);

    Object personExercise(String start, String stop, Long teamId,Long studentId, Integer type);

    Object getHaveDateTime(String start, String stop, Long teamId,Long studentId);

    Object getHaveDateTimeLately(Long studentId);

    Object teamRunCalorieSum(Long teamId, Integer type,String start, String stop);
}
