package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.AppTeamUser;
import io.geekidea.springbootplus.using.mapper.AppTeamUserMapper;
import io.geekidea.springbootplus.using.service.AppTeamUserService;
import io.geekidea.springbootplus.using.param.TeamStudentPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Slf4j
@Service
public class AppTeamUserServiceImpl extends BaseServiceImpl<AppTeamUserMapper, AppTeamUser> implements AppTeamUserService {

    @Autowired
    private AppTeamUserMapper appTeamUserMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveTeamStudent(AppTeamUser appTeamUser) throws Exception {
        return super.save(appTeamUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTeamStudent(AppTeamUser appTeamUser) throws Exception {
        return super.updateById(appTeamUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTeamStudent(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<AppTeamUser> getTeamStudentPageList(TeamStudentPageParam teamStudentPageParam) throws Exception {
        Page<AppTeamUser> page = new PageInfo<>(teamStudentPageParam, OrderItem.desc(getLambdaColumn(AppTeamUser::getCreateTime)));
        LambdaQueryWrapper<AppTeamUser> wrapper = new LambdaQueryWrapper<>();
        IPage<AppTeamUser> iPage = appTeamUserMapper.selectPage(page, wrapper);
        return new Paging<AppTeamUser>(iPage);
    }

}
