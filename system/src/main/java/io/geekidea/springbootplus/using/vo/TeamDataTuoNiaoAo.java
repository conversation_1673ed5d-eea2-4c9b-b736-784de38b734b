package io.geekidea.springbootplus.using.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TeamDataTuoNiaoAo implements Serializable {
    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 球赛id
     */
    private Long matchId;

    /**
     * 团队曲线
     */
    private JSON curves;

    /**
     * 运动时间（秒）
     */
    private long exerciseTime;

    /**
     * MVP
     */
    private JSONObject mvp;

    /**
     * 总跑动 (m)
     */
    private long sumRun;

    /**
     * 总卡路里
     */
    private Integer sumCalorie;

    /**
     * 总跑动最多学生
     */
    private String runBestStu;

    /**
     * 总跑动最多学生的跑动值（m）
     */
    private long bestStuRun;

    /**
     * 历史平均消耗
     */
    private double avgConsume;

    /**
     * 速度数据
     */
    private Map<String, Object> speedDataMap;

    /**
     * 步伐
     */
    private JSONObject pace;

    private JSONObject runningForm;

    private JSONObject touchdownWay;

    /**
     * 总步数
     */
    private Integer sumStep;

    private Integer jumpAvgHeight;

    private Integer maxDuration;

    private Integer avgDuration;

    private Integer maxTakeOffDistance;

    private Integer maxTakeOffHeight;

    private JSONArray runDistances = new JSONArray();

    private JSONArray maxSprintSpeeds = new JSONArray();

    private JSONArray overallSpeeds = new JSONArray();

    private JSONArray overallSpeedCounts = new JSONArray();

    private JSONArray leftOrRightTurnings = new JSONArray();

    private JSONArray startingsOrBrakes = new JSONArray();

    private JSONArray breakOuts = new JSONArray();

    /**
     * 深度数据
     */
    private JSONObject teamAvgDepthData;

    private JSONObject teamAvgOverallSpeed;

    private JSONObject teamAvgOverallSpeedCount;

    /**
     * 最大冲刺速度(km/h)
     */
    private Double teamAvgMaxSprintSpeed;
}
