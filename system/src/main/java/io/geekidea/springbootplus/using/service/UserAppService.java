package io.geekidea.springbootplus.using.service;

import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.using.entity.Audit;
import io.geekidea.springbootplus.using.entity.User;
import io.geekidea.springbootplus.using.entity.UserApp;
import io.geekidea.springbootplus.using.param.UserAppPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.io.Serializable;
import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface UserAppService extends BaseService<UserApp> {
    Boolean register(UserApp userApp);

    UserApp loginUserApp(UserApp userApp);

    UserApp examineName(UserApp userApp);

    Boolean checkPassword(String password, String checkpwd);

    ApiCode onlyValidation(String phone);

    ApiCode onlyValidationEmail(String email);

    ApiCode updaValidation(String phone,Long userId);

    ApiCode updaValidationEmail(String email,Long userId);

    Boolean resetPassword(UserApp userApp);

    Boolean logoutUser(UserApp userApp) throws Exception;

    UserApp getById(Long id);

    UserApp getUser(Long id);

    List<UserApp> getByTeamId(Long teamId);

    /**
     * 保存
     *
     * @param userApp
     * @return
     * @throws Exception
     */
    boolean saveUserApp(UserApp userApp) throws Exception;

    /**
     * 修改
     *
     * @param userApp
     * @return
     * @throws Exception
     */
    boolean updateUserApp(UserApp userApp) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteUserApp(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param userAppQueryParam
     * @return
     * @throws Exception
     */
    Paging<UserApp> getUserAppPageList(UserAppPageParam userAppPageParam) throws Exception;

    Boolean checkPhoneExist(String phone);

    String sendPhoneVerificationCode(String phoneNumber);

    ApiCode checkVerificationCode(String credential, String contentBody, Boolean delete);

    String sendEmailVerificationCode(String email,Boolean en);

    ApiCode checkEmailVerificationCode(String email, String code);

    Boolean checkEmailExist(String email);

    JSONObject getCount(Long userId, String matchType,Long courseId);

}
