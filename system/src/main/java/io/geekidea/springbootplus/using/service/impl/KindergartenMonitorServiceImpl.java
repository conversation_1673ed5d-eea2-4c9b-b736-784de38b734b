package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.geekidea.springbootplus.framework.util.DateUtil;
import io.geekidea.springbootplus.using.arg.SystemArg;
import io.geekidea.springbootplus.using.common.CalulateCommonMethod;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.enums.CurveEnum;
import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.enums.UploadedEnum;
import io.geekidea.springbootplus.using.mapper.KindergartenMonitorMapper;
import io.geekidea.springbootplus.using.mapper.KindergartenTeamMonitorMapper;
import io.geekidea.springbootplus.using.param.GameExtraPageParam;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.param.KindergartenMonitorPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.geekidea.springbootplus.using.vo.*;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
@Slf4j
@Service
public class KindergartenMonitorServiceImpl extends BaseServiceImpl<KindergartenMonitorMapper, KindergartenMonitor> implements KindergartenMonitorService {

    @Autowired
    private KindergartenMonitorMapper kindergartenMonitorMapper;
    @Autowired
    private KindergartenTeamMonitorService kindergartenTeamMonitorService;
    @Autowired
    private KindergartenMonitorStudentService kindergartenMonitorStudentService;
    @Autowired
    private PersonDataService personDataService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private MatchHardwareService matchHardwareService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private RawDataService rawDataService;
    @Autowired
    private TeamDataService teamDataService;
    @Autowired
    private SystemArg systemArg;
    @Autowired
    private SchoolInfoService schoolInfoService;
    @Autowired
    private CalulateCommonMethod calulateCommonMethod;
    @Autowired
    private HardwareService hardwareService;

    @Override
    public KindergartenMonitor getById(Long id) {
        KindergartenMonitor kindergartenMonitor = getBaseMapper().getById(id);
        if (kindergartenMonitor == null) {
            return null;
        }
        List<KindergartenTeamMonitor> kindergartenTeamMonitorList = kindergartenTeamMonitorService.list(new LambdaQueryWrapper<KindergartenTeamMonitor>().eq(KindergartenTeamMonitor::getMonitorId, kindergartenMonitor.getId()));

        kindergartenMonitor.setTeams(kindergartenTeamMonitorList.stream().map(KindergartenTeamMonitor::getTeamId).collect(Collectors.toList()));
        List<Long> noStartIds = new ArrayList<>();
        List<Long> noUploadIds = new ArrayList<>();
        int startCount = 0;
        for (Long d : kindergartenMonitor.getTeams()) {
            long stuCount = studentInfoService.count(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, d));
            if (stuCount == 0) {
                continue;
            }
            long hardCount = hardwareService.count(new LambdaQueryWrapper<Hardware>().eq(Hardware::getUnbind, 0).eq(Hardware::getTeamId, d));
            if (hardCount == 0) {
                continue;
            }
            List<PersonData> personDataList = personDataService.getListNoData("MONITOR", d, kindergartenMonitor.getId());
            List<MatchHardware> matchHardwares = matchHardwareService.getMatchHardwareByMatchId(kindergartenMonitor.getId(), d, "MONITOR").stream().filter(e -> e.getStarted()).collect(Collectors.toList());
            long start = matchHardwares.stream().filter(e -> e.getStarted()).count() - kindergartenMonitorStudentService.cancelCount(kindergartenMonitor.getId(), d);
            if (start > 0) {
                startCount++;
            }
            if (hardCount > matchHardwares.size()) {//有未启动的
                noStartIds.add(d);
            }
            if (start > personDataList.size()) {//启动设备人数大于同步 代表有未同步的
                noUploadIds.add(d);
            }
        }

        kindergartenMonitor.setNoStartIds(noStartIds);
        kindergartenMonitor.setNoUploadIds(noUploadIds);
        if (kindergartenMonitor.getStatus().equals(MatchStatusEnum.STARTED)&&noStartIds.size() == 0) {
            kindergartenMonitor.setStart(true);
        } else {
            kindergartenMonitor.setStart(false);
        }

        if (kindergartenMonitor.getStatus().equals(MatchStatusEnum.FINISHED)&&noUploadIds.size() == 0) {
            kindergartenMonitor.setUpload(true);
        } else {
            kindergartenMonitor.setUpload(false);
        }

        return kindergartenMonitor;
    }

    @Override
    public KindergartenMonitor infoLately(Long schoolId) {
        KindergartenMonitor kindergartenTeamMonitor = getOne(new LambdaQueryWrapper<KindergartenMonitor>()
                .eq(KindergartenMonitor::getSchoolId, schoolId).orderByDesc(KindergartenMonitor::getId).last(" limit 1"));
        if (kindergartenTeamMonitor == null) {
            return null;
        }
        return getById(kindergartenTeamMonitor.getId());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveKindergartenMonitor(KindergartenMonitor kindergartenMonitor) throws Exception {
        super.saveOrUpdate(kindergartenMonitor);
        List<KindergartenTeamMonitor> kindergartenTeamMonitorList = new ArrayList<>();
        for (Long id : kindergartenMonitor.getTeams()) {
            KindergartenTeamMonitor kindergartenTeamMonitor = new KindergartenTeamMonitor();
            kindergartenTeamMonitor.setMonitorId(kindergartenMonitor.getId());
            kindergartenTeamMonitor.setTeamId(id);
            kindergartenTeamMonitor.setSchoolId(kindergartenMonitor.getSchoolId());
            kindergartenTeamMonitorList.add(kindergartenTeamMonitor);
        }
        if (kindergartenTeamMonitorList.size() > 0) {
            kindergartenTeamMonitorService.saveOrUpdateBatch(kindergartenTeamMonitorList);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateKindergartenMonitor(KindergartenMonitor kindergartenMonitor) throws Exception {
        return super.updateById(kindergartenMonitor);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteKindergartenMonitor(Long id) throws Exception {
        kindergartenTeamMonitorService.remove(new LambdaQueryWrapper<KindergartenTeamMonitor>().eq(KindergartenTeamMonitor::getMonitorId, id));
        return super.removeById(id);
    }

    @Override
    public Paging<KindergartenMonitorStuAo> getKindergartenMonitorListByStu(KindergartenMonitorPageParam kindergartenMonitorPageParam) {
        StudentInfo studentInfo = studentInfoService.getStudentInfo(kindergartenMonitorPageParam.getStudentId());
        Page<KindergartenMonitorStuAo> page = new PageInfo<>(kindergartenMonitorPageParam);
        IPage<KindergartenMonitorStuAo> kindergartenMonitorListByStu = getBaseMapper().getKindergartenMonitorListByStu(page, studentInfo.getSchoollId(), studentInfo.getTeamId(), studentInfo.getId(), kindergartenMonitorPageParam.getFinish(), kindergartenMonitorPageParam.getUpload());
        if (kindergartenMonitorPageParam.getUpload() != null) {
            for (KindergartenMonitorStuAo k : kindergartenMonitorListByStu.getRecords()) {
                k.setUpload(kindergartenMonitorPageParam.getUpload());
                PersonData personData = personDataService.findOfMatchIdAndStudentId("MONITOR", k.getId(), kindergartenMonitorPageParam.getStudentId());
                if (personData == null) {
                    k.setAppLook(false);
                } else {
                    k.setAppLook(personData.getAppLook());
                }
            }
        }
        return new Paging<KindergartenMonitorStuAo>(kindergartenMonitorListByStu);
    }

    @Override
    public Paging<Team> getKindergartenMonitorPageList(KindergartenMonitorPageParam kindergartenMonitorPageParam) throws Exception {
        Page<Team> page = new PageInfo<>(kindergartenMonitorPageParam);
        LambdaQueryWrapper<Team> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Team::getSchoolId, kindergartenMonitorPageParam.getSchoolId());
        wrapper.eq(Team::getSportsMonitor, true);
        wrapper.orderByAsc(Team::getMonitorSort);

        IPage<Team> iPage = teamService.getBaseMapper().selectPage(page, wrapper);
        for (Team e : iPage.getRecords()) {
            Double avgage = studentInfoService.avgAgeByTeamId(e.getId());
            e.setAvgAge(avgage != null ? avgage.intValue() : 0);
            e.setPeopleNum(studentInfoService.count(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, e.getId())));
            e.setMonitorCount(kindergartenTeamMonitorService.count(new LambdaQueryWrapper<KindergartenTeamMonitor>().eq(KindergartenTeamMonitor::getTeamId, e.getId())));
            String s = DateUtil.getDateString(new Date()) + " 00:00:00";
            KindergartenTeamMonitor kindergartenMonitor = kindergartenTeamMonitorService.getOne(new LambdaQueryWrapper<KindergartenTeamMonitor>().eq(KindergartenTeamMonitor::getTeamId, e.getId()).ge(KindergartenTeamMonitor::getCreateTime, s).last(" limit 1"));
            if (kindergartenMonitor != null) {
                e.setRecentMonitorData(kindergartenMonitor.getCreateTime());
            } else {
                e.setRecentMonitorData(personDataService.getEcentData(e.getId(), "MONITOR"));
            }
            e.setExerciseConditionAos(fillKindergartenMonitorAo(e.getId()));
        }
        return new Paging<Team>(iPage);
    }

    @Override
    public boolean setMonitorSort(List<Team> teamList) {
        for (Team team : teamList) {
            teamService.update(new LambdaUpdateWrapper<Team>().eq(Team::getId, team.getId()).set(Team::getMonitorSort, team.getMonitorSort()));
        }
        return true;
    }

    @Override
    public List<KindergartenMonitorTeamAo> recentlyLatelyBySchool(Long schoolId) {
        KindergartenMonitor kindergartenMonitor = getOne(new LambdaQueryWrapper<KindergartenMonitor>().eq(KindergartenMonitor::getSchoolId, schoolId).orderByDesc(KindergartenMonitor::getId).last(" limit 1"));
        return recentlyById(kindergartenMonitor.getId(), null);
    }

    @Override
    public List<KindergartenMonitorTeamAo> recentlyLatelyByTeam(Long teamId) {
        KindergartenTeamMonitor kindergartenTeamMonitor = kindergartenTeamMonitorService.getOne(new LambdaQueryWrapper<KindergartenTeamMonitor>()
                .eq(KindergartenTeamMonitor::getTeamId, teamId).orderByDesc(KindergartenTeamMonitor::getId).last(" limit 1"));
        if (kindergartenTeamMonitor == null) {
            return recentlyById(0l, teamId);
        }
        return recentlyById(kindergartenTeamMonitor.getMonitorId(), teamId);
    }

    @Override
    public List<KindergartenMonitorTeamAo> recentlyLatelyByTeamOne(Long teamId, String date) {
        String createData = date + " 00:00:00";
        String stopData = date + " 23:59:59";
        KindergartenTeamMonitor kindergartenTeamMonitor = kindergartenTeamMonitorService.getOne(new LambdaQueryWrapper<KindergartenTeamMonitor>()
                .eq(KindergartenTeamMonitor::getTeamId, teamId).ge(KindergartenTeamMonitor::getCreateTime, createData)
                .le(KindergartenTeamMonitor::getCreateTime, stopData)
                .orderByDesc(KindergartenTeamMonitor::getId).last(" limit 1"));
        if (kindergartenTeamMonitor == null) {
            return recentlyById(0l, teamId);
        }
        return recentlyById(kindergartenTeamMonitor.getMonitorId(), teamId);
    }

    public List<KindergartenMonitorTeamAo> recentlyById(Long monitorId, Long teamId) {
        KindergartenMonitor kindergartenMonitor = kindergartenMonitorMapper.getById(monitorId);
        if (kindergartenMonitor == null) {
            return new ArrayList<>();
        }
        List<KindergartenTeamMonitor> kindergartenTeamMonitorList = kindergartenTeamMonitorService.list(new LambdaQueryWrapper<KindergartenTeamMonitor>()
                .eq(KindergartenTeamMonitor::getMonitorId, kindergartenMonitor.getId()).eq(teamId != null, KindergartenTeamMonitor::getTeamId, teamId));
        Map<Long, List<ExerciseConditionAo>> personDataMap = personDataService.findByMonitor(kindergartenMonitor.getId()).stream()
                .collect(Collectors.groupingBy(ExerciseConditionAo::getTeamId));

        List<KindergartenMonitorTeamAo> kindergartenMonitorTeamAos = new ArrayList<>();

        for (KindergartenTeamMonitor kindergartenTeamMonitor : kindergartenTeamMonitorList) {
            KindergartenMonitorTeamAo kindergartenMonitorTeamAo = new KindergartenMonitorTeamAo();
            kindergartenMonitorTeamAo.setTeamId(kindergartenTeamMonitor.getTeamId());

            List<MatchHardware> matchHardwares = matchHardwareService.getMatchHardwareByMatchId(kindergartenMonitor.getId(), kindergartenTeamMonitor.getTeamId(), "MONITOR");
            Map<Long, MatchHardware> matchHardwareMap = matchHardwares.stream()
                    .collect(Collectors.toMap(MatchHardware::getHardwareId, Function.identity()));
            kindergartenMonitorTeamAo.setHardware(matchHardwares.stream().filter(e -> e.getStarted()).count());
            kindergartenMonitorTeamAo.setPeopleNum(studentInfoService.count(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, kindergartenMonitorTeamAo.getTeamId())));
            kindergartenMonitorTeamAo.setStartTime(kindergartenMonitor.getStartTime().getTime());
            List<ExerciseConditionAo> personDatas = personDataMap.get(kindergartenMonitorTeamAo.getTeamId());


            List<ExerciseConditionAo> personDatas1 = fillKindergartenMonitorAo(kindergartenTeamMonitor.getTeamId());

            Map<Long, ExerciseConditionAo> personDataMap1 = null;
            if (personDatas != null) {
                kindergartenMonitorTeamAo.setExerciseStandardNum(personDatas.stream().filter(e -> {
                    return calulateCommonMethod.standard(e.getExerciseTime()) != 0;
                }).count());
                personDataMap1 = personDatas.stream()
                        .collect(Collectors.toMap(ExerciseConditionAo::getStudentId, Function.identity()));
            }

            Team team = teamService.getTeam(kindergartenMonitorTeamAo.getTeamId());
            kindergartenMonitorTeamAo.setMonitorId(kindergartenMonitor.getId());
            kindergartenMonitorTeamAo.setStatus(kindergartenMonitor.getStatus());
            kindergartenMonitorTeamAo.setTeamName(team.getFullName());
            kindergartenMonitorTeamAo.setSportsMonitorTime(team.getSportsMonitorTime());
            kindergartenMonitorTeamAo.setCaseColor(team.getCaseColor());
            kindergartenMonitorTeamAo.setMonitorSort(team.getMonitorSort());

            for (ExerciseConditionAo exerciseConditionAo : personDatas1) {
                if (personDataMap1 != null) {
                    ExerciseConditionAo exerciseConditionAo1 = personDataMap1.get(exerciseConditionAo.getStudentId());
                    if (exerciseConditionAo1 != null) {
                        exerciseConditionAo.setDate(exerciseConditionAo1.getDate());
                        exerciseConditionAo.setExerciseTime(exerciseConditionAo1.getExerciseTime());
                        exerciseConditionAo.setCalorie(exerciseConditionAo1.getCalorie());
                        exerciseConditionAo.setRunDistance(exerciseConditionAo1.getRunDistance());
                        exerciseConditionAo.setRanking(exerciseConditionAo1.getRanking());
                    }
                }
                Hardware hardware = hardwareService.getByStuId(exerciseConditionAo.getStudentId(), null);
                if (hardware != null) {
                    exerciseConditionAo.setHardware(hardware);
                    exerciseConditionAo.setStart(matchHardwareMap.get(hardware.getId()) != null ? matchHardwareMap.get(hardware.getId()).getStarted() : false);
                }
                KindergartenMonitorStudent kindergartenMonitorStudent = kindergartenMonitorStudentService.getOneByMonitorIdAndStudentId(monitorId, exerciseConditionAo.getStudentId());
                if (kindergartenMonitorStudent != null) {
                    exerciseConditionAo.setCancel(kindergartenMonitorStudent.getCancel());
                }
            }
            personDatas1.sort(Comparator.comparingInt(ExerciseConditionAo::getSortNum));
            kindergartenMonitorTeamAo.setExerciseConditionAos(personDatas1);
            kindergartenMonitorTeamAos.add(kindergartenMonitorTeamAo);
        }

        List<KindergartenMonitorTeamAo> kindergartenMonitorTeamAos1 = new ArrayList<>();
        kindergartenMonitorTeamAos.sort(Comparator.comparingInt(KindergartenMonitorTeamAo::getMonitorSort));
        kindergartenMonitorTeamAos1.addAll(kindergartenMonitorTeamAos);

        return kindergartenMonitorTeamAos1;
    }

    @Override
    public List<KindergartenMonitorTeamAo> recently(Long schoolId, Long teamId, String date) {
        String createData = date + " 00:00:00";
        String stopData = date + " 23:59:59";

        List<Team> kindergartenTeamMonitorList = teamService.list(new LambdaQueryWrapper<Team>().eq(Team::getId, teamId));
        Map<Long, List<ExerciseConditionAo>> personDataMap = personDataService.findByMonitor(teamId, createData, stopData).stream()
                .collect(Collectors.groupingBy(ExerciseConditionAo::getTeamId));
        if (personDataMap == null) {
            return fillKindergartenMonitorTeamAo(schoolId, teamId);
        }
        List<KindergartenMonitorTeamAo> kindergartenMonitorTeamAos = new ArrayList<>();

        for (Team team : kindergartenTeamMonitorList) {
            KindergartenMonitorTeamAo kindergartenMonitorTeamAo = new KindergartenMonitorTeamAo();
            kindergartenMonitorTeamAo.setTeamId(team.getId());
            kindergartenMonitorTeamAo.setPeopleNum(studentInfoService.count(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, kindergartenMonitorTeamAo.getTeamId())));
            List<ExerciseConditionAo> personDatas = personDataMap.get(kindergartenMonitorTeamAo.getTeamId());
            List<ExerciseConditionAo> personDatas1 = fillKindergartenMonitorAo(team.getId());

            Map<Long, ExerciseConditionAo> personDataMap1 = null;

            if (personDatas != null) {
                kindergartenMonitorTeamAo.setExerciseStandardNum(personDatas.stream().filter(e -> {
                    return calulateCommonMethod.standard(e.getExerciseTime()) != 0;
                }).count());
                personDataMap1 = personDatas.stream()
                        .collect(Collectors.toMap(ExerciseConditionAo::getStudentId, Function.identity()));
            }

            kindergartenMonitorTeamAo.setTeamName(team.getFullName());
            kindergartenMonitorTeamAo.setSportsMonitorTime(team.getSportsMonitorTime());
            kindergartenMonitorTeamAo.setCaseColor(team.getCaseColor());
            kindergartenMonitorTeamAo.setMonitorSort(team.getMonitorSort());

            for (ExerciseConditionAo exerciseConditionAo : personDatas1) {
                if (personDataMap1 != null) {
                    ExerciseConditionAo exerciseConditionAo1 = personDataMap1.get(exerciseConditionAo.getStudentId());
                    if (exerciseConditionAo1 != null) {
                        exerciseConditionAo.setDate(exerciseConditionAo1.getDate());
                        exerciseConditionAo.setExerciseTime(exerciseConditionAo1.getExerciseTime());
                        exerciseConditionAo.setCalorie(exerciseConditionAo1.getCalorie());
                        exerciseConditionAo.setRunDistance(exerciseConditionAo1.getRunDistance());
                        exerciseConditionAo.setRanking(exerciseConditionAo1.getRanking());
                    }
                }
            }
            personDatas1.sort(Comparator.comparingInt(ExerciseConditionAo::getSortNum));
            kindergartenMonitorTeamAo.setExerciseConditionAos(personDatas1);
            kindergartenMonitorTeamAos.add(kindergartenMonitorTeamAo);
        }

        List<KindergartenMonitorTeamAo> kindergartenMonitorTeamAos1 = new ArrayList<>();
        kindergartenMonitorTeamAos.sort(Comparator.comparingInt(KindergartenMonitorTeamAo::getMonitorSort));

        kindergartenMonitorTeamAos1.addAll(kindergartenMonitorTeamAos);

        List<Long> haveDateIds = kindergartenMonitorTeamAos.stream().map(KindergartenMonitorTeamAo::getTeamId).collect(Collectors.toList());
        List<KindergartenMonitorTeamAo> notDate = fillKindergartenMonitorTeamAo(schoolId, teamId).stream().filter(e -> {
            for (Long id : haveDateIds) {
                if (e.getTeamId().equals(id)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        kindergartenMonitorTeamAos1.addAll(notDate);

        return kindergartenMonitorTeamAos1;
    }

    public List<ExerciseConditionAo> fillKindergartenMonitorAo(Long teamId) {
        List<StudentInfo> studentInfos = studentInfoService.list(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, teamId));
        List<ExerciseConditionAo> exerciseConditionAos = new ArrayList<>();
        for (StudentInfo studentInfo : studentInfos) {
            ExerciseConditionAo exerciseConditionAo = new ExerciseConditionAo();
            exerciseConditionAo.setTeamId(teamId);
            exerciseConditionAo.setStudentId(studentInfo.getId());
            exerciseConditionAo.setStudentName(studentInfo.getName());
            exerciseConditionAo.setHeadImg(studentInfo.getHeadImg());
            exerciseConditionAo.setWeight(studentInfo.getWeight());
            exerciseConditionAo.setHeight(studentInfo.getHeight());
            exerciseConditionAo.setAge(studentInfo.getAge());
            exerciseConditionAo.setExerciseTime(-1);
            Hardware hardware = hardwareService.getByStuId(studentInfo.getId(), null);
            exerciseConditionAo.setHardware(hardware);
            exerciseConditionAos.add(exerciseConditionAo);
        }
        return exerciseConditionAos;
    }

    public List<KindergartenMonitorTeamAo> fillKindergartenMonitorTeamAo(Long schoolId, Long teamId) {
        LambdaQueryWrapper<Team> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Team::getSchoolId, schoolId);
        wrapper.eq(teamId != -1, Team::getId, teamId);
        wrapper.eq(Team::getSportsMonitor, true);
        wrapper.orderByAsc(Team::getMonitorSort);

        List<Team> teams = teamService.list(wrapper);
        List<KindergartenMonitorTeamAo> kindergartenMonitorTeamAos = new ArrayList<>();

        for (Team team : teams) {
            List<StudentInfo> studentInfos = studentInfoService.list(new LambdaQueryWrapper<StudentInfo>().eq(StudentInfo::getTeamId, team.getId()));
            KindergartenMonitorTeamAo kindergartenMonitorTeamAo = new KindergartenMonitorTeamAo();
            kindergartenMonitorTeamAo.setTeamName(team.getFullName());
            kindergartenMonitorTeamAo.setMonitorSort(team.getMonitorSort());
            kindergartenMonitorTeamAo.setSportsMonitorTime(team.getSportsMonitorTime());
            kindergartenMonitorTeamAo.setCaseColor(team.getCaseColor());
            kindergartenMonitorTeamAo.setTeamId(team.getId());
            kindergartenMonitorTeamAo.setHardware(-1);
            kindergartenMonitorTeamAo.setPeopleNum(studentInfos.size());
            kindergartenMonitorTeamAo.setExerciseStandardNum(-1);
            List<ExerciseConditionAo> exerciseConditionAos = new ArrayList<>();
            for (StudentInfo studentInfo : studentInfos) {
                ExerciseConditionAo exerciseConditionAo = new ExerciseConditionAo();
                exerciseConditionAo.setTeamId(teamId);
                exerciseConditionAo.setStudentId(studentInfo.getId());
                exerciseConditionAo.setStudentName(studentInfo.getName());
                exerciseConditionAo.setHeadImg(studentInfo.getHeadImg());
                exerciseConditionAo.setWeight(studentInfo.getWeight());
                exerciseConditionAo.setHeight(studentInfo.getHeight());
                exerciseConditionAo.setAge(studentInfo.getAge());
                exerciseConditionAo.setExerciseTime(0);
                Hardware hardware = hardwareService.getByStuId(studentInfo.getId(), null);
                exerciseConditionAo.setHardware(hardware);
                exerciseConditionAos.add(exerciseConditionAo);
            }

            kindergartenMonitorTeamAo.setExerciseConditionAos(exerciseConditionAos);
            kindergartenMonitorTeamAos.add(kindergartenMonitorTeamAo);
        }
        return kindergartenMonitorTeamAos;
    }

    @Override
    @Transactional
    public boolean uploadData(String strJSONArray, Long matchId, String matchType) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        //整合body
        JSONArray jsonArray = settleUploadBody(strJSONArray, matchId, matchType);
        Long teamId = jsonArray.getJSONObject(0).getLong("teamId");
        JSONObject o = getTeamExerciseStartAndStopTime(matchId);
        String origin = dateFormat.format(new Date(o.getLong("origin")));
        String destination = dateFormat.format(new Date(o.getLong("destination")));

        //发送计算请求
        RestTemplate restTemplate = new RestTemplate();
        String url = systemArg.getDomain_algorithm() + "?origin=" + origin + "&destination=" + destination;
        HttpEntity<JSONArray> entity = new HttpEntity<>(jsonArray);
        ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.POST, entity, JSONObject.class);
        if (!response.getBody().getBoolean("success")) {
            throw new IllegalArgumentException("数据上传失败");
        }
        //存个人数据
        if (!calulateCommonMethod.savePersonData(response.getBody().getJSONObject("data"), teamId, matchId, matchType)) {
            throw new IllegalArgumentException("数据上传失败");
        }
        //存团队数据
        if (teamId != 0 && !calulateCommonMethod.saveTeamData(response.getBody().getJSONObject("data"), teamId, matchId, matchType)) {
            throw new IllegalArgumentException("数据上传失败");
        }

        KindergartenMonitor kindergartenMonitor = getById(matchId);
        if (kindergartenMonitor != null && kindergartenMonitor.getStatus().equals(MatchStatusEnum.STARTED)) {
            kindergartenMonitor.setStatus(MatchStatusEnum.FINISHED);
            saveOrUpdate(kindergartenMonitor);
        }
        return true;
    }

    @Override
    public Object getPlayerData(Long matchId, Long studentId, Long teamId, String matchType, Boolean app) {
        KindergartenMonitor kindergartenMonitor = getById(matchId);
        if (studentId == null) {
            List<PersonData> personDataList = personDataService.findOfMatchId(matchId, teamId, matchType);
            TeamData teamData = teamDataService.findOfTeam(matchId, teamId, matchType);
            TeamDataAo teamDataAo = new TeamDataAo();
            teamDataAo.setKindergartenMonitor(kindergartenMonitor);
            if (teamData == null) {
                calulateCommonMethod.calculateTeamAvg(null, teamDataAo, null);
                return teamDataAo;
            }
            Team team = teamService.getById(teamId);
            teamDataAo.setTeamId(teamData.getTeamId());
            teamDataAo.setMatchId(teamData.getMatchId());
            teamDataAo.setMatchType(teamData.getMatchType());
            teamDataAo.setCurves(teamData.getCurves());
            teamDataAo.setExerciseTime(teamData.getExerciseTime());
            teamDataAo.setFullName(team.getFullName());
            teamDataAo.setShortName(team.getShortName());
            teamDataAo.setLogo(team.getLogo());
            return calculateTeamData(teamDataAo, personDataList, matchType);
        }
        PersonData personData = personDataService.findOfMatchIdAndStudentId(matchType, matchId, studentId);
        if (personData == null) {
            JSONObject data = new JSONObject();
            data.put("kindergartenMonitor", kindergartenMonitor);
            return data;
        }
        PersonDataAo personDataAo = new PersonDataAo();
        BeanUtils.copyProperties(personData, personDataAo);
        List<PersonData> personDataList = personDataService.list(new LambdaQueryWrapper<PersonData>().eq(PersonData::getMatchType, matchType).eq(PersonData::getTeamId, teamId).eq(PersonData::getMatchId, kindergartenMonitor.getId()));
        personDataAo.setStudentInfo(studentInfoService.getById(studentId));
        calculatePersonData(personDataAo, personDataList);
        Team team = teamService.getById(personData.getTeamId());
        if (team != null) {
            SchoolInfo schoolInfo = schoolInfoService.getById(team.getSchoolId());
            personDataAo.setSchoolLogo(schoolInfo.getLogo());
            personDataAo.setSchoolPub(schoolInfo.getPub());
            personDataAo.setSchoolName(schoolInfo.getName());
            personDataAo.setFullName(team.getFullName());
            personDataAo.setShortName(team.getShortName());
        }
        personDataAo.setKindergartenMonitor(kindergartenMonitor);
        if (app) {//如果是app查看设置为查看状态
            personData.setAppLook(true);
            personDataService.saveOrUpdate(personData);
        }
        return personDataAo;
    }

    public void calculatePersonData(PersonDataAo personDataAo, List<PersonData> personDataList) {
        List<PersonData> historyPersonDataList = personDataService.findOfMatchIdByHistStu(personDataAo.getStudentId(), personDataAo.getMatchId(), "MONITOR");
        calulateCommonMethod.setPersonBasicData(personDataList, historyPersonDataList, personDataAo);
    }

    public TeamDataAo calculateTeamData(TeamDataAo teamDataAo, List<PersonData> personDatas, String matchType) {
        Long absent = 0l;
        Long cancel = 0l;
        List<KindergartenMonitorStudent> gameStudents = kindergartenMonitorStudentService.getList(teamDataAo.getMatchId(), teamDataAo.getTeamId());
        absent = gameStudents.stream().filter(e -> !e.getStatus()).count();
        List<MatchHardware> matchHardwares = matchHardwareService.getMatchHardwareByMatchId(teamDataAo.getMatchId(), teamDataAo.getTeamId(), matchType);
        teamDataAo.setHardware(matchHardwares.stream().filter(e -> e.getStarted()).count());
        teamDataAo.setAbsent(absent.intValue());
        teamDataAo.setNoUploaded(teamDataAo.getHardware().intValue() - teamDataAo.getUploaded() - cancel.intValue());
        teamDataAo.setUploaded(personDatas.size());
        List<TeamData> teamDatas = teamDataService.findOfTeamList(teamDataAo.getTeamId(), "MONITOR").stream().filter(e -> !(e.getMatchId().equals(teamDataAo.getMatchId()))).collect(Collectors.toList());
        //公共方法设置基本数据
        calulateCommonMethod.setTeamDataAoBasicData(teamDataAo, personDatas, teamDatas);
        return teamDataAo;
    }

    public JSONArray settleUploadBody(String strJSONArray, Long matchId, String matchType) {
        Map<Long, RawData> map = new HashMap<>();
        JSONArray jsonArray = strJSONArray == null ? new JSONArray() : JSONArray.parseArray(strJSONArray);
        getPersonExerciseStartAndStopTime(matchId, jsonArray, matchType);
        //查出已经上传过的body
        List<RawData> rawDataList = rawDataService.findOfMatch(matchType, matchId, jsonArray.getJSONObject(0).getLongValue("teamId"));
        Map<Long, RawData> rawMap = rawDataList.stream().collect(Collectors.toMap(RawData::getStudentId, Function.identity(), (key1, key2) -> key2));
        List<RawData> rawDatas = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            jsonArray.getJSONObject(i).put("userId", jsonArray.getJSONObject(i).getLongValue("studentId"));
            jsonArray.getJSONObject(i).put("teamId", jsonArray.getJSONObject(i).getLongValue("teamId"));
            Long stuId = jsonArray.getJSONObject(i).getLongValue("studentId");
            RawData rawData;
            if (rawMap.containsKey(stuId)) {
                rawDataList.remove(rawMap.get(stuId));
                rawData = rawMap.get(stuId).setRaw(jsonArray.getJSONObject(i));
            } else {
                rawData = new RawData();
                rawData.setMatchId(matchId);
                rawData.setStudentId(stuId);
                rawData.setRaw(jsonArray.getJSONObject(i));
                rawData.setMatchType(matchType);
                rawData.setTeamId(jsonArray.getJSONObject(i).getLongValue("teamId"));
            }
            map.put(stuId, rawData);
            rawDatas.add(rawData);
        }
        rawDataService.saveOrUpdateBatch(rawDatas);
        //将之前上传的数据 一起算
        rawDataList.forEach(o -> jsonArray.add(o.getRaw()));
        return jsonArray;
    }


    /**
     * 获取比赛运动的起始和结束时间（个人）
     */
    public void getPersonExerciseStartAndStopTime(Long matchId, JSONArray jsonArray, String matchType) {
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            long origin = 0l;
            long destination = 0l;
            KindergartenMonitor kindergartenMonitor = getById(matchId);
            origin = kindergartenMonitor.getStartTime().getTime();
            destination = kindergartenMonitor.getStopTime().getTime();
            object.put("origin", origin);
            object.put("destination", destination);
            object.put("timeSlot", new JSONArray());
            jsonArray.set(i, object);
        }
    }

    /**
     * 获取比赛运动的起始和结束时间（团队）
     */
    public JSONObject getTeamExerciseStartAndStopTime(Long matchId) {
        JSONObject object = new JSONObject();
        KindergartenMonitor game = getById(matchId);
        object.put("origin", game.getStartTime().getTime());
        object.put("destination", game.getStopTime().getTime());
        return object;
    }

    @Override
    public Object getPhysicalAnalysis(Long matchId, Long teamId) {
        return calulateCommonMethod.getPhysicalAnalysis(matchId, teamId, "MONITOR");
    }

    @Override
    public JSONArray moveDistanceRanking(Long matchId, String rankingKey, List<PersonData> personDatas, Long teamId) {
        return calulateCommonMethod.moveDistanceRanking(matchId, rankingKey, personDatas, teamId, "MONITOR");
    }

    /**
     * @desc: 接口未使用
     * @author: DH
     * @date: 2024/1/8 18:27
     */
    @Override
    public List<DataStatisticsAo> statistics(Long teamId, Long studentId, Integer type, JSONArray array) {
        JSONObject object = getStatisticsDateCondition(array, type);
        List<DataStatisticsAo> dataStatisticsAos = personDataService.dataStatistics(teamId, studentId, object.getString("start"), object.getString("stop"), type);
        List<DataStatisticsAo> dataStatisticsAos1 = new ArrayList<>();
        boolean flag;
        for (int i = 0; i < array.size(); i++) {
            flag = true;
            for (DataStatisticsAo dataStatisticsAo : dataStatisticsAos) {
                if (type == 2 && dataStatisticsAo.getDate().equals(DateUtil.getWeekOfYear(array.getString(i)))) {
                    dataStatisticsAo.setDate(array.getString(i));
                    dataStatisticsAos1.add(dataStatisticsAo);
                    flag = false;
                    break;
                } else if (dataStatisticsAo.getDate().equals(array.getString(i))) {
                    dataStatisticsAos1.add(dataStatisticsAo);
                    flag = false;
                    break;
                }
            }
            if (flag) {//填充空的数据
                DataStatisticsAo dataStatisticsAo = new DataStatisticsAo();
                dataStatisticsAo.setDate(array.getString(i));
                dataStatisticsAos1.add(dataStatisticsAo);
            }
        }
        return dataStatisticsAos1;
    }

    @Override
    public Object sportsRanking(Long schoolId, String start, String stop, Long teamId, Integer type, String key) {
        final String startTime1 = start + " 00:00:00";
        final String endTime1 = stop + " 59:59:59";
        List<TeamExerciseAo> teamExerciseAos = personDataService.sportsRanking(schoolId, startTime1, endTime1, teamId, type);
        if (key.equals("run")) {
            teamExerciseAos = teamExerciseAos.stream().sorted(Comparator.comparing(TeamExerciseAo::getRunDistance).reversed()).collect(Collectors.toList());
        } else if (key.equals("calorie")) {
            teamExerciseAos = teamExerciseAos.stream().sorted(Comparator.comparing(TeamExerciseAo::getCalorie).reversed()).collect(Collectors.toList());
        } else {
            teamExerciseAos = teamExerciseAos.stream().sorted(Comparator.comparing(TeamExerciseAo::getPracticalExerciseTime).reversed()).collect(Collectors.toList());
        }
        return teamExerciseAos;
    }

    @Override
    public Object statisticsNew(Long teamId, Long studentId, String startTime, String endTime, Integer type) {
        final String startTime1 = startTime + " 00:00:00";
        final String endTime1 = endTime + " 59:59:59";
        List<DataStatisticsNewAo> dataStatisticsAos = personDataService.dataStatisticsNew(teamId, studentId, startTime1, endTime1, type);
        return studentId == -1 ? calulateCommonMethod.statisticsNew(dataStatisticsAos, type) : calulateCommonMethod.statisticsNewPerson(dataStatisticsAos.stream().peek(e -> {
            if (type == 1) {
                e.setPersonDataList(personDataService.getCurveList(startTime1, endTime1, e.getStudentId()));
            }
            e.setTeamId(teamId);
            Team team = teamService.getById(teamId);
            e.setTeamName(team.getFullName());
            e.setSchollId(team.getSchoolId());
        }).collect(Collectors.toList()), type).stream().findFirst().orElse(null);
    }

    @Override
    public Object personColumnar(String start, String stop, Long teamId, Long studentId, Integer type) {
        List<PersonColumnarAo> personColumnarAos = personDataService.personColumnar(start, stop, studentId, teamId, type);
        List<PersonColumnarAo> personColumnarAos1 = new ArrayList<>();
        List<String> dates = DateUtil.displayDateIncrement(start, stop, type);
        for (PersonColumnarAo personColumnarAo : personColumnarAos) {
            long exerciseTime = personColumnarAo.getExerciseTime();
            long jump = calulateCommonMethod.calculatePercentage((personColumnarAo.getJump() * 0.15), exerciseTime);
            long run = calulateCommonMethod.calculatePercentage(personColumnarAo.getRunExerciseTime(), exerciseTime);
            long step = calulateCommonMethod.calculatePercentage(personColumnarAo.getStepExerciseTime(), exerciseTime);
            long quiet = 100 - jump - step - run;
            personColumnarAo.setRun(run);
            personColumnarAo.setJump(jump);
            personColumnarAo.setStep(step);
            personColumnarAo.setQuiet(quiet - (quiet / 3));
            personColumnarAo.setMeditation(quiet / 3);
        }
        boolean flag;
        for (int i = 0; i < dates.size(); i++) {
            flag = true;
            for (PersonColumnarAo personColumnarAo : personColumnarAos) {
                if (personColumnarAo.getDate().equals(dates.get(i))) {
                    personColumnarAos1.add(personColumnarAo);
                    flag = false;
                    break;
                }
            }
            if (flag) {//填充空的数据
                PersonColumnarAo personColumnarAo = new PersonColumnarAo();
                personColumnarAo.setDate(dates.get(i));
                personColumnarAos1.add(personColumnarAo);
            }
        }
        return personColumnarAos1;
    }


    @Override
    public Object teamExercise(String start, String stop, Long teamId, Integer type, String key) {
        final String startTime1 = start + " 00:00:00";
        final String endTime1 = stop + " 59:59:59";
        List<TeamExerciseAo> teamExerciseAos = personDataService.teamExerciseTime(startTime1, endTime1, teamId, type);
        if (key.equals("run")) {
            teamExerciseAos = teamExerciseAos.stream().sorted(Comparator.comparing(TeamExerciseAo::getRunDistance).reversed()).collect(Collectors.toList());
        } else if (key.equals("calorie")) {
            teamExerciseAos = teamExerciseAos.stream().sorted(Comparator.comparing(TeamExerciseAo::getCalorie).reversed()).collect(Collectors.toList());
        } else {
            teamExerciseAos = teamExerciseAos.stream().sorted(Comparator.comparing(TeamExerciseAo::getPracticalExerciseTime).reversed()).collect(Collectors.toList());
        }
        return teamExerciseAos;
    }

    @Override
    public Object personExercise(String start, String stop, Long teamId, Long studentId, Integer type) {
        final String startTime1 = start + " 00:00:00";
        final String endTime1 = stop + " 59:59:59";
        List<TeamExerciseAo> teamExerciseAos = personDataService.personExerciseTime(startTime1, endTime1, studentId, teamId, type);
        List<TeamExerciseAo> teamExerciseAos1 = new ArrayList<>();
        List<String> dates = DateUtil.displayDateIncrement(start, stop, type);

        boolean flag;
        for (int i = 0; i < dates.size(); i++) {
            flag = true;
            for (TeamExerciseAo teamExerciseAo : teamExerciseAos) {
                if (teamExerciseAo.getDate().equals(dates.get(i))) {
                    teamExerciseAos1.add(teamExerciseAo);
                    flag = false;
                    break;
                }
            }
            if (flag) {//填充空的数据
                TeamExerciseAo teamExerciseAo = new TeamExerciseAo();
                teamExerciseAo.setDate(dates.get(i));
                teamExerciseAos1.add(teamExerciseAo);
            }
        }
        return teamExerciseAos1;
    }


    @Override
    public Object getHaveDateTime(String start, String stop, Long teamId, Long studentId) {
        StudentInfo studentInfo = studentInfoService.getStudentInfo(studentId);
        List<Long> teamIds = new ArrayList<>();
        if (teamId == -1l) {
            List<Team> teams = teamService.getTeamsBySchoolId(studentInfo.getSchoollId());
            teamIds = teams.stream().map(Team::getId).collect(Collectors.toList());
        } else {
            teamIds.add(teamId);
        }
        return personDataService.exerciseTime(teamIds, studentId, start, stop, "MONITOR");
    }

    @Override
    public Object getHaveDateTimeLately(Long studentId) {
        PersonData personData = personDataService.getOne(new LambdaQueryWrapper<PersonData>().eq(PersonData::getStudentId, studentId).eq(PersonData::getMatchType, "MONITOR").orderByDesc(PersonData::getId).last("limit 1"));
        if (personData != null) {
            return personData.getCreateTime().getTime();
        }
        return -1l;
    }

    @Override
    public Object teamRunCalorieSum(Long teamId, Integer type, String start, String stop) {
        TeamExerciseAo teamExerciseAo = personDataService.teamRunCalorieSum(teamId, type, start, stop);
        JSONObject object = new JSONObject();
        if (teamExerciseAo != null) {
            object.put("sumRun", teamExerciseAo.getRunDistance());
            object.put("sumCalorie", teamExerciseAo.getCalorie());
        } else {
            object.put("sumRun", 0);
            object.put("sumCalorie", 0);
        }
        return object;
    }


    public JSONObject getStatisticsDateCondition(JSONArray array, Integer type) {
        JSONObject object = new JSONObject();
        String startTime = "";
        String stopTime = "";
        if (type == 1 || type == 2) {
            startTime = array.getString(0) + " 00:00:00";
            stopTime = array.getString(array.size() - 1) + " 59:59:59";
        } else if (type == 3) {
            startTime = array.getString(0) + "-01 00:00:00";
            stopTime = array.getString(array.size() - 1) + "-31 59:59:59";
        } else {
            startTime = array.getString(0) + "-01-01 00:00:00";
            stopTime = array.getString(array.size() - 1) + "-12-31 59:59:59";
        }
        object.put("start", startTime);
        object.put("stop", stopTime);
        return object;
    }

}
