package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.GameExtraGroup;
import io.geekidea.springbootplus.using.param.GameExtraGroupPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
public interface GameExtraGroupService extends BaseService<GameExtraGroup> {

    /**
     * 保存
     *
     * @param gameExtraGroup
     * @return
     * @throws Exception
     */
    boolean saveGameExtraGroup(GameExtraGroup gameExtraGroup) throws Exception;

    /**
     * 修改
     *
     * @param gameExtraGroup
     * @return
     * @throws Exception
     */
    boolean updateGameExtraGroup(GameExtraGroup gameExtraGroup) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteGameExtraGroup(Long id) throws Exception;


    /**
     * 获取分页对象
     *
     * @param gameExtraGroupQueryParam
     * @return
     * @throws Exception
     */
    Paging<GameExtraGroup> getGameExtraGroupPageList(GameExtraGroupPageParam gameExtraGroupPageParam) throws Exception;


    List<GameExtraGroup> getGameExtraGroup(Long gameExtaId);
}
