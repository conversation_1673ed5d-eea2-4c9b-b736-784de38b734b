package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Audit对象")
@TableName(autoResultMap = true)
public class  Audit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "不能为空")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("app用户id")
    private Long userId;

    @ApiModelProperty("pad球队id")
    private Long teamId;

    @ApiModelProperty("0：审核 1：审核通过 2：不通过")
    private Integer status;

    @ApiModelProperty("grade:足球赛事等级 basketballGrade:篮球赛事等级")
    private String type;

    @ApiModelProperty("审核图片 [\"url\",\"url2\"]")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray imgs;

    @ApiModelProperty("审核文字 暂时不用")
    private String writing;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    @ApiModelProperty("{\"grade\":修改的赛事等级}")
    private JSONObject body;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @ApiModelProperty("逻辑删除")
    @TableLogic
    private Boolean deleted;
}
