package io.geekidea.springbootplus.using.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;
import java.util.List;

import io.geekidea.springbootplus.using.enums.MatchStatusEnum;
import io.geekidea.springbootplus.using.enums.UploadedEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Game对象")
@TableName(autoResultMap = true)
public class Game extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("2篮球 3足球")
    private Long courseId;

    @ApiModelProperty("比赛名称")
    private String title;

    @ApiModelProperty("比赛时间")
    private Date gameTime;

    @ApiModelProperty("比赛地点")
    private String location;

    @ApiModelProperty("主球队id ")
    private Long teamId;

    @ApiModelProperty("主球队名字 ")
    private String teamName;

    @ApiModelProperty("主球队LOGO")
    private String teamLogo;

    @ApiModelProperty("主球队得分")
    private Integer teamScore;

    @ApiModelProperty("对手球队id 没有就传-1")
    private Long opponentId;

    @ApiModelProperty("对手名称")
    private String opponentName;

    @ApiModelProperty("对手得分")
    private Integer opponentScore;

    @ApiModelProperty("客队logo")
    private String opponentLogo;

    @ApiModelProperty("足球是 1-11   篮球是 1-5")
    private Integer side;

    @ApiModelProperty("时长")
    private Integer duration;

    @ApiModelProperty("比赛状态(未开始:NOTSTARTED,已开始:STARTED,已结束:FINISHED)")
    private MatchStatusEnum status;

    @ApiModelProperty("备注")
    private String note;

    @ApiModelProperty("结束机制类型0：120分钟自动过期 1：手动结束 2：以第一位学员启动设备时间延后120分钟过期 3：以第一个控制设备学员时间延后120分钟过期")
    private Integer finishType;

    @ApiModelProperty("结束比赛用的开始时间（数据处理）")
    private Date finishStartTime;

    @ApiModelProperty("比赛结束时间")
    private Date finishStopTime;

    @ApiModelProperty("是否有球队弃权")
    private Boolean waiver;

    @ApiModelProperty("创建该类型的第几场训练")
    private Integer amount;

    @ApiModelProperty("加时客队比分")
    private Integer overtimeOpponentScore;

    @ApiModelProperty("加时主队比分")
    private Integer overtimeTeamScore;

    @ApiModelProperty("是否加时")
    private Boolean overtime;

    @ApiModelProperty("点球客队比分")
    private Integer pointSphereOpponentScore;

    @ApiModelProperty("点球主队比分")
    private Integer pointSphereTeamScore;

    @ApiModelProperty("是否点球")
    private Boolean pointSphere;

    @ApiModelProperty("赛后总结")
    private String summarize;

    @ApiModelProperty("是否实时对战")
    private Boolean realTime;

    @ApiModelProperty("是否开启gps null：未知")
    private Boolean gps;

    @ApiModelProperty("是否设置过模式")
    private Boolean pattern;

    @ApiModelProperty("跑动距离 m")
    private Integer runDistance;

    @ApiModelProperty("完成度 {\"red\":0.6,\"yellow\":0.2,\"green\":0.5}")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject completeness;

    @ApiModelProperty("最后学员启动时间")
    @TableField("last_start_time")
    private Date lastStartTime;

    @ApiModelProperty("是否app用户创建")
    private Boolean app;

    @ApiModelProperty("创建者id")
    private Long createUserId;

    @ApiModelProperty("省电时间 单位min分钟")
    private Long powerSaving;

    @ApiModelProperty("数据实际开始统计时间搓")
    private Long realDataTime;

    @ApiModelProperty("查询者所属球队id")
    @TableField(exist = false)
    private Long userTeamId;

    @ApiModelProperty("训练是否作废（个人）")
    @TableField(exist = false)
    private Boolean cancel = false;

    @TableField(exist = false)
    @ApiModelProperty("是否同步数据 //NOT未同步 - ALL全部同步 - PORTION部分同步")
    private UploadedEnum uploaded;

    @TableField(exist = false)
    @ApiModelProperty("是否设置了出勤队员")
    private Boolean setAttendance;

    @ApiModelProperty("{\"1\":计时数量,\"2\":计数数量,\"3\":场记数量}")
    @TableField(exist = false)
    private Object count;

    @TableField(exist = false)
    @ApiModelProperty("比赛剩余秒")
    private Long gameSec;

    @ApiModelProperty("是否有设备")
    private Boolean haveHard;

    @ApiModelProperty("是否有已启动的设备")
    @TableField(exist = false)
    private Boolean haveHardwareStarted = false;

    @TableField(exist = false)
    @ApiModelProperty("快速场记名字 不需要就不传")
    private String fastFdName;

    @ApiModelProperty("查询者跑动距离 m")
    @TableField(exist = false)
    private Integer personRunDistance;

    @ApiModelProperty("参考 getPlayerData 字段  extra里的 fieldGroup")
    @TableField(exist = false)
    private List<FieldData> fieldNotes;

    @ApiModelProperty("卡路里(kcal）")
    @TableField(exist = false)
    private Integer calorie;

    @ApiModelProperty("用户是否启动了这场比赛")
    @TableField(exist = false)
    private Boolean currentUserStatus;

    @ApiModelProperty("用户篮球分数")
    @TableField(exist = false)
    private Integer currentUserFraction;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
