package io.geekidea.springbootplus.using.service.impl;

import io.geekidea.springbootplus.using.entity.TacticsBoardPlayer;
import io.geekidea.springbootplus.using.mapper.TacticsBoardPlayerMapper;
import io.geekidea.springbootplus.using.service.TacticsBoardPlayerService;
import io.geekidea.springbootplus.using.param.TacticsBoardPlayerPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 战术板球员表，存储球员信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Slf4j
@Service
public class TacticsBoardPlayerServiceImpl extends BaseServiceImpl<TacticsBoardPlayerMapper, TacticsBoardPlayer> implements TacticsBoardPlayerService {

    @Autowired
    private TacticsBoardPlayerMapper tacticsBoardPlayerMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveTacticsBoardPlayer(TacticsBoardPlayer tacticsBoardPlayer) throws Exception {
        return super.save(tacticsBoardPlayer);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTacticsBoardPlayer(TacticsBoardPlayer tacticsBoardPlayer) throws Exception {
        return super.updateById(tacticsBoardPlayer);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteTacticsBoardPlayer(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<TacticsBoardPlayer> getTacticsBoardPlayerPageList(TacticsBoardPlayerPageParam tacticsBoardPlayerPageParam) throws Exception {
        Page<TacticsBoardPlayer> page = new PageInfo<>(tacticsBoardPlayerPageParam);
        LambdaQueryWrapper<TacticsBoardPlayer> wrapper = new LambdaQueryWrapper<>();
        IPage<TacticsBoardPlayer> iPage = tacticsBoardPlayerMapper.selectPage(page, wrapper);
        return new Paging<TacticsBoardPlayer>(iPage);
    }

}
