package io.geekidea.springbootplus.using.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(autoResultMap = true)
public class TeamExerciseAo {

    @ApiModelProperty("球队id")
    private Long teamId;

    @ApiModelProperty("学生id")
    private Long studentId;

    @ApiModelProperty("时间日期 根据查询的type动态返回 日：2022-02-26 周：45|今年第多少周 月：2022-02 年：2022")
    private String date;

    @ApiModelProperty("学生名字")
    private String name;

    @ApiModelProperty("球队名字")
    private String teamName;

    @ApiModelProperty("学生头像")
    private String headImg;

    @ApiModelProperty("卡路里(kcal）")
    private int calorie;

    @ApiModelProperty("跑动距离m")
    private int runDistance;

    @ApiModelProperty("跑动运动时间")
    @JsonIgnore
    private long runExerciseTime = 0l;

    @ApiModelProperty("步数运动时间")
    @JsonIgnore
    private long stepExerciseTime = 0l;

    @ApiModelProperty("实际运动时间")
    private long practicalExerciseTime = 0l;
}
