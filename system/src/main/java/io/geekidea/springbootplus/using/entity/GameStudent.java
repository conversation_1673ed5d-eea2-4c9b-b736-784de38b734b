package io.geekidea.springbootplus.using.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.geekidea.springbootplus.framework.common.entity.BaseEntity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import io.geekidea.springbootplus.framework.core.validator.groups.Update;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GameStudent对象")
public class GameStudent extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("训练表ID")
    private Long gameId;

    @ApiModelProperty("学生ID")
    private Long studentId;

    @ApiModelProperty("球队ID")
    private Long teamId;

    @ApiModelProperty("训练时间段")
    private String timeSlot;

    @ApiModelProperty("1,正常：0缺勤")
    private Boolean status;

    @ApiModelProperty("是否首发")
    private Integer starter;

    @ApiModelProperty("球场四角是否定位成功")
    private Integer fourPosition;

    @ApiModelProperty("比赛是否作废（个人）")
    private Boolean cancel;

    @ApiModelProperty("启动比赛时间")
    private Date startTime;

    @ApiModelProperty("停止比赛时间")
    private Date stopTime;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @TableLogic
    private Integer deleted;

}
