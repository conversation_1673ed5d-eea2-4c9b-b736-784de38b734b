package io.geekidea.springbootplus.using.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.geekidea.springbootplus.framework.util.UPushUtil;
import io.geekidea.springbootplus.using.entity.*;
import io.geekidea.springbootplus.using.mapper.FieldDataMapper;
import io.geekidea.springbootplus.using.mapper.FieldNotesMapper;
import io.geekidea.springbootplus.using.param.FieldDataPageParam;
import io.geekidea.springbootplus.using.service.*;
import io.geekidea.springbootplus.using.param.FieldNotesPageParam;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.geekidea.springbootplus.framework.common.service.impl.BaseServiceImpl;
import io.geekidea.springbootplus.framework.core.pagination.Paging;
import io.geekidea.springbootplus.framework.core.pagination.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Slf4j
@Service
public class FieldNotesServiceImpl extends BaseServiceImpl<FieldNotesMapper, FieldNotes> implements FieldNotesService {

    @Autowired
    private FieldNotesMapper fieldNotesMapper;
    @Autowired
    private FieldGroupService fieldGroupService;
    @Autowired
    private FieldDataService fieldDataService;
    @Autowired
    private IconService iconService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private HardwareService hardwareService;
    @Autowired
    private HardwareAppService hardwareAppService;
    @Autowired
    private FieldDataMapper fieldDataMapper;
    @Autowired
    private DrillService drillService;
    @Autowired
    private TransparentMessageService transparentMessageService;
    @Autowired
    UPushUtil uPushUtil;
    @Autowired
    private GameService gameService;
    @Autowired
    private UserAppService userAppService;
    @Autowired
    @Lazy
    private ProDataService proDataService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveFieldNotes(FieldNotes fieldNotes) throws Exception {
        fieldNotes.setCreateTime(new Date());
        fieldNotes.setUpdateTime(new Date());
        saveOrUpdate(fieldNotes);
        fieldNotes.getGroup().forEach(e -> {
            e.setFieldId(fieldNotes.getId());
            e.setCreateTime(new Date());
            e.setUpdateTime(new Date());
            fieldGroupService.saveOrUpdateBatch(fieldNotes.getGroup());
            e.getFieldData().forEach(s -> {
                s.setGroupId(e.getId());
                s.setFieldId(fieldNotes.getId());
                s.setCreateTime(new Date());
                s.setUpdateTime(new Date());
                if (fieldNotes.getType() == 1) {
                    String map = "{\"goal\":0,\"orthogonality\":0,\"assisting\":0,\"surpass\":0,\"pointsphere\":0,\"cornerkick\":0,\"freekick\":0,\"threatball\":0,\"heading\":0,\"raise\":0,\"preemption\":0,\"redcard\":0,\"shooting\":0,\"rules\":0,\"offside\":0,\"puking\":0,\"yellowcard\":0,\"owngoal\":0,\"waveshooting\":0,\"kickair\":0,\"stoperror\":0,\"defensiveerror\":0,\"passingerror\":0,\"wonderful\":0,\"excitingShot\":0,\"excitingSyeals\":0,\"longpass\":0,\"headgoal\":0,\"headclear\":0,\"clearance\":0,\"rescue\":0,\"pointMaking\":0,\"fatalerror\":0,\"wonderfulstop\":0,\"shoot\":0,\"threatenshoot\":0,\"scoring\":0,\"seriouserror\":0  }";
                    s.setDataMap(JSONObject.parseObject(map));
                } else if (fieldNotes.getType() == 3) {
                    String map = "{\"walk\": 0, \"assist\": 0, \"snatch\": 0, \"outBall\": 0, \"dribbler\": 0, \"headFoul\": 0, \"hookFoul\": 0, \"walkBall\": 0, \"backboard\": 0, \"blockShot\": 0, \"onePointer\": 0, \"pullPeople\": 0, \"pushPepole\": 0, \"treeBasket\": 0, \"twoPointer\": 0, \"flyingElbow\": 0, \"penaltyExit\": 0, \"illegalHands\": 0, \"threePointer\": 0, \"illegalAttack\": 0, \"illegalDefense\": 0, \"illegalDribble\": 0, \"threeViolation\": 0, \"intentionalBall\": 0, \"technicalfoul\": 0, \"violationfoul\": 0, \"comeback\": 0, \"amazing\": 0, \"nicepass\": 0, \"frontback\":0, \"afterback\":0, \"nullone\":0, \"nulltwo\":0, \"nullthree\":0, \"dunk\":0, \"helpball\":0}";
                    s.setDataMap(JSONObject.parseObject(map));
                }
            });
            fieldDataService.saveOrUpdateBatch(e.getFieldData());
        });
        return true;
    }

    @Override
    public boolean copyFieldNotes(Long id) {
        FieldNotes fieldNotes = getBaseMapper().selectById(id);
        List<FieldGroup> groupList = fieldGroupService.list(new LambdaQueryWrapper<FieldGroup>().eq(FieldGroup::getFieldId, id));
        fieldNotes.setGroup(groupList);
        fieldNotes.setCreateTime(new Date());
        fieldNotes.setUpdateTime(new Date());
        fieldNotes.setId(null);
        saveOrUpdate(fieldNotes);
        fieldNotes.getGroup().forEach(e -> {
            List<FieldData> dataList = fieldDataService.list(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId, id).eq(FieldData::getGroupId, e.getId()));
            e.setFieldId(fieldNotes.getId());
            e.setId(null);
            e.setCreateTime(new Date());
            e.setUpdateTime(new Date());
            fieldGroupService.saveOrUpdateBatch(fieldNotes.getGroup());
            e.setFieldData(dataList);
            e.getFieldData().forEach(s -> {
                s.setGroupId(e.getId());
                s.setFieldId(fieldNotes.getId());
                s.setCreateTime(new Date());
                s.setUpdateTime(new Date());
                s.setId(null);
            });
            fieldDataService.saveOrUpdateBatch(e.getFieldData());
        });
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateFieldNotes(FieldNotes fieldNotes) throws Exception {
        fieldNotes.setUpdateTime(new Date());
        saveOrUpdate(fieldNotes);
        fieldNotes.getGroup().forEach(e -> {
            if (e.getId() != null) {
                fieldGroupService.saveOrUpdate(e);
            }
            if (e.getFieldData() != null) {
                e.getFieldData().forEach(s -> {
                    s.setUpdateTime(new Date());
                    FieldData fieldData1 = fieldDataMapper.getId(s.getId());
                    if (fieldData1 != null && fieldData1.getUpMap() != null) {
                        JSONObject jsonObject = s.getDataMap();
                        Set<String> gaiKey = jsonObject.keySet();
                        JSONObject quanObject = JSONObject.parseObject(fieldData1.getUpMap());
                        Set<String> quanKey = quanObject.keySet();
                        JSONObject datamap = JSONObject.parseObject(fieldData1.getUpMap());
                        for (String quan : quanKey) {
                            for (String gai : gaiKey) {
                                datamap.put(gai, jsonObject.get(gai));
                            }
                        }
                        s.setDataMap(datamap);
                    }
                });
                fieldDataService.saveOrUpdateBatch(e.getFieldData());
            }
        });
        FieldNotes fieldNotes1 = getById(fieldNotes.getId());
        //发送透传消息
        org.json.JSONObject jsonObject = new org.json.JSONObject();
        jsonObject.put("fieldNotesId", fieldNotes.getId());
        if (fieldNotes.getMatchType().equals("DRILL")) {
            sendFieldNotesByStudent(jsonObject);
        } else {
            FieldGroup fieldGroup1 = fieldNotes.getGroup().get(0);
            FieldGroup fieldGroup2 = fieldNotes.getGroup().get(1);
            Game game = gameService.getById(fieldNotes1.getMatchId());
            if (game != null) {
                game.setTeamScore(fieldGroup1.getGroupFraction());
                game.setOpponentScore(fieldGroup2.getGroupFraction());
                gameService.saveOrUpdate(game);
                proDataService.delComprehensiveScoreRankingCacheArray(game.getTeamId(), fieldNotes1.getMatchId(), fieldNotes.getMatchType());
                proDataService.delComprehensiveScoreRankingCacheArray(game.getOpponentId(), fieldNotes1.getMatchId(), fieldNotes.getMatchType());
            }
            sendFieldNotesByUserApp(jsonObject);
        }
        Long teamId = 0l;
        if (fieldNotes.getMatchType().equals("DRILL")) {
            Drill drill = drillService.getById(fieldNotes1.getMatchId());
            if (drill != null) {
                teamId = drill.getTeamId();
                proDataService.delComprehensiveScoreRankingCacheArray(teamId, fieldNotes1.getMatchId(), fieldNotes1.getMatchType());
            }

        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteFieldNotes(Long id) throws Exception {
        return super.removeById(id);
    }

    @Override
    public Paging<FieldNotes> getFieldNotesPageList(FieldNotesPageParam fieldNotesPageParam) throws Exception {
        Page<FieldNotes> page = new PageInfo<>(fieldNotesPageParam, OrderItem.desc(getLambdaColumn(FieldNotes::getCreateTime)));
        LambdaQueryWrapper<FieldNotes> wrapper = new LambdaQueryWrapper<>();
        IPage<FieldNotes> iPage = fieldNotesMapper.selectPage(page, wrapper);
        for (FieldNotes fieldNotes : iPage.getRecords()) {
            Icon icon = iconService.getById(fieldNotes.getIconId());
            fieldNotes.setIconUrl(icon.getUrl());
        }
        return new Paging<FieldNotes>(iPage);
    }

    @Override
    public List<FieldNotes> getPageList(FieldNotesPageParam fieldNotesPageParam) {
        Page<FieldNotes> page = new PageInfo<>(fieldNotesPageParam, OrderItem.desc(getLambdaColumn(FieldNotes::getCreateTime)));
        IPage<FieldNotes> fieldNotesIPage = getBaseMapper().selectPage(page, new LambdaQueryWrapper<FieldNotes>().eq(FieldNotes::getMatchType, fieldNotesPageParam.getMatchType())
                .eq(FieldNotes::getMatchId, fieldNotesPageParam.getMatchId()));
        List<FieldNotes> list = fieldNotesIPage.getRecords();
        if (list.size() > 0) {
            for (FieldNotes fieldNotes : list) {
                List<FieldGroup> fieldGroupList = fieldGroupService.getBaseMapper().selectList(new LambdaQueryWrapper<FieldGroup>().eq(FieldGroup::getFieldId, fieldNotes.getId()));
                if (fieldGroupList.size() > 0) {
                    for (FieldGroup fieldGroup : fieldGroupList) {
                        List<FieldData> fieldDataList = fieldDataService.getBaseMapper().selectList(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId, fieldNotes.getId()).eq(FieldData::getGroupId, fieldGroup.getId()));
                        if (fieldDataList.size() > 0) {
                            for (FieldData fieldData : fieldDataList) {
                                if (fieldNotesPageParam.getMatchType().equals("DRILL")) {
                                    StudentInfo studentInfo = studentInfoService.getById(fieldData.getStuId());
                                    fieldData.setStuName(studentInfo.getName());
                                } else {
                                    UserApp userApp = userAppService.getById(fieldData.getStuId());
                                    fieldData.setStuName(userApp.getName());
                                }
                            }
                        }
                        fieldGroup.setFieldData(fieldDataList);
                    }
                }
                fieldNotes.setGroup(fieldGroupList);
            }
        }
        return list;
    }

    @Override
    public List<FieldNotes> getFieldNotesByCreate() {
        List<FieldNotes> fieldNotesList = getBaseMapper().selectList(new LambdaQueryWrapper<FieldNotes>().orderByDesc(FieldNotes::getCreateTime));
        if (fieldNotesList.size() > 0) {
            for (FieldNotes fieldNotes : fieldNotesList) {
                Icon icon = iconService.getById(fieldNotes.getIconId());
                fieldNotes.setIconUrl(icon.getUrl());
            }
        }
        return fieldNotesList;
    }

    @Override
    public List<FieldNotes> getGroupField(FieldNotesPageParam fieldNotesPageParam) {
        Page<FieldNotes> fieldNotesPage = new PageInfo<>(fieldNotesPageParam, OrderItem.desc(getLambdaColumn(FieldNotes::getCreateTime)));
        List<FieldNotes> fieldNotesList = fieldNotesMapper.selList(fieldNotesPage, fieldNotesPageParam.getMatchId(), fieldNotesPageParam.getMatchType()).getRecords();
        if (fieldNotesList.size() > 0) {
            for (FieldNotes fieldNotes : fieldNotesList) {
                List<FieldGroup> fieldGroupList = fieldGroupService.getBaseMapper().selectList(new LambdaQueryWrapper<FieldGroup>().eq(FieldGroup::getFieldId, fieldNotes.getId()));
                fieldNotes.setGroup(fieldGroupList);
//                for(FieldGroup fieldGroup: fieldNotes.getGroup()){
//                    List<FieldData> fieldDataList = fieldDataService.getBaseMapper().selectList(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId,fieldNotes.getId()).eq(FieldData::getGroupId,fieldGroup.getId()));
//                    for(int i = 0 ;i<fieldDataList.size();i++){
//                        //计算每一个学员的数据合并成小组数据
//                        String data = fieldDataList.get(i).getDataMap();
//                        JSONObject jsonObject = new JSONObject(data);
//                        Object yifen = jsonObject.get("walk");
//                    }
//                    fieldGroup.setFieldData(fieldDataList);
//                }
                Icon icon = iconService.getById(fieldNotes.getIconId());
                fieldNotes.setIconUrl(icon.getUrl());
            }
        }
        return fieldNotesList;
    }

    @Override
    public FieldNotes getGroupById(Long id) {
        FieldNotes fieldNotes = getBaseMapper().selectById(id);
        List<FieldGroup> fieldGroupList = fieldGroupService.getBaseMapper().selectList(new LambdaQueryWrapper<FieldGroup>().eq(FieldGroup::getFieldId, id));
        if (fieldNotes != null) {
            fieldNotes.setGroup(fieldGroupList);
        }
        Icon icon = iconService.getById(fieldNotes.getIconId());
        fieldNotes.setIconUrl(icon.getUrl());
        if (fieldNotes.getType() == 3) {
            for (FieldGroup fieldGroup : fieldGroupList) {
                //篮球场记统计
                Integer onePointer = 0;
                Integer twoPointer = 0;
                Integer threePointer = 0;
                Integer treeBasket = 0;
                Integer assist = 0;
                Integer backboard = 0;
                Integer snatch = 0;
                Integer blockShot = 0;
                Integer walk = 0;
                Integer pullPeople = 0;
                Integer walkBall = 0;
                Integer threeViolation = 0;
                Integer illegalDribble = 0;
                Integer intentionalBall = 0;
                Integer hookFoul = 0;
                Integer dribbler = 0;
                Integer outBall = 0;
                Integer illegalHands = 0;
                Integer headFoul = 0;
                Integer flyingElbow = 0;
                Integer illegalAttack = 0;
                Integer illegalDefense = 0;
                Integer pushPepole = 0;
                Integer penaltyExit = 0;
                Integer technicalfoul = 0;
                Integer violationfoul = 0;
                Integer comeback = 0;
                Integer amazing = 0;
                Integer nicepass = 0;
                Integer frontback = 0;
                Integer afterback = 0;
                Integer nullone = 0;
                Integer nulltwo = 0;
                Integer nullthree = 0;
                Integer dunk = 0;
                Integer helpball = 0;

                List<FieldData> fieldDataList = fieldDataMapper.selectByFieldIdAndDataId(id, fieldGroup.getId());
                for (FieldData fieldData : fieldDataList) {
                    if (fieldNotes.getMatchType().equals("DRILL")) {
                        StudentInfo studentInfo = studentInfoService.getById(fieldData.getStuId());
                        fieldData.setStuName(studentInfo.getName());
                        fieldData.setImage(studentInfo.getHeadImg());
                        fieldData.setShirt(studentInfo.getShirt());
                        fieldData.setFootballShirt(studentInfo.getFootballShirt());
                        Hardware hardware = hardwareService.getByStuId(studentInfo.getId(), null);
                        fieldData.setHardNum(hardware != null ? hardware.getNum() : null);
                    } else {
                        UserApp studentInfo = userAppService.getById(fieldData.getStuId());
                        fieldData.setStuName(studentInfo.getName());
                        fieldData.setImage(studentInfo.getHeadImg());
                        fieldData.setShirt(studentInfo.getShirt());
                        fieldData.setFootballShirt(studentInfo.getFootballShirt());
                        HardwareApp hardware = hardwareAppService.getByStuId(studentInfo.getId(), null);
                        fieldData.setHardNum(hardware != null ? hardware.getNum() : null);
                    }
                    JSONObject jsonObject = JSONObject.parseObject(fieldData.getUpMap());
                    fieldData.setDataMap(jsonObject);
                    if (fieldData.getDataMap() == null) {
                        fieldData.setDataMap(new JSONObject());
                    }
                    String frontback2 = fieldData.getDataMap().getString("frontback");
                    if (frontback2 == null) {
                        frontback2 = "0";
                    }
                    String afterback2 = fieldData.getDataMap().getString("afterback");
                    if (afterback2 == null) {
                        afterback2 = "0";
                    }
                    fieldData.getDataMap().put("backboard", Integer.valueOf(frontback2) + Integer.valueOf(afterback2));
                    fieldData.setType(3);
                    fieldData.setUpMap(null);
                    JSONObject jsonObject1 = JSONObject.parseObject(fieldData.getOnMap());
                    fieldData.setHideMap(jsonObject1);
                    fieldData.setOnMap(null);
                    //计算全员统计
                    if (fieldData.getDataMap() != null) {
                        String onePointer1 = fieldData.getDataMap().getString("onePointer");
                        if (onePointer1 != null) {
                            onePointer = onePointer + Integer.valueOf(onePointer1);
                        }
                        String twoPointer1 = fieldData.getDataMap().getString("twoPointer");
                        if (twoPointer1 != null) {
                            twoPointer = twoPointer + Integer.valueOf(twoPointer1);
                        }
                        String threePointer1 = fieldData.getDataMap().getString("threePointer");
                        if (threePointer1 != null) {
                            threePointer = threePointer + Integer.valueOf(threePointer1);
                        }
                        String treeBasket1 = fieldData.getDataMap().getString("treeBasket");
                        if (treeBasket1 != null) {
                            treeBasket = treeBasket + Integer.valueOf(treeBasket1);
                        }
                        String assist1 = fieldData.getDataMap().getString("assist");
                        if (assist1 != null) {
                            assist = assist + Integer.valueOf(assist1);
                        }
                        String snatch1 = fieldData.getDataMap().getString("snatch");
                        if (snatch1 != null) {
                            snatch = snatch + Integer.valueOf(snatch1);
                        }
                        String blockShot1 = fieldData.getDataMap().getString("blockShot");
                        if (blockShot1 != null) {
                            blockShot = blockShot + Integer.valueOf(blockShot1);
                        }
                        String walk1 = fieldData.getDataMap().getString("walk");
                        if (walk1 != null) {
                            walk = walk + Integer.valueOf(walk1);
                        }
                        String pullPeople1 = fieldData.getDataMap().getString("pullPeople");
                        if (pullPeople1 != null) {
                            pullPeople = pullPeople + Integer.valueOf(pullPeople1);
                        }
                        String walkBall1 = fieldData.getDataMap().getString("walkBall");
                        if (walkBall1 != null) {
                            walkBall = walkBall + Integer.valueOf(walkBall1);
                        }
                        String threeViolation1 = fieldData.getDataMap().getString("threeViolation");
                        if (threeViolation1 != null) {
                            threeViolation = threeViolation + Integer.valueOf(threeViolation1);
                        }
                        String illegalDribble1 = fieldData.getDataMap().getString("illegalDribble");
                        if (illegalDribble1 != null) {
                            illegalDribble = illegalDribble + Integer.valueOf(illegalDribble1);
                        }
                        String intentionalBall1 = fieldData.getDataMap().getString("intentionalBall");
                        if (intentionalBall1 != null) {
                            intentionalBall = intentionalBall + Integer.valueOf(intentionalBall1);
                        }
                        String hookFoul1 = fieldData.getDataMap().getString("hookFoul");
                        if (hookFoul1 != null) {
                            hookFoul = hookFoul + Integer.valueOf(hookFoul1);
                        }
                        String dribbler1 = fieldData.getDataMap().getString("dribbler");
                        if (dribbler1 != null) {
                            dribbler = dribbler + Integer.valueOf(dribbler1);
                        }
                        String outBall1 = fieldData.getDataMap().getString("outBall");
                        if (outBall1 != null) {
                            outBall = outBall + Integer.valueOf(outBall1);
                        }
                        String illegalHands1 = fieldData.getDataMap().getString("illegalHands");
                        if (illegalHands1 != null) {
                            illegalHands = illegalHands + Integer.valueOf(illegalHands1);
                        }
                        String headFoul1 = fieldData.getDataMap().getString("headFoul");
                        if (headFoul1 != null) {
                            headFoul = headFoul + Integer.valueOf(headFoul1);
                        }
                        String flyingElbow1 = fieldData.getDataMap().getString("flyingElbow");
                        if (flyingElbow1 != null) {
                            flyingElbow = flyingElbow + Integer.valueOf(flyingElbow1);
                        }
                        String illegalAttack1 = fieldData.getDataMap().getString("illegalAttack");
                        if (illegalAttack1 != null) {
                            illegalAttack = illegalAttack + Integer.valueOf(illegalAttack1);
                        }
                        String illegalDefense1 = fieldData.getDataMap().getString("illegalDefense");
                        if (illegalDefense1 != null) {
                            illegalDefense = illegalDefense + Integer.valueOf(illegalDefense1);
                        }
                        String pushPepole1 = fieldData.getDataMap().getString("pushPepole");
                        if (pushPepole1 != null) {
                            pushPepole = pushPepole + Integer.valueOf(pushPepole1);
                        }
                        String penaltyExit1 = fieldData.getDataMap().getString("penaltyExit");
                        if (penaltyExit1 != null) {
                            penaltyExit = penaltyExit + Integer.valueOf(penaltyExit1);
                        }
                        String technicalfoul1 = fieldData.getDataMap().getString("technicalfoul");
                        if (technicalfoul1 != null) {
                            technicalfoul = technicalfoul + Integer.valueOf(technicalfoul1);
                        }
                        String violationfoul1 = fieldData.getDataMap().getString("violationfoul");
                        if (violationfoul1 != null) {
                            violationfoul = violationfoul + Integer.valueOf(violationfoul1);
                        }
                        String comeback1 = fieldData.getDataMap().getString("comeback");
                        if (comeback1 != null) {
                            comeback = comeback + Integer.valueOf(comeback1);
                        }
                        String amazing1 = fieldData.getDataMap().getString("amazing");
                        if (amazing1 != null) {
                            amazing = amazing + Integer.valueOf(amazing1);
                        }
                        String nicepass1 = fieldData.getDataMap().getString("nicepass");
                        if (nicepass1 != null) {
                            nicepass = nicepass + Integer.valueOf(nicepass1);
                        }
                        String frontback1 = fieldData.getDataMap().getString("frontback");
                        if (frontback1 != null) {
                            frontback = frontback + Integer.valueOf(frontback1);
                        }
                        String afterback1 = fieldData.getDataMap().getString("afterback");
                        if (afterback1 != null) {
                            afterback = afterback + Integer.valueOf(afterback1);
                        }
                        String nullone1 = fieldData.getDataMap().getString("nullone");
                        if (nullone1 != null) {
                            nullone = nullone + Integer.valueOf(nullone1);
                        }
                        String nulltwo1 = fieldData.getDataMap().getString("nulltwo");
                        if (nulltwo1 != null) {
                            nulltwo = nulltwo + Integer.valueOf(nulltwo1);
                        }
                        String nullthree1 = fieldData.getDataMap().getString("nullthree");
                        if (nullthree1 != null) {
                            nullthree = nullthree + Integer.valueOf(nullthree1);
                        }
                        String dunk1 = fieldData.getDataMap().getString("dunk");
                        if (dunk1 != null) {
                            dunk = dunk + Integer.valueOf(dunk1);
                        }
                        String helpball1 = fieldData.getDataMap().getString("helpball");
                        if (helpball1 != null) {
                            helpball = helpball + Integer.valueOf(helpball1);
                        }
                        backboard = backboard + frontback + afterback;

                    }
                }
                JSONObject dataMap = new JSONObject();
                dataMap.put("onePointer", onePointer);
                dataMap.put("twoPointer", twoPointer);
                dataMap.put("threePointer", threePointer);
                dataMap.put("treeBasket", treeBasket);
                dataMap.put("assist", assist);
                dataMap.put("backboard", backboard);
                dataMap.put("snatch", snatch);
                dataMap.put("blockShot", blockShot);
                dataMap.put("walk", walk);
                dataMap.put("pullPeople", pullPeople);
                dataMap.put("walkBall", walkBall);
                dataMap.put("threeViolation", threeViolation);
                dataMap.put("illegalDribble", illegalDribble);
                dataMap.put("intentionalBall", intentionalBall);
                dataMap.put("hookFoul", hookFoul);
                dataMap.put("dribbler", dribbler);
                dataMap.put("outBall", outBall);
                dataMap.put("illegalHands", illegalHands);
                dataMap.put("headFoul", headFoul);
                dataMap.put("flyingElbow", flyingElbow);
                dataMap.put("illegalAttack", illegalAttack);
                dataMap.put("illegalDefense", illegalDefense);
                dataMap.put("pushPepole", pushPepole);
                dataMap.put("penaltyExit", penaltyExit);
                dataMap.put("technicalfoul", technicalfoul);
                dataMap.put("violationfoul", violationfoul);
                dataMap.put("comeback", comeback);
                dataMap.put("amazing", amazing);
                dataMap.put("nicepass", nicepass);
                dataMap.put("frontback", frontback);
                dataMap.put("afterback", afterback);
                dataMap.put("nullone", nullone);
                dataMap.put("nulltwo", nulltwo);
                dataMap.put("nullthree", nullthree);
                dataMap.put("dunk", dunk);
                dataMap.put("helpball", helpball);
                fieldGroup.setGroupMap(dataMap);
                fieldGroup.setFieldData(fieldDataList);
            }
        } else if (fieldNotes.getType() == 1) {
            for (FieldGroup fieldGroup : fieldGroupList) {
                //足球场记统计
                Integer goal = 0;
                Integer orthogonality = 0;
                Integer assisting = 0;
                Integer surpass = 0;
                Integer pointsphere = 0;
                Integer cornerkick = 0;
                Integer freekick = 0;
                Integer threatball = 0;
                Integer heading = 0;
                Integer raise = 0;
                Integer preemption = 0;
                Integer redcard = 0;
                Integer shooting = 0;
                Integer rules = 0;
                Integer offside = 0;
                Integer puking = 0;
                Integer yellowcard = 0;
                Integer owngoal = 0;
                Integer waveshooting = 0;
                Integer kickair = 0;
                Integer stoperror = 0;
                Integer defensiveerror = 0;
                Integer passingerror = 0;
                Integer wonderful = 0;
                Integer excitingShot = 0;
                Integer excitingSyeals = 0;
                Integer longpass = 0;
                Integer headgoal = 0;
                Integer headclear = 0;
                Integer clearance = 0;
                Integer rescue = 0;
                Integer pointMaking = 0;
                Integer fatalerror = 0;
                Integer wonderfulstop = 0;
                Integer shoot = 0;
                Integer threatenshoot = 0;
                Integer scoring = 0;
                Integer seriouserror = 0;
                List<FieldData> fieldDataList = fieldDataMapper.selectByFieldIdAndDataId(id, fieldGroup.getId());
                for (FieldData fieldData : fieldDataList) {
                    if (fieldNotes.getMatchType().equals("DRILL")) {
                        StudentInfo studentInfo = studentInfoService.getById(fieldData.getStuId());
                        fieldData.setStuName(studentInfo.getName());
                        fieldData.setImage(studentInfo.getHeadImg());
                        fieldData.setShirt(studentInfo.getShirt());
                        fieldData.setFootballShirt(studentInfo.getFootballShirt());
                        Hardware hardware = hardwareService.getByStuId(studentInfo.getId(), null);
                        fieldData.setHardNum(hardware != null ? hardware.getNum() : null);
                    } else {
                        UserApp studentInfo = userAppService.getById(fieldData.getStuId());
                        fieldData.setStuName(studentInfo.getName());
                        fieldData.setImage(studentInfo.getHeadImg());
                        fieldData.setShirt(studentInfo.getShirt());
                        HardwareApp hardware = hardwareAppService.getByStuId(studentInfo.getId(), null);
                        fieldData.setFootballShirt(studentInfo.getFootballShirt());
                        fieldData.setHardNum(hardware != null ? hardware.getNum() : null);
                    }
                    JSONObject jsonObject = JSONObject.parseObject(fieldData.getUpMap());
                    fieldData.setDataMap(jsonObject);
                    fieldData.setType(1);
                    fieldData.setUpMap(null);
                    JSONObject jsonObject1 = JSONObject.parseObject(fieldData.getOnMap());
                    fieldData.setHideMap(jsonObject1);
                    fieldData.setOnMap(null);
                    //计算全员统计
                    if (fieldData.getDataMap() != null) {
                        String goal1 = fieldData.getDataMap().getString("goal");
                        if (goal1 != null) {
                            goal = goal + Integer.valueOf(goal1);
                        }
                        String orthogonality1 = fieldData.getDataMap().getString("orthogonality");
                        if (orthogonality1 != null) {
                            orthogonality = orthogonality + Integer.valueOf(orthogonality1);
                        }
                        String assisting1 = fieldData.getDataMap().getString("assisting");
                        if (assisting1 != null) {
                            assisting = assisting + Integer.valueOf(assisting1);
                        }
                        String surpass1 = fieldData.getDataMap().getString("surpass");
                        if (surpass1 != null) {
                            surpass = surpass + Integer.valueOf(surpass1);
                        }
                        String pointsphere1 = fieldData.getDataMap().getString("pointsphere");
                        if (pointsphere1 != null) {
                            pointsphere = pointsphere + Integer.valueOf(pointsphere1);
                        }
                        String cornerkick1 = fieldData.getDataMap().getString("cornerkick");
                        if (cornerkick1 != null) {
                            cornerkick = cornerkick + Integer.valueOf(cornerkick1);
                        }
                        String freekick1 = fieldData.getDataMap().getString("freekick");
                        if (freekick1 != null) {
                            freekick = freekick + Integer.valueOf(freekick1);
                        }
                        String threatball1 = fieldData.getDataMap().getString("threatball");
                        if (threatball1 != null) {
                            threatball = threatball + Integer.valueOf(threatball1);
                        }
                        String heading1 = fieldData.getDataMap().getString("heading");
                        if (heading1 != null) {
                            heading = heading + Integer.valueOf(heading1);
                        }
                        String raise1 = fieldData.getDataMap().getString("raise");
                        if (raise1 != null) {
                            raise = raise + Integer.valueOf(raise1);
                        }
                        String preemption1 = fieldData.getDataMap().getString("preemption");
                        if (preemption1 != null) {
                            preemption = preemption + Integer.valueOf(preemption1);
                        }
                        String redcard1 = fieldData.getDataMap().getString("redcard");
                        if (redcard1 != null) {
                            redcard = redcard + Integer.valueOf(redcard1);
                        }
                        String shooting1 = fieldData.getDataMap().getString("shooting");
                        if (shooting1 != null) {
                            shooting = shooting + Integer.valueOf(shooting1);
                        }
                        String rules1 = fieldData.getDataMap().getString("rules");
                        if (rules1 != null) {
                            rules = rules + Integer.valueOf(rules1);
                        }
                        String offside1 = fieldData.getDataMap().getString("offside");
                        if (offside1 != null) {
                            offside = offside + Integer.valueOf(offside1);
                        }
                        String puking1 = fieldData.getDataMap().getString("puking");
                        if (puking1 != null) {
                            puking = puking + Integer.valueOf(puking1);
                        }
                        String yellowcard1 = fieldData.getDataMap().getString("yellowcard");
                        if (yellowcard1 != null) {
                            yellowcard = yellowcard + Integer.valueOf(yellowcard1);
                        }
                        String owngoal1 = fieldData.getDataMap().getString("owngoal");
                        if (owngoal1 != null) {
                            owngoal = owngoal + Integer.valueOf(owngoal1);
                        }
                        String waveshooting1 = fieldData.getDataMap().getString("waveshooting");
                        if (waveshooting1 != null) {
                            waveshooting = waveshooting + Integer.valueOf(waveshooting1);
                        }
                        String kickair1 = fieldData.getDataMap().getString("kickair");
                        if (kickair1 != null) {
                            kickair = kickair + Integer.valueOf(kickair1);
                        }
                        String stoperror1 = fieldData.getDataMap().getString("stoperror");
                        if (stoperror1 != null) {
                            stoperror = stoperror + Integer.valueOf(stoperror1);
                        }
                        String defensiveerror1 = fieldData.getDataMap().getString("defensiveerror");
                        if (defensiveerror1 != null) {
                            defensiveerror = defensiveerror + Integer.valueOf(defensiveerror1);
                        }
                        String passingerror1 = fieldData.getDataMap().getString("passingerror");
                        if (passingerror1 != null) {
                            passingerror = passingerror + Integer.valueOf(passingerror1);
                        }
                        String wonderful1 = fieldData.getDataMap().getString("wonderful");
                        if (wonderful1 != null) {
                            wonderful = wonderful + Integer.valueOf(wonderful1);
                        }
                        String excitingShot1 = fieldData.getDataMap().getString("excitingShot");
                        if (excitingShot1 != null) {
                            excitingShot = excitingShot + Integer.valueOf(excitingShot1);
                        }
                        String excitingSyeals1 = fieldData.getDataMap().getString("excitingSyeals");
                        if (excitingSyeals1 != null) {
                            excitingSyeals = excitingSyeals + Integer.valueOf(excitingSyeals1);
                        }
                        String longpass1 = fieldData.getDataMap().getString("longpass");
                        if (longpass1 != null) {
                            longpass = longpass + Integer.valueOf(longpass1);
                        }
                        String headgoal1 = fieldData.getDataMap().getString("headgoal");
                        if (headgoal1 != null) {
                            headgoal = headgoal + Integer.valueOf(headgoal1);
                        }
                        String headclear1 = fieldData.getDataMap().getString("headclear");
                        if (headclear1 != null) {
                            headclear = headclear + Integer.valueOf(headclear1);
                        }
                        String clearance1 = fieldData.getDataMap().getString("clearance");
                        if (clearance1 != null) {
                            clearance = clearance + Integer.valueOf(clearance1);
                        }
                        String rescue1 = fieldData.getDataMap().getString("rescue");
                        if (rescue1 != null) {
                            rescue = rescue + Integer.valueOf(rescue1);
                        }
                        String pointMaking1 = fieldData.getDataMap().getString("pointMaking");
                        if (pointMaking1 != null) {
                            pointMaking = pointMaking + Integer.valueOf(pointMaking1);
                        }
                        String fatalerror1 = fieldData.getDataMap().getString("fatalerror");
                        if (fatalerror1 != null) {
                            fatalerror = fatalerror + Integer.valueOf(fatalerror1);
                        }
                        String wonderfulstop1 = fieldData.getDataMap().getString("wonderfulstop");
                        if (wonderfulstop1 != null) {
                            wonderfulstop = wonderfulstop + Integer.valueOf(wonderfulstop1);
                        }
                        String shoot1 = fieldData.getDataMap().getString("shoot");
                        if (shoot1 != null) {
                            shoot = shoot + Integer.valueOf(shoot1);
                        }
                        String threatenshoot1 = fieldData.getDataMap().getString("threatenshoot");
                        if (threatenshoot1 != null) {
                            threatenshoot = threatenshoot + Integer.valueOf(threatenshoot1);
                        }
                        String scoring1 = fieldData.getDataMap().getString("scoring");
                        if (scoring1 != null) {
                            scoring = scoring + Integer.valueOf(scoring1);
                        }
                        String seriouserror1 = fieldData.getDataMap().getString("seriouserror");
                        if (seriouserror1 != null) {
                            seriouserror = seriouserror + Integer.valueOf(seriouserror1);
                        }
                    }
                }
                JSONObject dataMap = new JSONObject();
                dataMap.put("goal", goal);
                dataMap.put("orthogonality", orthogonality);
                dataMap.put("assisting", assisting);
                dataMap.put("surpass", surpass);
                dataMap.put("pointsphere", pointsphere);
                dataMap.put("cornerkick", cornerkick);
                dataMap.put("freekick", freekick);
                dataMap.put("threatball", threatball);
                dataMap.put("heading", heading);
                dataMap.put("raise", raise);
                dataMap.put("preemption", preemption);
                dataMap.put("redcard", redcard);
                dataMap.put("shooting", shooting);
                dataMap.put("rules", rules);
                dataMap.put("offside", offside);
                dataMap.put("puking", puking);
                dataMap.put("yellowcard", yellowcard);
                dataMap.put("owngoal", owngoal);
                dataMap.put("waveshooting", waveshooting);
                dataMap.put("kickair", kickair);
                dataMap.put("stoperror", stoperror);
                dataMap.put("defensiveerror", defensiveerror);
                dataMap.put("passingerror", passingerror);
                dataMap.put("wonderful", wonderful);
                dataMap.put("excitingShot", excitingShot);
                dataMap.put("excitingSyeals", excitingSyeals);
                dataMap.put("longpass", longpass);
                dataMap.put("headgoal", headgoal);
                dataMap.put("headclear", headclear);
                dataMap.put("clearance", clearance);
                dataMap.put("rescue", rescue);
                dataMap.put("pointMaking", pointMaking);
                dataMap.put("fatalerror", fatalerror);
                dataMap.put("wonderfulstop", wonderfulstop);
                dataMap.put("shoot", shoot);
                dataMap.put("threatenshoot", threatenshoot);
                dataMap.put("scoring", scoring);
                dataMap.put("seriouserror", seriouserror);
                fieldGroup.setGroupMap(dataMap);
                fieldGroup.setFieldData(fieldDataList);
            }
        }
        Date startTime;
        Date stopTime;
        if (fieldNotes.getMatchType().equals("DRILL")) {
            Drill drill = drillService.getById(fieldNotes.getMatchId());
            if (drill.getRealDataTime() > 0l) {
                startTime = new Date(drill.getRealDataTime());
            } else {
                startTime = drill.getStartTime();
            }
            stopTime = drill.getStopTime();
        } else {
            Game game = gameService.getById(fieldNotes.getMatchId());
            startTime = game.getFinishStartTime();
            stopTime = game.getFinishStopTime();
        }
        Long now = System.currentTimeMillis();
        if (startTime != null && stopTime == null) {
            Long runningTime = now - startTime.getTime();
            fieldNotes.setRunningTime(runningTime);
        } else {
            fieldNotes.setRunningTime(0l);
        }
        return fieldNotes;
    }

    @Override
    public FieldNotes fieldFast(FieldNotes fieldNotes, String groupName1, String groupName2) {
        //首先创建场记表
        if (fieldNotes.getType() == 1) {
            fieldNotes.setIconId(9);
        } else if (fieldNotes.getType() == 3) {
            fieldNotes.setIconId(4);
        }
        fieldNotes.setFinish(0);
        fieldNotes.setCreateTime(new Date());
        fieldNotes.setUpdateTime(new Date());
        saveOrUpdate(fieldNotes);
        fieldNotes.setName(fieldNotes.getName() + fieldNotes.getId());
        saveOrUpdate(fieldNotes);
        //得到场记id后创建小组表
        FieldGroup fieldGroup = new FieldGroup();
        fieldGroup.setFieldId(fieldNotes.getId());
        if (groupName1 == null) {
            fieldGroup.setGroupName("  组1");
        } else {
            fieldGroup.setGroupName(groupName1);
        }
        fieldGroup.setGroupFraction(0);
        fieldGroup.setGroupControl(0L);
        fieldGroup.setCreateTime(new Date());
        fieldGroup.setUpdateTime(new Date());
        fieldGroupService.saveOrUpdate(fieldGroup);
        FieldGroup fieldGroup1 = new FieldGroup();
        fieldGroup1.setFieldId(fieldNotes.getId());
        if (groupName2 == null) {
            fieldGroup1.setGroupName("  无名组");
        } else {
            fieldGroup1.setGroupName(groupName2);
        }
        fieldGroup1.setGroupFraction(0);
        fieldGroup1.setCreateTime(new Date());
        fieldGroup1.setUpdateTime(new Date());
        fieldGroupService.saveOrUpdate(fieldGroup1);
        //得到场记id跟小组id创建数据表
        if (fieldNotes.getStudentId() != null) {
            FieldData fieldData = new FieldData();
            fieldData.setGroupId(fieldGroup.getId());
            fieldData.setFieldId(fieldNotes.getId());
            fieldData.setStuId(fieldNotes.getStudentId());
            if (fieldNotes.getType() == 1) {
                String map = "{\"goal\":0,\"orthogonality\":0,\"assisting\":0,\"surpass\":0,\"pointsphere\":0,\"cornerkick\":0,\"freekick\":0,\"threatball\":0,\"heading\":0,\"raise\":0,\"preemption\":0,\"redcard\":0,\"shooting\":0,\"rules\":0,\"offside\":0,\"puking\":0,\"yellowcard\":0,\"owngoal\":0,\"waveshooting\":0,\"kickair\":0,\"stoperror\":0,\"defensiveerror\":0,\"passingerror\":0,\"wonderful\":0,\"excitingShot\":0,\"excitingSyeals\":0,\"longpass\":0,\"headgoal\":0,\"headclear\":0,\"clearance\":0,\"rescue\":0,\"pointMaking\":0,\"fatalerror\":0,\"wonderfulstop\":0,\"shoot\":0,\"threatenshoot\":0,\"scoring\":0,\"seriouserror\":0  }";
                fieldData.setDataMap(JSONObject.parseObject(map));
            } else if (fieldNotes.getType() == 3) {
                String map = "{\"walk\": 0, \"assist\": 0, \"snatch\": 0, \"outBall\": 0, \"dribbler\": 0, \"headFoul\": 0, \"hookFoul\": 0, \"walkBall\": 0, \"backboard\": 0, \"blockShot\": 0, \"onePointer\": 0, \"pullPeople\": 0, \"pushPepole\": 0, \"treeBasket\": 0, \"twoPointer\": 0, \"flyingElbow\": 0, \"penaltyExit\": 0, \"illegalHands\": 0, \"threePointer\": 0, \"illegalAttack\": 0, \"illegalDefense\": 0, \"illegalDribble\": 0, \"threeViolation\": 0, \"intentionalBall\": 0, \"technicalfoul\": 0, \"violationfoul\": 0, \"comeback\": 0, \"amazing\": 0, \"nicepass\": 0, \"frontback\":0, \"afterback\":0, \"nullone\":0, \"nulltwo\":0, \"nullthree\":0, \"dunk\":0, \"helpball\":0}";
                fieldData.setDataMap(JSONObject.parseObject(map));
            }

            fieldData.setCreateTime(new Date());
            fieldData.setUpdateTime(new Date());
            fieldDataService.saveOrUpdate(fieldData);
        } else {
            if (fieldNotes.getMatchType().equals("DRILL")) {
                Drill drill = drillService.getById(fieldNotes.getMatchId());
                List<StudentInfo> studentInfos = studentInfoService.getStusByTeamId(drill.getTeamId());
                for (StudentInfo studentInfo : studentInfos) {
                    FieldData fieldData = new FieldData();
                    fieldData.setGroupId(fieldGroup.getId());
                    fieldData.setFieldId(fieldNotes.getId());
                    fieldData.setStuId(studentInfo.getId());
                    if (fieldNotes.getType() == 1) {
                        String map1 = "{\"goal\":0,\"orthogonality\":0,\"assisting\":0,\"surpass\":0,\"pointsphere\":0,\"cornerkick\":0,\"freekick\":0,\"threatball\":0,\"heading\":0,\"raise\":0,\"preemption\":0,\"redcard\":0,\"shooting\":0,\"rules\":0,\"offside\":0,\"puking\":0,\"yellowcard\":0,\"owngoal\":0,\"waveshooting\":0,\"kickair\":0,\"stoperror\":0,\"defensiveerror\":0,\"passingerror\":0,\"wonderful\":0,\"excitingShot\":0,\"excitingSyeals\":0,\"longpass\":0,\"headgoal\":0,\"headclear\":0,\"clearance\":0,\"rescue\":0,\"pointMaking\":0,\"fatalerror\":0,\"wonderfulstop\":0,\"shoot\":0,\"threatenshoot\":0,\"scoring\":0,\"seriouserror\":0  }";
                        fieldData.setDataMap(JSONObject.parseObject(map1));
                    } else if (fieldNotes.getType() == 3) {
                        String map3 = "{\"walk\": 0, \"assist\": 0, \"snatch\": 0, \"outBall\": 0, \"dribbler\": 0, \"headFoul\": 0, \"hookFoul\": 0, \"walkBall\": 0, \"backboard\": 0, \"blockShot\": 0, \"onePointer\": 0, \"pullPeople\": 0, \"pushPepole\": 0, \"treeBasket\": 0, \"twoPointer\": 0, \"flyingElbow\": 0, \"penaltyExit\": 0, \"illegalHands\": 0, \"threePointer\": 0, \"illegalAttack\": 0, \"illegalDefense\": 0, \"illegalDribble\": 0, \"threeViolation\": 0, \"intentionalBall\": 0, \"technicalfoul\": 0, \"violationfoul\": 0, \"comeback\": 0, \"amazing\": 0, \"nicepass\": 0, \"frontback\":0, \"afterback\":0, \"nullone\":0, \"nulltwo\":0, \"nullthree\":0, \"dunk\":0, \"helpball\":0}";
                        fieldData.setDataMap(JSONObject.parseObject(map3));
                    }
                    fieldData.setCreateTime(new Date());
                    fieldData.setUpdateTime(new Date());
                    fieldDataService.saveOrUpdate(fieldData);
                }
            } else {
                Game game = gameService.getById(fieldNotes.getMatchId());
                //这里查出学生只用一个id 所以随便设置一个字段用于判断客队主队学员来分组
                List<UserApp> userApps1 = userAppService.getByTeamId(game.getTeamId()).stream().peek(e -> e.setName("host")).collect(Collectors.toList());
                List<UserApp> userApps2 = userAppService.getByTeamId(game.getOpponentId()).stream().peek(e -> e.setName("guest")).collect(Collectors.toList());
                userApps1.addAll(userApps2);
                for (UserApp userApp : userApps1) {
                    FieldData fieldData = new FieldData();
                    if (userApp.getName().equals("host")) {
                        fieldData.setGroupId(fieldGroup.getId());
                    } else {
                        fieldData.setGroupId(fieldGroup1.getId());
                    }
                    fieldData.setFieldId(fieldNotes.getId());
                    fieldData.setStuId(userApp.getId());
                    if (fieldNotes.getType() == 1) {
                        String map1 = "{\"goal\":0,\"orthogonality\":0,\"assisting\":0,\"surpass\":0,\"pointsphere\":0,\"cornerkick\":0,\"freekick\":0,\"threatball\":0,\"heading\":0,\"raise\":0,\"preemption\":0,\"redcard\":0,\"shooting\":0,\"rules\":0,\"offside\":0,\"puking\":0,\"yellowcard\":0,\"owngoal\":0,\"waveshooting\":0,\"kickair\":0,\"stoperror\":0,\"defensiveerror\":0,\"passingerror\":0,\"wonderful\":0,\"excitingShot\":0,\"excitingSyeals\":0,\"longpass\":0,\"headgoal\":0,\"headclear\":0,\"clearance\":0,\"rescue\":0,\"pointMaking\":0,\"fatalerror\":0,\"wonderfulstop\":0,\"shoot\":0,\"threatenshoot\":0,\"scoring\":0,\"seriouserror\":0  }";
                        fieldData.setDataMap(JSONObject.parseObject(map1));
                    } else if (fieldNotes.getType() == 3) {
                        String map3 = "{\"walk\": 0, \"assist\": 0, \"snatch\": 0, \"outBall\": 0, \"dribbler\": 0, \"headFoul\": 0, \"hookFoul\": 0, \"walkBall\": 0, \"backboard\": 0, \"blockShot\": 0, \"onePointer\": 0, \"pullPeople\": 0, \"pushPepole\": 0, \"treeBasket\": 0, \"twoPointer\": 0, \"flyingElbow\": 0, \"penaltyExit\": 0, \"illegalHands\": 0, \"threePointer\": 0, \"illegalAttack\": 0, \"illegalDefense\": 0, \"illegalDribble\": 0, \"threeViolation\": 0, \"intentionalBall\": 0, \"technicalfoul\": 0, \"violationfoul\": 0, \"comeback\": 0, \"amazing\": 0, \"nicepass\": 0, \"frontback\":0, \"afterback\":0, \"nullone\":0, \"nulltwo\":0, \"nullthree\":0, \"dunk\":0, \"helpball\":0}";
                        fieldData.setDataMap(JSONObject.parseObject(map3));
                    }
                    fieldData.setCreateTime(new Date());
                    fieldData.setUpdateTime(new Date());
                    fieldDataService.saveOrUpdate(fieldData);
                }
            }
        }
        return fieldNotes;
    }

    @Override
    @Async
    public void sendFieldNotesByStudent(org.json.JSONObject message) {
        List<FieldData> fieldDataList = fieldDataService.list(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId, message.getLong("fieldNotesId")));
        if (fieldDataList.size() > 0) {
            for (FieldData fieldData : fieldDataList) {
                StudentInfo studentInfo = studentInfoService.getById(fieldData.getStuId());
                if (!studentInfo.getDeleted()) {
                    FieldNotes fieldNotes = getBaseMapper().selectById(fieldData.getFieldId());
                    message.put("action", "fieldNotes_update");
                    message.put("deviceToken", studentInfo.getDeviceTokens());
                    message.put("receiveUserId", studentInfo.getId());
                    message.put("schoolId", studentInfo.getSchoollId());
                    message.put("drillId", fieldNotes.getMatchId());
                    message.put("fieldNodesId", fieldNotes.getId());
                    boolean b = false;
                    try {
                        b = uPushUtil.sendAndroidUnicastApp(studentInfo.getDeviceTokens(), message);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    transparentMessageService.saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(studentInfo.getId()).setStatus(b));
                }
            }
        }
    }

    @Override
    @Async
    public void sendFieldNotesByUserApp(org.json.JSONObject message) {
        List<FieldData> fieldDataList = fieldDataService.list(new LambdaQueryWrapper<FieldData>().eq(FieldData::getFieldId, message.getLong("fieldNotesId")));
        if (fieldDataList.size() > 0) {
            for (FieldData fieldData : fieldDataList) {
                UserApp studentInfo = userAppService.getById(fieldData.getStuId());
                if (studentInfo != null && !studentInfo.getDeleted()) {
                    FieldNotes fieldNotes = getBaseMapper().selectById(fieldData.getFieldId());
                    message.put("action", "fieldNotes_update_app");
                    message.put("deviceToken", studentInfo.getDeviceTokens());
                    message.put("receiveUserId", studentInfo.getId());
                    message.put("schoolId", 0);
                    message.put("matchId", fieldNotes.getMatchId());
                    message.put("matchType", fieldNotes.getMatchId());
                    message.put("fieldNodesId", fieldNotes.getId());
                    boolean b = false;
                    try {
                        b = uPushUtil.sendAndroidUnicastApp(studentInfo.getDeviceTokens(), message);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    transparentMessageService.saveOrUpdate(new TransparentMessage().setMessage(JSON.parseObject(JSON.toJSONString(message))).setType("APP").setReceiveUserId(studentInfo.getId()).setStatus(b));
                }
            }
        }
    }

    @Override
    public FieldNotesPageParam createFieldNotesPageParam(Long userId, String matchType, Long matchId, Long pageIndex, Long pageSize) {
        FieldNotesPageParam fieldNotesPageParam = new FieldNotesPageParam();
        fieldNotesPageParam.setMatchType(matchType);
        fieldNotesPageParam.setStudentId(userId);
        fieldNotesPageParam.setMatchId(matchId);
        fieldNotesPageParam.setPageIndex(pageIndex);
        fieldNotesPageParam.setPageSize(pageSize);
        return fieldNotesPageParam;
    }

    public Long hitRate(FieldData fieldData) {
        int onePointer = 0;
        int nullone = 0;
        int twoPointer = 0;
        int nulltwo = 0;
        int threePointer = 0;
        int nullthree = 0;
        JSONObject playerData = fieldData.getDataMap();
        if (playerData == null) {
            playerData = new JSONObject();
        }
        // 计算一分命中率
        onePointer = (int) playerData.getOrDefault("onePointer", 0);
        nullone = (int) playerData.getOrDefault("nullone", 0);
        // 计算二分命中率
        twoPointer = (int) playerData.getOrDefault("twoPointer", 0);
        nulltwo = (int) playerData.getOrDefault("nulltwo", 0);
        // 计算三分命中率
        threePointer = (int) playerData.getOrDefault("threePointer", 0);
        nullthree = (int) playerData.getOrDefault("nullthree", 0);
        int totalShots = onePointer + nullone + twoPointer + nulltwo + threePointer + nullthree;
        int totalHits = onePointer + twoPointer + threePointer;
        // 计算总命中率
        double totalHitRate = (double) totalHits / totalShots * 100;
        return Math.round(totalHitRate);
    }

}
