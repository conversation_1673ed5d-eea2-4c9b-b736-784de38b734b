package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.Hardware;
import io.geekidea.springbootplus.using.entity.HardwareApp;
import io.geekidea.springbootplus.framework.common.service.BaseService;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
public interface HardwareAppService extends BaseService<HardwareApp> {

    /**
     * 保存
     *
     * @param hardwareApp
     * @return
     * @throws Exception
     */
    boolean saveHardwareApp(HardwareApp hardwareApp) throws Exception;

    /**
     * 修改
     *
     * @param hardwareApp
     * @return
     * @throws Exception
     */
    boolean updateHardwareApp(List<HardwareApp> hardwareApps) throws Exception;


    /**
     * @desc: 启动设备
     * @author: DH
     * @date: 2022/4/29 10:09
     */
    boolean startHardwares(List<HardwareApp> hardwareList, Long drillId,Long teamId);

    boolean startHardwaresGame(List<HardwareApp> hardwareList, Long gameId,Long teamId);

    /**
     * @desc: 获取学生设备
     * @author: DH
     * @date: 2022/4/29 10:09
     */
    HardwareApp getByStuId(Long stuId, String mac);

    void test();

}
