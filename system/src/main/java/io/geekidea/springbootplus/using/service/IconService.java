package io.geekidea.springbootplus.using.service;

import io.geekidea.springbootplus.using.entity.Icon;
import io.geekidea.springbootplus.using.param.IconPageParam;
import io.geekidea.springbootplus.framework.common.service.BaseService;
import io.geekidea.springbootplus.framework.core.pagination.Paging;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
public interface IconService extends BaseService<Icon> {

    /**
     * 保存
     *
     * @param icon
     * @return
     * @throws Exception
     */
    boolean saveIcon(Icon icon) throws Exception;

    /**
     * 修改
     *
     * @param icon
     * @return
     * @throws Exception
     */
    boolean updateIcon(Icon icon) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean deleteIcon(Long id) throws Exception;

}
