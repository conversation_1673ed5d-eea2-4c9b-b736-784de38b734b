<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2019-2029 geekidea(https://github.com/geekidea)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.geekidea.springbootplus.system.mapper.SysPermissionMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, parent_id, url, code, icon, type, level, state, sort, remark, version, create_time, update_time
    </sql>

    <select id="getSysPermissionById" resultType="io.geekidea.springbootplus.system.vo.SysPermissionQueryVo">
        select
        <include refid="Base_Column_List"/>
        from sys_permission where id = #{id}
    </select>

    <select id="getSysPermissionPageList" resultType="io.geekidea.springbootplus.system.vo.SysPermissionQueryVo">
        select
        <include refid="Base_Column_List"/>
        from sys_permission
    </select>

    <select id="getPermissionCodesByUserId" resultType="java.lang.String">
        select p.code
        from sys_user u
                 inner join sys_role r
                            on u.role_id = r.id
                 inner join sys_role_permission rp
                            on r.id = rp.role_id
                 inner join sys_permission p
                            on rp.permission_id = p.id
        where u.state = 1
          and r.state = 1
          and rp.state = 1
          and p.state = 1
          and u.deleted = 0
          and u.id = #{userId};
    </select>

    <select id="getMenuListByUserId" resultType="io.geekidea.springbootplus.system.entity.SysPermission">
        select p.*
        from sys_user u
                 inner join sys_role r
                            on u.role_id = r.id
                 inner join sys_role_permission rp
                            on r.id = rp.role_id
                 inner join sys_permission p
                            on rp.permission_id = p.id
        where u.state = 1
          and u.deleted = 0
          and r.state = 1
          and rp.state = 1
          and p.state = 1
          and u.id = #{userId}
    </select>

</mapper>
