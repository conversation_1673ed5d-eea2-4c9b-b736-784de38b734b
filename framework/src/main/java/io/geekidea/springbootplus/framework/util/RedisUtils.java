package io.geekidea.springbootplus.framework.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisUtils {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    // =============================common============================

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error(key, e);
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error(key, e);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public Object del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                return redisTemplate.delete(key[0]);
            } else {
                return redisTemplate.delete(CollectionUtils.arrayToList(key));
            }
        }
        return false;
    }

    /**
     * 删除缓存
     *
     * @param keys 多个
     */
    @SuppressWarnings("unchecked")
    public Object dels(List<String> keys) {
        if (keys.size() > 0) {
           return redisTemplate.delete(keys);
        }
        return false;
    }

    // ============================String=============================

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error(key, e);
            return false;
        }

    }
    /**
     * 普通缓存放入 只能设值一次 第一次成功 第二次第N次都是失败 只有删除才行
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean setIfAbsent(String key, Object value) {
        try {
            return redisTemplate.opsForValue().setIfAbsent(key, value);
        } catch (Exception e) {
            log.error(key, e);
            return false;
        }
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error(key, e);
            return false;
        }
    }

    /**
     * 递增 适用场景： https://blog.csdn.net/y_y_y_k_k_k_k/article/details/79218254 高并发生成订单号，秒杀类的业务逻辑等。。
     *
     * @param key 键
     * @param by  要增加几(大于0)
     * @return
     */
    public long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 递减
     *
     * @param key 键
     * @param by  要减少几(小于0)
     * @return
     */
    public long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    // ============================zset=============================

    //返回集合内指定分数范围的成员个数（Double类型）
    public Set<Object> zSGet(String key,Double min,Double max) {
        try {
            return redisTemplate.boundZSetOps(key). rangeByScore(min,max);
        } catch (Exception e) {
            log.error(key, e);
            return null;
        }
    }

    public Boolean zSSet(String key, Object value, double score) {
        try {
            return redisTemplate.opsForZSet().add(key, value, score);
        } catch (Exception e) {
            log.error(key, e);
            return false;
        }
    }

    public long zSetRemove(String key,Object value) {
        try {
            Long count = redisTemplate.boundZSetOps(key).remove(value);
            return count;
        } catch (Exception e) {
            log.error(key, e);
            return 0;
        }
    }

    /**
     * 获取hash中的某一项
     *
     * @param key  键 不能为null和空串
     * @param item 项 不能为null
     * @throws IllegalArgumentException 当key或者item为null或者空字符串时抛出
     * @return 值
     */
    public Object hGet(final String key, final String item) {
        if (key == null || key.isEmpty()) {
            throw new IllegalArgumentException("key值不能为" + key);
        }
        if (item == null || item.isEmpty()) {
            throw new IllegalArgumentException("item值不能为" + key);
        }
        return redisTemplate.opsForHash().get(key, item);
    }

    /**
     * 获取hash中的所有键值
     *
     * @param key 键
     * @throws IllegalArgumentException key值为null或者空字符串时抛出
     * @return 对应的多个键值
     */
    public Map<Object, Object> hGetAll(final String key) {
        if (key == null || key.isEmpty()) {
            throw new IllegalArgumentException("key值不能为" + key);
        }
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 设置多个hash值
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    public boolean hMultiSet(final String key, final Map<String, Object> map) {
        try {
            if (key == null || key.isEmpty()) {
                throw new IllegalArgumentException("key值不能为" + key);
            }
            if (map == null) {
                throw new IllegalArgumentException("map不能为null");
            }
            redisTemplate.opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            log.error("设置多个hash值失败，原因：" + e.getMessage());
            return false;
        }
    }

    /**
     * 设置多个hash值并设置过期时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean hMultiSet(final String key, final Map<String, Object> map, final long time) {
        try {
            if (key == null || key.isEmpty()) {
                throw new IllegalArgumentException("key值不能为" + key);
            }
            if (map == null) {
                throw new IllegalArgumentException("map不能为null");
            }
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("设置多个hash值失败，原因：" + e.getMessage());
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    public boolean hSet(final String key, final String item, final Object value) {
        try {
            if (key == null || key.isEmpty()) {
                if (item == null || item.isEmpty()) {
                    throw new IllegalArgumentException("item值不能为" + key);
                }
                throw new IllegalArgumentException("key值不能为" + key);
            }
            redisTemplate.opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            log.error("设置多个hash值失败，原因：" + e.getMessage());
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒)  注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public boolean hSet(final String key, final String item, final Object value, final long time) {
        try {
            if (key == null || key.isEmpty()) {
                if (item == null || item.isEmpty()) {
                    throw new IllegalArgumentException("item值不能为" + key);
                }
                throw new IllegalArgumentException("key值不能为" + key);
            }
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("设置多个hash值失败，原因：" + e.getMessage());
            return false;
        }
    }

    /**
     * 批量删除
     *
     * @param patternKey
     * @return
     */
    public Long delKeyByPrefix(String prex) {
        Set<String> keys = redisTemplate.keys(prex + "*");
        if (!CollectionUtils.isEmpty(keys)) {
            return redisTemplate.delete(keys);
        }
        return 0l;
    }

}

