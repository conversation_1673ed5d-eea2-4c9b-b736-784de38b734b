package io.geekidea.springbootplus.framework.util;

import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.constant.CacheConsts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.mail.*;
import javax.mail.internet.*;
import java.util.Properties;


@Component
public class EmailUtil {
    @Autowired
    private RedisUtils redisUtils;

    public void sendEmail(String to, String body, Boolean en) {
        // 发件人电子邮件地址
        String from = "<EMAIL>";

        // SMTP服务器信息
        String host = "smtp.qq.com";
        String username = "<EMAIL>";
        String password = "blxxyuofycfbeeif";

        // 设置邮件服务器属性
        Properties properties = System.getProperties();
        properties.setProperty("mail.smtp.host", host);
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.port", "587");
        properties.put("mail.smtp.starttls.enable", "true");
        properties.put("mail.smtp.ssl.protocols", "TLSv1.2");

        // 获取默认的Session对象
        Session session = Session.getDefaultInstance(properties, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });

        try {
            // 创建一个默认的MimeMessage对象
            MimeMessage message = new MimeMessage(session);

            // 设置发件人
            message.setFrom(new InternetAddress(from));

            // 设置收件人
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));

            // 设置邮件主题
            message.setSubject("【Microteam】");

            String validateMessEn = "Dear User：\n\n" +
                    "  Thank you for using our MicroTeam service. To complete your account operation, please enter the following verification code:\n\n" +
                    "  " + body + "  \n\n" +
                    "  Please note that this code will expire in 15 minutes. If it expires, please request a new verification email.\n\n" +
                    "  Thank you for your understanding and cooperation.\n\n" +
                    "  <a href='https://www.microteam.cn'>www.microteam.cn</a></p>\n";
            String validateMess = "亲爱的用户：\n\n" +
                    "  感谢你使用我们MicroTeam的服务。为了完成你的账号操作，请输入下面的验证码：\n\n" +
                    "  " + body + " \n\n" +
                    "  请注意，此验证码将在15分钟后失效。如果过期，请重新请求验证邮件。\n\n" +
                    "  感谢您的理解与合作。\n\n" +
                    "  <a href='https://www.microteam.cn'>www.microteam.cn</a></p>\n";
            validateMess = validateMess.replace("\n", "<br>");
            validateMessEn = validateMessEn.replace("\n", "<br>");
            // 设置邮件内容
            if (en) {
                message.setContent(validateMessEn, "text/html; charset=utf-8");
            } else {
                message.setContent(validateMess, "text/html; charset=utf-8");
            }

            // 发送邮件
            Transport.send(message);
            System.out.println("Sent message successfully....");
        } catch (MessagingException mex) {
            mex.printStackTrace();
        }
    }

    public ApiCode verify(Boolean delete, String email, String contentBody) {
        if (contentBody == null || contentBody == "") {
            return ApiCode.PARAMETER_EXCEPTION;
        }

        Object cacheContentBody = redisUtils.get(CacheConsts.EMAIL_MESSAGE_PREFIX + email);
        if (cacheContentBody == null) {
            //验证码过期
            return ApiCode.PHONE_CODE_DISABLED;
        } else {
            if (!contentBody.equals(cacheContentBody.toString())) {
                //验证失败
                return ApiCode.LOGGED_PHONE_CODE_ERROR;
            }
        }
        if (!delete) {
            //验证成功后延时删除
            redisUtils.set(CacheConsts.EMAIL_MESSAGE_PREFIX + email, cacheContentBody, 180l);
        } else {
            //验证成功后立马删除
            redisUtils.del(CacheConsts.EMAIL_MESSAGE_PREFIX + email);
        }
        return null;
    }
}
