/*
 * Copyright 2019-2029 geekidea(https://github.com/geekidea)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.geekidea.springbootplus.framework.util;

import io.geekidea.springbootplus.config.constant.DatePattern;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2018-11-08
 */
public class DateUtil {

    public static String getDateString(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DatePattern.YYYY_MM_DD);
        String dateString = simpleDateFormat.format(date);
        return dateString;
    }

    public static String getDateTimeString(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DatePattern.YYYY_MM_DD_HH_MM_SS);
        String dateString = simpleDateFormat.format(date);
        return dateString;
    }

    /**
     * @desc: 获取时间年月日时分秒
     * @author: DH
     * @date: 2021/10/19 16:27
     */
    public static String getYTD(String param, Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (param.equals("年"))
            return calendar.get(Calendar.YEAR) + "";
        if (param.equals("月"))
            return (calendar.get(Calendar.MONTH) + 1) + "";
        if (param.equals("日"))
            return calendar.get(Calendar.DAY_OF_MONTH) + "";
        if (param.equals("时"))
            return calendar.get(Calendar.HOUR_OF_DAY) + "";
        if (param.equals("分"))
            return calendar.get(Calendar.MINUTE) + "";
        if (param.equals("秒"))
            return calendar.get(Calendar.SECOND) + "";
        return "";
    }

    /**
     * @desc: 获取延时多少天的日期
     * @author: DH
     * @date: 2023/5/8 16:01
     */
    public static Date getDateDelayedByDay(Date date, int day) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        // 把日期往后增加一天,整数  往后推,负数往前移动
        calendar.add(Calendar.DATE, day);
        // 这个时间就是日期往后推一天的结果
        date = calendar.getTime();
        return date;
    }

    /**
     * @desc: 返回第几周
     * @author: DH
     * @date: 2023/11/7 17:19
     */
    public static int getWeekOfYear(String dateTimeStr) {
        LocalDate date = LocalDate.parse(dateTimeStr, java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return date.get(WeekFields.of(Locale.getDefault()).weekOfYear());
    }

    /**
     * @desc: 计算年龄
     * @author: DH
     * @date: 2023/11/23 17:32
     */
    public static int getAge(Date birthDate) {
        if (birthDate == null) {
            return 0;
        }
        LocalDate today = LocalDate.now();  // 获取当前日期

        // 将Date转换为LocalDate以进行计算
        LocalDate localBirthDate = birthDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 使用Period.between方法计算年龄
        Period period = Period.between(localBirthDate, today);

        return period.getYears();
    }

    public static List<String> displayDateIncrement1(LocalDate startDate, LocalDate endDate, int type) {
        List<String> dates = new ArrayList<>();
        while (!startDate.isAfter(endDate)) {
            if (type == 1) {
                dates.add(startDate.toString());
                startDate = startDate.plusDays(1); //日
            } else if (type == 2) {
                dates.add(String.valueOf(startDate.get(WeekFields.of(Locale.getDefault()).weekOfYear())));
                startDate = startDate.plusWeeks(1); //周
            } else if (type == 3) {
                dates.add(startDate.getYear() + "-" + startDate.getMonthValue());
                startDate = startDate.plusMonths(1); //月
            } else if (type == 4) {
                dates.add(String.valueOf(startDate.getYear()));
                startDate = startDate.plusYears(1); //年
            }
        }
        return dates;
    }

    public static List<String> displayDateIncrement2(String startDateString, String endDateString, int type) {
        List<String> dates = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startDateString, formatter);
        LocalDate endDate = LocalDate.parse(endDateString, formatter);
        if (startDate.getYear() != endDate.getYear()) {
            endDate = startDate.withDayOfYear(startDate.lengthOfYear());
            dates.addAll(displayDateIncrement1(startDate, endDate, type));
            endDate = LocalDate.parse(endDateString, formatter);
            startDate = endDate.withDayOfYear(1);
            dates.addAll(displayDateIncrement1(startDate, endDate, type));
        }else{
            dates.addAll(displayDateIncrement1(startDate, endDate, type));
        }
        return dates;
    }
    public static List<String> displayDateIncrement(String startDate, String endDate, int type) {
        List<String> dateList = new ArrayList<>();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        switch (type) {
            case 1: // 天
                while (!start.isAfter(end)) {
                    dateList.add(start.format(DateTimeFormatter.ISO_DATE));
                    start = start.plusDays(1);
                }
                break;
            case 2: // 周
                while (!start.isAfter(end)) {
                    dateList.add(String.valueOf(start.get(WeekFields.ISO.weekOfWeekBasedYear())));
                    start = start.plusWeeks(1);
                }
                break;
            case 3: // 月
                while (!start.isAfter(end)) {
                    dateList.add(start.format(DateTimeFormatter.ofPattern("yyyy-MM")));
                    start = start.plusMonths(1);
                }
                break;
            case 4: // 年
                while (!start.isAfter(end)) {
                    dateList.add(String.valueOf(start.getYear()));
                    start = start.plusYears(1);
                }
                break;
            default:
                break;
        }

        return dateList;
    }


    public static void main(String[] args) {
        System.out.println(displayDateIncrement("2024-01-01","2024-01-07",2));
    }
}

