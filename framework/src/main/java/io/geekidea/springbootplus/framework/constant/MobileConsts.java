package io.geekidea.springbootplus.framework.constant;

import com.alibaba.fastjson.JSONObject;

public class MobileConsts {
    public static final String ACCESSKEYID = "LTAIN4ch8Vd9AcMt";
    public static final String ACCESSSECRET = "s47i6SbjpiQEDfZfmpqAxWumSQx4Cb";
    public static final String Domain = "dysmsapi.aliyuncs.com";
    public static final String Version = "2017-05-25";
    public static final String action = "SendSms";
    public static final String RegionId = "cn-hangzhou";
    public static final String SIGNNAME = "深圳市微队信息技术";
    public static final String  INTERNATIONAL_TEMPLATE = "SMS_149101410";
    public static final String  CHINA_TEMPLATE = "SMS_171856487";

    public static String packTemplateParam(String code) {
        JSONObject object = new JSONObject();
        object.put("code",code);
        return object.toJSONString();
    }
}
