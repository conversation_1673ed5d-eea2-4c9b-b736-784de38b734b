package io.geekidea.springbootplus.framework.util;

import com.baidu.aip.contentcensor.AipContentCensor;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
@Component
public class SensitiveWordUtil {
    // 初始化一个AipContentCensor
     AipContentCensor client = new AipContentCensor("26686628", "2Vt4vwlpPBn4V3IBs49q8UOF", "gIcIVBwVYvMT3kFCR29cq9DXIRdhkts3");

    /**
     * @desc: 敏感词判断
     * @author: DH
     * @date: 2022/7/13 10:57
     */
    public boolean textCensorUserDefined(String text) {
            JSONObject response = client.textCensorUserDefined(text);
            try {
                System.out.println(response.toString());
                if (response.getString("conclusion").equals("合规")) {
                    return true;
                } else {
                    return false;
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        return false;
    }

    public boolean judgeContainsStr(String cardNum) {

        String regex=".*[a-zA-Z]+.*";

        Matcher m=Pattern.compile(regex).matcher(cardNum);

        return m.matches();

    }

}
