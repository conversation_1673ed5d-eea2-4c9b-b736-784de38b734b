/*
 * Copyright 2019-2029 geekidea(https://github.com/geekidea)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.geekidea.springbootplus.framework.common.api;

import com.alibaba.fastjson.JSON;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * REST API 响应码
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-08
 */
public enum ApiCode {

    /**
     * 操作成功
     **/
    SUCCESS(200, "操作成功"),
    /**
     * 非法访问
     **/
    UNAUTHORIZED(401, "非法访问"),
    /**
     * 没有权限
     **/
    NOT_PERMISSION(403, "没有权限"),
    /**
     * 你请求的资源不存在
     **/
    NOT_FOUND(404, "你请求的资源不存在"),
    /**
     * 操作失败
     **/
    FAIL(500, "操作失败"),
    /**
     * 登录失败
     **/
    LOGIN_EXCEPTION(4000, "登录失败"),
    /**
     * 系统异常
     **/
    SYSTEM_EXCEPTION(5000, "系统异常"),
    /**
     * 请求参数校验异常
     **/
    PARAMETER_EXCEPTION(5001, "请求参数校验异常"),
    /**
     * 请求参数解析异常
     **/
    PARAMETER_PARSE_EXCEPTION(5002, "请求参数解析异常"),
    /**
     * HTTP内容类型异常
     **/
    HTTP_MEDIA_TYPE_EXCEPTION(5003, "HTTP内容类型异常"),
    /**
     * 系统处理异常
     **/
    SPRING_BOOT_PLUS_EXCEPTION(5100, "系统处理异常"),
    /**
     * 业务处理异常
     **/
    BUSINESS_EXCEPTION(5101, "业务处理异常"),
    /**
     * 数据库处理异常
     **/
    DAO_EXCEPTION(5102, "数据库处理异常"),
    /**
     * 验证码校验异常
     **/
    VERIFICATION_CODE_EXCEPTION(5103, "验证码校验异常"),
    /**
     * 登录授权异常
     **/
    AUTHENTICATION_EXCEPTION(5104, "登录授权异常"),
    /**
     * 没有访问权限
     **/
    UNAUTHENTICATED_EXCEPTION(5105, "没有访问权限"),
    /**
     * 没有访问权限
     **/
    UNAUTHORIZED_EXCEPTION(5106, "没有访问权限"),
    /**
     * JWT Token解析异常
     **/
    JWTDECODE_EXCEPTION(5107, "Token解析异常"),

    HTTP_REQUEST_METHOD_NOT_SUPPORTED_EXCEPTION(5108, "METHOD NOT SUPPORTED"),

    TEXT_CENSOR_USER_DEfIRED_EXCEPTION(5109, "包含敏感词"),
    MAC_NOTEXIST(20042,"MAC不存在"),
    CODE_NOTEXIST(20043,"二维码不存在"),
    BOX_NOTEXIST(20040,"box不存在"),
    HARDWARE_BIND(20041,"球鞋已被绑定"),
    NOT_LOGGED_IN(20004, "未登录"),
    PICTURE_BEYOND_LIMIT(60006,"图片超出限制"),
    THERE_IS_NO_THE_DATA(60005, "这条数据不存在"),
    UNSELECTD_FILES(60004, "未上传文件"),
    INVALID_PARAMETER(60003, "参数不合法"),
    DUPLICATE_START_HARDWARE(60002, "设备已经启动"),
    THERE_IS_ALREADY_ORGANIZE(50006, "已经有了机构"),
    IS_NOT_THE_TEAM_COATCH(50005, "不是该队的教练"),
    REMOVE_COACH_IS_FAILURE(50004, "删除教练失败"),
    COACH_USERNMAE_ALREADY_EXITS(50003, "用户名已存在"),
    USER_IS_NOT_ORGANIZE(50002, "用户没有本机构权限"),
    USER_IS_NOT_COACH(50001, "用户没有教练权限"),
    USERNAME_ERROR(999, "账号不存在"),
    PASSWORD_ERROR(1000, "密码错误"),
    USERNAME_PASSWORD_ISNULL(1001, "用户名或密码为空" ),
    ACCOUNT_HAS_LOGGED(20003, "会话失效请重新登陆"),
    LOGGED_THIRD_ERROR(20005, "第三方登录失败" ),
    LOGGED_PHONE_CODE_ERROR(20006, "验证码不正确" ),
    PHONE_CODE_OFTEN(20007, "验证码操作频繁,请稍后再试" ),
    PHONE_CODE_SEND_ERROR(20008, "验证码发送失败" ),
    PHONE_CODE_DISABLED(20009, "请重新获取验证码"),
    PHONE_INEXISTENCE(20010, "该手机号未绑定"),
    BIND_THIRD_ALREADY_BIND_ERROR(20011, "该第三方账号已被绑定"),
    USER_INEXISTENCE(20014, "用户不存在" ),
    PHONE_USER_VERIFY_FALSE(20015, "输入的手机号不正确"),
    SMS_ASTRICT_MINUTE(20016, "获取短信验证码次数超限，1 分钟后再重新操作。" ),
    SMS_ASTRICT_HOUR(20017, "获取短信验证码次数超限，1 小时后再重新操作。" ),
    SMS_ASTRICT_DAY(20018, "获取短信验证码次数超限，明天后再重新操作。" ),
    ORIGINAL_PASSWORD_ERROR(20019, "原密码错误" ),
    APPMSG_USER(20034,"已申请关联球员，请勿重复申请"),
    PHONE_REPETITION(20035,"手机号重复"),
    STU_USER(20036,"已有关联用户"),
    APPMSG_USE(20037,"已申请其他球员成功，请勿在申请"),
    ROLEUSER(20038,"请使用机构账号登录"),
    NOTMSG(20039,"没有录入信息"),
    PASSWORD_ISNULL(20040,"密码为空"),
    TOKEN_ISNULL(20041,"token为空"),
    CARD_REPETITION(20044,"学号重复"),
    SHIRT_REPETITION(20045,"篮球球衣重复"),
    HARDWAREGROUP_REP(20046,"设备组重复"),
    HARDWAREGROUP_BIND(20047,"该设备已被设备组其他班级绑定"),
    HARDWAREGROUP_BIND_S(20048,"该设备已被设备组其他学校绑定"),
    HARDWAREGROUP_REPLACE(20049,"替换的设备已被其他设备组绑定"),
    HARDWAREGROUP_TEAM_EXCEED(20050,"设备组绑定超出数量"),
    FOOTBALLSHIRT_REPETITION(20051,"足球球衣重复"),

    EMAIL_REPETITION(20052,"邮箱号重复"),
    ;

    private final int code;
    private final String message;

    ApiCode(final int code, final String message) {
        this.code = code;
        this.message = message;
    }

    public static ApiCode getApiCode(int code) {
        ApiCode[] ecs = ApiCode.values();
        for (ApiCode ec : ecs) {
            if (ec.getCode() == code) {
                return ec;
            }
        }
        return SUCCESS;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String json() {
        Map<String, Object> map = new HashMap<>();
        map.put("code", this.code);
        map.put("message", this.message);
        return JSON.toJSONString(map);
    }
}
