package io.geekidea.springbootplus.framework.util;

import io.geekidea.springbootplus.framework.push.AndroidNotification;
import io.geekidea.springbootplus.framework.push.PushClient;
import io.geekidea.springbootplus.framework.push.android.AndroidListcast;
import io.geekidea.springbootplus.framework.push.android.AndroidUnicast;
import io.geekidea.springbootplus.framework.push.ios.IOSListcast;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.concurrent.locks.ReentrantLock;

import static io.geekidea.springbootplus.framework.constant.Const.UMENGPRODUCTIONMODE;

@Service
public class UPushUtil {

    private String appkey = null;
    private String appMasterSecret = null;
    private String appkeyApp = null;
    private String appMasterSecretApp = null;
    private String appkeyIOS = null;
    private String appMasterSecretIOS = null;
    private volatile PushClient client = new PushClient();

    public UPushUtil() {
        appkey = "62f1d31388ccdf4b7efa596e";
        appMasterSecret = "yxqqmvhomyaklljmroyslaiuvipqydda";
        appkeyApp = "62f47dae05844627b519930b";
        appMasterSecretApp = "9zxnsvonhqm1ehghgii8vgmvxnkdwomz";
        appkeyIOS = "557e52f667e58e5f89000d17";
        appMasterSecretIOS = "xnuypo74otruri5e3vx2tufrmr5o1aln";
    }

    public boolean sendAndroidUnicast(String deviceToken, JSONObject message) throws Exception {
        AndroidUnicast unicast = new AndroidUnicast(appkey, appMasterSecret);
        unicast.setDeviceToken(deviceToken);
        unicast.setCustomField(message);
        unicast.setDisplayType(AndroidNotification.DisplayType.MESSAGE);
        unicast.setProductionMode(UMENGPRODUCTIONMODE);
        client.send(unicast);
        System.out.println(message);
        return true;
    }

    public boolean sendAndroidUnicastApp(String deviceToken, JSONObject message) throws Exception {
        AndroidUnicast unicast = new AndroidUnicast(appkeyApp, appMasterSecretApp);
        unicast.setDeviceToken(deviceToken);
        unicast.setCustomField(message);
        unicast.setDisplayType(AndroidNotification.DisplayType.MESSAGE);
        unicast.setProductionMode(UMENGPRODUCTIONMODE);
        client.send(unicast);
        System.out.println(message);
        return true;
    }

    public void sendAndroidListcast(String deviceTokens, String text) throws Exception {
        AndroidListcast listcast = new AndroidListcast(appkey, appMasterSecret);
        listcast.setDeviceToken(deviceTokens);
        listcast.setDisplayType(AndroidNotification.DisplayType.MESSAGE);
        listcast.setProductionMode(true);
        listcast.setCustomField(text);
        client.send(listcast);
    }

    public void sendIOSListcast(String deviceTokens, String text) throws Exception {
        IOSListcast unicast = new IOSListcast(appkeyIOS, appMasterSecretIOS);
        unicast.setDeviceToken(deviceTokens);
        unicast.setProductionMode(UMENGPRODUCTIONMODE);
        unicast.setCustomizedField("message", text);
        unicast.setCustomizedField("display_type", "1");
        unicast.setContentAvailable(1);
        client.send(unicast);
    }


}