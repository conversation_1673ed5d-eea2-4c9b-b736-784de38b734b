
package io.geekidea.springbootplus.framework.common.exception;

import io.geekidea.springbootplus.framework.common.api.ApiCode;

public class TextCensorUserDefinedException extends SpringBootPlusException {
    private static final long serialVersionUID = -2303357122330162359L;

	public TextCensorUserDefinedException(String message) {
        super(message);
    }

    public TextCensorUserDefinedException(Integer errorCode, String message) {
        super(errorCode, message);
    }

    public TextCensorUserDefinedException(ApiCode apiCode) {
        super(apiCode);
    }
}
