package io.geekidea.springbootplus.framework.aop;

import com.alibaba.fastjson.JSON;
import io.geekidea.springbootplus.framework.aop.annotation.TextCensorUserDefined;
import io.geekidea.springbootplus.framework.common.exception.TextCensorUserDefinedException;
import io.geekidea.springbootplus.framework.util.SensitiveWordUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.annotation.Annotation;

@Aspect
@Component
public class PreconditionAspect {
    @Autowired
    private SensitiveWordUtil sensitiveWordUtil;

    @Pointcut("@annotation(precondition)")
    public void preconditionPointcut(TextCensorUserDefined precondition) {
    }

    @Before("preconditionPointcut(precondition)")
    public void checkPrecondition(JoinPoint joinPoint, TextCensorUserDefined precondition) {
       // MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();
     //   System.out.println("Checking precondition for method: " + methodSignature.getMethod().getName());
        if (sensitiveWordUtil.textCensorUserDefined(JSON.toJSONString(args))) {
        } else {
            throw new TextCensorUserDefinedException("包含敏感词！！！");
        }
    }
}