package io.geekidea.springbootplus.framework.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import io.geekidea.springbootplus.framework.common.api.ApiCode;
import io.geekidea.springbootplus.framework.constant.CacheConsts;
import io.geekidea.springbootplus.framework.constant.MobileConsts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class MobileUtils {

    @Autowired
    RedisUtils redisUtils;

    /**
     * @desc: 发送短信
     * @Param phoneNumber 手机号，countryTelCode 国家手机代号如中国86，contentBody 内容（验证码）
     * @author: DH
     * @date: 2020/7/31 14:18
     */
    public JSONObject sendSms(String phoneNumber, String countryTelCode, String contentBody) {
        if (countryTelCode.equals("1")) {
            return sendSmsAmerica(countryTelCode + phoneNumber, contentBody,countryTelCode);
        }
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", MobileConsts.ACCESSKEYID, MobileConsts.ACCESSSECRET);
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(MobileConsts.Domain);
        request.setSysVersion(MobileConsts.Version);
        request.setSysAction(MobileConsts.action);
        request.putQueryParameter("RegionId", MobileConsts.RegionId);
        request.putQueryParameter("PhoneNumbers", phoneNumber);
        request.putQueryParameter("SignName", MobileConsts.SIGNNAME);
        if (countryTelCode.equals("86") || countryTelCode.equals("")) {
            request.putQueryParameter("TemplateCode", MobileConsts.CHINA_TEMPLATE);
        } else {
            request.putQueryParameter("TemplateCode", MobileConsts.INTERNATIONAL_TEMPLATE);
        }
        request.putQueryParameter("TemplateParam", MobileConsts.packTemplateParam(contentBody));
        String responseData = "";
        JSONObject jsonObject = null;
        try {
            CommonResponse response = client.getCommonResponse(request);
            responseData = response.getData();
            jsonObject = JSON.parseObject(responseData);
            if ("OK".equals(jsonObject.getString("Code"))) {
                //将短信存入redis
            } else {

            }
        } catch (ClientException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    public JSONObject sendSmsAmerica(String phoneNumber, String contentBody,String countryTelCode) {
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", MobileConsts.ACCESSKEYID, MobileConsts.ACCESSSECRET);
        IAcsClient client = new DefaultAcsClient(profile);
        // 创建API请求并设置参数
        CommonRequest request = new CommonRequest();
        request.setSysAction("SendMessageToGlobe");
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(MobileConsts.Domain);
        request.setSysVersion(MobileConsts.Version);
        request.putQueryParameter("Message", "【MicroteamApp】code：" + contentBody);
        request.putQueryParameter("To", phoneNumber);
        request.putQueryParameter("From", countryTelCode.equals("1")?"18667851350":"");
        request.putQueryParameter("Type", "OTP");
        try {
            CommonResponse response = client.getCommonResponse(request);
            System.out.println(response.getData());
            return JSON.parseObject(response.getData());
        } catch (ServerException e) {
            e.printStackTrace();
        } catch (ClientException e) {
            // 打印错误码
            System.out.println("ErrCode:" + e.getErrCode());
            System.out.println("ErrMsg:" + e.getErrMsg());
            System.out.println("RequestId:" + e.getRequestId());
        }
        return null;
    }


    /**
     * @desc: 审核电话号码的格式
     * @author: DH
     * @date: 2020/8/1 16:31
     */
    public Map<Integer, String> reviewPhoneNumber(String phoneNumber_original) {
        String countryTelCode = "";  //国家电话区号
        String phoneCode = "";       //手机电话号码（不带区号）
        String international_phoneNumber = "";  //国际电话号码，格式：国家区号-电话号码

        int length = phoneNumber_original.length();

        int countryTelCode_len = phoneNumber_original.lastIndexOf("-");

        //如果存在区号，则将区号和电话分离
        if (countryTelCode_len >= 0) {
            countryTelCode = phoneNumber_original.substring(0, countryTelCode_len);
            phoneCode = phoneNumber_original.substring(countryTelCode_len + 1, length);

            international_phoneNumber = phoneNumber_original;
        } else {
            phoneCode = phoneNumber_original;
            international_phoneNumber = "86-" + phoneNumber_original;
        }

        Map<Integer, String> retMap = new HashMap<Integer, String>();

        retMap.put(0, international_phoneNumber); //国际电话
        retMap.put(1, countryTelCode); //区号
        retMap.put(2, phoneCode);      //电话号码

        return retMap;
    }

    /**
     * @desc: 验证验证码
     * @author: DH
     * @date: 2020/8/4 10:08
     */
    public ApiCode checkVerificationCode(String credential, String contentBody, Boolean delete) {
        if (contentBody == null || contentBody == "") {
            return ApiCode.PARAMETER_EXCEPTION;
        }

        Object cacheContentBody = redisUtils.get(CacheConsts.MOBILE_PHONE_MESSAGE_PREFIX + credential);
        if (cacheContentBody == null) {
            //验证码过期
            return ApiCode.PHONE_CODE_DISABLED;
        } else {
            if (!contentBody.equals(cacheContentBody.toString())) {
                //验证失败
                return ApiCode.LOGGED_PHONE_CODE_ERROR;
            }
        }
        if (!delete) {
            //验证成功后延时删除
            redisUtils.set(CacheConsts.MOBILE_PHONE_MESSAGE_PREFIX + credential, cacheContentBody, 180l);
        } else {
            //验证成功后立马删除
            redisUtils.del(CacheConsts.MOBILE_PHONE_MESSAGE_PREFIX + credential);
        }
        return null;
    }
}
