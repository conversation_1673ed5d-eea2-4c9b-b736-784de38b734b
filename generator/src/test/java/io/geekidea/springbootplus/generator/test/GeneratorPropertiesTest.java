/*
 * Copyright 2019-2029 geekidea(https://github.com/geekidea)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.geekidea.springbootplus.generator.test;

import io.geekidea.springbootplus.generator.CodeGenerator;
import io.geekidea.springbootplus.generator.properties.GeneratorProperties;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 生成代码配置文件读取测试
 *
 * <AUTHOR>
 * @date 2020/3/12
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = GeneratorTestApplication.class)
public class GeneratorPropertiesTest {

    @Autowired
    private GeneratorProperties generatorProperties;

    @Autowired
    private CodeGenerator codeGenerator;

    @Test
    public void test(){
        codeGenerator.generator(generatorProperties);
    }

}
