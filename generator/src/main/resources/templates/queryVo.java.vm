package ${cfg.queryVoPackage};

#if(${swagger2})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
#if(${entityLombokModel})
import lombok.Data;
import lombok.experimental.Accessors;
#end
import java.io.Serializable;
import java.util.Date;

/**
 * <pre>
 * $!{table.comment} 查询结果对象
 * </pre>
 *
 * <AUTHOR>
 * @date ${date}
 */
#if(${entityLombokModel})
@Data
@Accessors(chain = true)
#end
@ApiModel(value = "${entity}${cfg.queryVo}对象")
public class ${entity}${cfg.queryVo} implements Serializable {
    private static final long serialVersionUID = 1L;
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})

#if(${field.keyFlag})
#set($keyPropertyName=${field.propertyName})
#end
#if("$!field.comment" != "")
    #if(${swagger2})
    @ApiModelProperty("${field.comment}")
    #else
/**
     * ${field.comment}
     */
     #end
#end
    private ${field.propertyType} ${field.propertyName};
#end
}