package ${package.Entity};

#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
#if(${swagger2})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
#if(${entityLombokModel})
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
#end
#if(${cfg.paramValidation})
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import ${cfg.validatorUpdatePackage};
#end

/**
 * $!{table.comment}
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${entityLombokModel})
@Data
@Accessors(chain = true)
#if(${superEntityClass})
@EqualsAndHashCode(callSuper = true)
#else
@EqualsAndHashCode(callSuper = false)
#end
#end
#if(${table.convert})
@TableName("${table.name}")
#end
#if(${swagger2})
@ApiModel(value = "${entity}对象")
#end
#if(${superEntityClass})
public class ${entity} extends ${superEntityClass}#if(${activeRecord})<${entity}>#end {
#elseif(${activeRecord})
public class ${entity} extends Model<${entity}> {
#else
public class ${entity} implements Serializable {
#end
    private static final long serialVersionUID = 1L;

## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
## 如果没有默认值，且不为空，则设置非空校验
#if(${cfg.paramValidation} && ${field.customMap.null} == 'NO' && !${field.customMap.default})
    #if(${field.keyIdentityFlag})
    @NotNull(message = "${field.propertyName}不能为空", groups = {Update.class})
    #elseif(${field.propertyType} == 'String')
    @NotBlank(message = "${field.comment}不能为空")
    #else
    @NotNull(message = "${field.comment}不能为空")
    #end
#end
#if("$!field.comment" != "")
    #if(${swagger2})
    @ApiModelProperty("${field.comment}")
    #else
/**
     * ${field.comment}
     */
     #end
#end
#if(${field.keyFlag})
## 主键
#if(${field.keyIdentityFlag})
    @TableId(value = "${field.name}", type = IdType.AUTO)
#elseif(!$null.isNull(${idType}) && "$!idType" != "")
## 设置主键注解
    @TableId(value = "${field.name}", type = IdType.${idType})
#elseif(${field.convert})
    @TableId("${field.name}")
#end
## 普通字段
#elseif(${field.fill})
## -----   存在字段填充设置   -----
#if(${field.convert})
    @TableField(value = "${field.name}", fill = FieldFill.${field.fill})
#else
    @TableField(fill = FieldFill.${field.fill})
#end
#elseif(${field.convert})
    @TableField("${field.name}")
#end
## 乐观锁注解
#if(${versionFieldName}==${field.name})
    @Version
#end
## 逻辑删除注解
#if(${logicDeleteFieldName}==${field.name})
    @TableLogic
#end
    private ${field.propertyType} ${field.propertyName};

#end
}
