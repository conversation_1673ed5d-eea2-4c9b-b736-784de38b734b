package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
#if(${cfg.generatorPageParam})
import ${cfg.pageParamClass};
#end
#if(${cfg.generatorQueryVo})
import ${cfg.queryVoClass};
#end
#if(${cfg.generatorStrategy} == 'SINGLE')
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
#end
import ${superServiceImplClassPackage};
#if(${cfg.generatorStrategy} != 'SIMPLE')
import ${cfg.pagingClass};
import ${cfg.pageInfoClass};
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
#end
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * $!{table.comment} 服务实现类
 *
 * <AUTHOR>
 * @since ${date}
 */
@Slf4j
@Service
public class ${table.serviceImplName} extends BaseServiceImpl<${table.mapperName}, ${entity}> implements ${table.serviceName} {

    @Autowired
    private ${table.mapperName} ${cfg.mapperObjectName};
#if(${cfg.generatorStrategy} != 'SIMPLE')

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save${entity}(${entity} ${cfg.entityObjectName}) throws Exception {
        return super.save(${cfg.entityObjectName});
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update${entity}(${entity} ${cfg.entityObjectName}) throws Exception {
        return super.updateById(${cfg.entityObjectName});
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean delete${entity}(Long id) throws Exception {
        return super.removeById(id);
    }

    #if(${cfg.generatorStrategy} != 'SINGLE')
    @Override
    public ${entity}${cfg.queryVo} get${entity}ById(Serializable id) throws Exception {
    return ${cfg.mapperObjectName}.get${entity}ById(id);
    }

    #end
    @Override
    public Paging<${entity}${cfg.queryVo}> get${entity}PageList(${entity}${cfg.pageParam} ${cfg.entityObjectName}${cfg.pageParam}) throws Exception {
        Page<${entity}${cfg.queryVo}> page = new PageInfo<>(${cfg.entityObjectName}${cfg.pageParam}, OrderItem.desc(getLambdaColumn(${entity}::getCreateTime)));
    #if(${cfg.generatorStrategy} != 'SINGLE')
        IPage<${entity}${cfg.queryVo}> iPage = ${cfg.mapperObjectName}.get${entity}PageList(page, ${cfg.entityObjectName}${cfg.pageParam});
    #else
        LambdaQueryWrapper<${entity}> wrapper = new LambdaQueryWrapper<>();
        IPage<${entity}${cfg.queryVo}> iPage = ${cfg.mapperObjectName}.selectPage(page, wrapper);
    #end
        return new Paging<${entity}${cfg.queryVo}>(iPage);
    }
#end

}
