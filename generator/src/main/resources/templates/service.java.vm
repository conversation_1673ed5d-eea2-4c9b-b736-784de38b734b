package ${package.Service};

import ${package.Entity}.${entity};
#if(${cfg.generatorPageParam})
import ${cfg.pageParamClass};
#end
import ${superServiceClassPackage};
#if(${cfg.generatorQueryVo})
import ${cfg.queryVoClass};
#end
#if(${cfg.generatorStrategy} != 'SIMPLE')
import ${cfg.pagingClass};
#end

/**
 * $!{table.comment} 服务类
 *
 * <AUTHOR>
 * @since ${date}
 */
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {
#if(${cfg.generatorStrategy} != 'SIMPLE')

    /**
     * 保存
     *
     * @param ${cfg.entityObjectName}
     * @return
     * @throws Exception
     */
    boolean save${entity}(${entity} ${cfg.entityObjectName}) throws Exception;

    /**
     * 修改
     *
     * @param ${cfg.entityObjectName}
     * @return
     * @throws Exception
     */
    boolean update${entity}(${entity} ${cfg.entityObjectName}) throws Exception;

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    boolean delete${entity}(Long id) throws Exception;

    #if(${cfg.generatorStrategy} != 'SINGLE')
    /**
     * 根据ID获取查询对象
     *
     * @param id
     * @return
     * @throws Exception
     */
    ${entity}${cfg.queryVo} get${entity}ById(Serializable id) throws Exception;
    #end

    /**
     * 获取分页对象
     *
     * @param ${cfg.entityObjectName}QueryParam
     * @return
     * @throws Exception
     */
    Paging<${entity}${cfg.queryVo}> get${entity}PageList(${entity}${cfg.pageParam} ${cfg.entityObjectName}${cfg.pageParam}) throws Exception;
#end

}
