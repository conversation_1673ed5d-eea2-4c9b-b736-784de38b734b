<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright 2019-2029 geekidea(https://github.com/geekidea)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.geekidea.springbootplus</groupId>
        <artifactId>parent</artifactId>
        <version>2.0</version>
    </parent>

    <artifactId>config</artifactId>
    <name>config</name>
    <description>项目配置模块</description>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>config/application-*.yml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources/config</directory>
                <filtering>true</filtering>
                <targetPath>config</targetPath>
                <includes>
                    <include>application-${profileActive}.yml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
