<!--
  ~ Copyright 2019-2029 geekidea(https://github.com/geekidea)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>验证码</title>
</head>
<body>

<div>
    <p>方式一：获取图片</p>
    <img id="imageCode" src="http://localhost:8888/verificationCode/getImage" alt="" onclick="changeImage()">
    <p>verifyToken：查看Responses Headers</p>
</div>

<hr>

<div>
    <p>方式二：获取base64图片编码</p>
    <img src="" alt="" id="base64ImageCode" onclick="changeBase64Image()">
    <p id="verifyToken"></p>
</div>

<script type="text/javascript">
    function changeImage() {
        document.getElementById('imageCode').src = "http://localhost:8888/api/verificationCode/getImage?time=" + new Date().getTime();
    }

    var url = "http://localhost:8888/api/verificationCode/getBase64Image";

    function changeBase64Image() {
        var  xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function () {
            if (xhr.readyState == 4 && xhr.status == 200) {
                var result = JSON.parse(xhr.responseText);
                document.getElementById('base64ImageCode').src = result.data.image;
                document.getElementById('verifyToken').innerText = "verifyToken：" + result.data.verifyToken;
            }
        };
        xhr.open("GET", url, true);
        xhr.send(null);
    }

    changeBase64Image();

</script>

</body>
</html>