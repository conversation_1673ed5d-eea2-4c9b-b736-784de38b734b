spring-boot-plus:
  # 是否启用ansi控制台输出有颜色的字体，local环境建议开启，服务器环境设置为false
  enable-ansi: false
  # 当前环境服务IP地址
  server-ip: *************
  # 文件上传下载配置
  upload-path: /opt/upload/
  # AOP配置
  aop:
    log:
      enable: true
      log-print-type: LINE
      request-log-format: false
      response-log-format: false

spring:
  datasource:
    url: **********************************************************************************************************************************************************
    username: root
    password: Springbootplus666!

  # Redis配置
  redis:
    database: 0
    host: localhost
    password:
    port: 6379

# 打印SQL语句和结果集，本地开发环境可开启，线上注释掉
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

