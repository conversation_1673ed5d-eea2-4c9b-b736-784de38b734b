spring-boot-plus:
  # 是否启用ansi控制台输出有颜色的字体，dev环境建议开启，服务器环境设置为false
  enable-ansi: false
  # 当前环境服务IP地址
  server-ip: *************
  # 文件上传下载配置
  upload-path: /opt/upload/

server:
  tomcat:
    # 设置Tomcat的HTTP POST请求最大大小，-1表示无限制
    max-http-post-size: -1
    # 设置Tomcat的最大吞吐量大小
    max-swallow-size: -1

spring:
  servlet:
    # 文件上传配置
    multipart:
      # 单个文件最大值
      max-file-size: 100MB
      # 单个请求文件总计最大值
      max-request-size: 500MB
  datasource:
    dynamic:
      primary: teambox #设置默认的数据源或者数据源组
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        teambox:
          url: *****************************************************************************************************************************************************
          username: root
          password: Vsteam2015
        vsteam:
          url: *******************************************************************************************************************************************************
          username: root
          password: Vsteam2015
  # Redis配置
  redis:
    database: 3
    host: *************
    password: vsteam2015
    port: 6379

## 打印SQL语句和结果集，本地开发环境可开启，线上注释掉
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

system:
  url: http://127.0.0.1:7070/algorithm/getDataListTeamBox
  vsteamUrlPrefix: https://www.microteam.cn/vsteam/rest/1.0
  microteamUrlPrefix: https://www.microteam.cn/microteam/