# spring-boot-plus Common Config

############################# 访问路径、端口tomcat start  云端端口8888 contextPath /teambox_new #############################
server:
  port: 8889
  servlet:
    context-path: /teambox
  tomcat:
    max-threads: 1000
    min-spare-threads: 30
    uri-encoding: UTF-8
    # 设置Tomcat的HTTP POST请求最大大小，-1表示无限制
    max-http-post-size: -1
    # 设置Tomcat的最大吞吐量大小
    max-swallow-size: -1
############################# 访问路径、端口tomcat end ###############################

################################ spring config start ###############################
spring:
  application:
    name: spring-boot-plus
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: true
  servlet:
    # 文件上传配置
    multipart:
      # 指定上传文件的临时目录
      location: /mnt/opt/upload/tmp
      # 单个文件最大值
      max-file-size: 100MB
      # 单个请求文件总计最大值
      max-request-size: 500MB

# 当前项目maven激活环境，例如：dev/test/uat/prod，对应pom.xml中profile设置值
---
spring:
  profiles:
    active: @profileActive@

# logback.xml中有详细的日志配置
logging:
  config: classpath:config/logback.xml
  #  方便Spring Boot Admin页面上实时查看日志
  file:
    name: logs/${spring.application.name}.log
################################ spring config end #################################


############################## spring-boot-plus start ##############################
spring-boot-plus:
  # 是否启用ansi控制台输出有颜色的字体
  enable-ansi: true
  # 服务器IP地址
  server-ip: 127.0.0.1
  # 是否启用验证码
  enable-verify-code: false
  # 默认新建用户登录初始化密码
  login-init-salt: 666666
  login-init-password: admin
  login-init-head: http://${spring-boot-plus.server-ip}:8888/api/resource/logo.png
  # 实现BaseEnum接口的枚举包
  enum-packages: com.example.foorbar.enums
  # Swagger路径
  swagger-paths: /swagger-ui.html,/docs,/doc.html,/swagger-resources/**,/webjars/**,/v2/api-docs,/csrf,/v2/api-docs-ext,/null/swagger-resources/**
  # Filter配置
  filter:
    request:
      enable: true
      url-patterns: /*
      order: 1
      async: true
    xss:
      enable: true
      url-patterns: /*
      order: 2
      async: true

  # 拦截器配置
  interceptor:
    permission:
      enable: true
      include-paths: ${spring-boot-plus.swagger-paths}
    resource:
      enable: false
      include-paths: ${spring-boot-plus.resource-access-patterns}
    upload:
      enable: false
      include-paths: /upload/**
    download:
      enable: false
      include-paths: /download/**

  # AOP配置
  aop:
    # Aop日志配置
    log:
      # 是否启用
      enable: true
      # 是否启用requestId
      enable-request-id: false
      # requestId生成规则: UUID/IDWORK
      request-id-type: IDWORK
      # NONE：不打印日志
      # ORDER：请求和响应日志，按照执行顺序分开打印
      # LINE：方法执行结束时，连续分开打印请求和响应日志
      # MERGE：方法执行结束时，合并请求和响应日志，同时打印
      log-print-type: ORDER
      # 请求日志在控制台是否格式化输出，local环境建议开启，服务器环境设置为false
      request-log-format: false
      # 响应日志在控制台是否格式化输出，local环境建议开启，服务器环境设置为false
      response-log-format: false
      # 排除的路径
      exclude-paths: /,/csrf
    # 操作日志配置
    operation-log:
      # 是否启用
      enable: false
      # 排除的路径
      exclude-paths:
    # 登录日志配置
    login-log:
      # 是否启用
      enable: false
      # 登录地址
      login-path: /login
      # 登出地址
      logout-path: /logout

  # 文件上传下载配置
  # 上传路径配置
  upload-path: /opt/upload/
  # 资源访问路径
  resource-access-path: /resource/
  # 资源访问路径匹配：/resource/**
  resource-access-patterns: ${spring-boot-plus.resource-access-path}**
  # 资源访问全路径前缀：http://127.0.0.1:8888/resource/
  resource-access-url: http://${spring-boot-plus.server-ip}:${server.port}${server.servlet.context-path}${spring-boot-plus.resource-access-path}
  # 全局允许上传的类型
  allow-upload-file-extensions: jpg,png,docx,xlsx,pptx,pdf
  # 全局允许下载的类型
  allow-download-file-extensions: jpg,png,docx,xlsx,pptx,pdf

  ############################ CORS start ############################
  # CORS跨域配置，默认允许跨域
  cors:
    # 是否启用跨域，默认启用
    enable: true
    # CORS过滤的路径，默认：/**
    path: /**
    # 允许访问的源
    allowed-origins: '*'
    # 允许访问的请求头
    allowed-headers: '*'
    # 是否允许发送cookie
    allow-credentials: true
    # 允许访问的请求方式
    allowed-methods: OPTION,GET,POST
    # 允许响应的头
    exposed-headers: token
    # 该响应的有效时间默认为30分钟，在有效时间内，浏览器无须为同一请求再次发起预检请求
    max-age: 1800

  ############################ CORS end ##############################

  ########################## Resource start ##########################
  # 静态资源访问配置
  resource-handlers: |
    /static/**=classpath:/static/
    swagger-ui.html=classpath:/META-INF/resources/
    /webjars/**=classpath:/META-INF/resources/webjars/
    doc.html=classpath:/META-INF/resources/
  ########################## Resource end ############################

  ######################## Spring Shiro start ########################
  shiro:
    # 是否启用
    enable: false
    # 权限配置
    anon:
      # 排除登录登出
      - /login,/logout,/using/**
      # 排除静态资源
      - /static/**,/templates/**
      # 排除Swagger
      - ${spring-boot-plus.swagger-paths}
      # 排除actuator
      - /actuator/**
      - # 排除首页
      - /,/index.html
      # 排除测试路径
      - /hello/world,/fooBar/**,/exampleOrder/**
      # 排除druid
      - /druid/**
    # 多行字符串权限配置
    filter-chain-definitions: |
      /resource/**=anon
      /upload/**=anon
      /verificationCode/**=anon
      /enum=anon
      /getSysUserInfo=anon

  ######################## Spring Shiro end ##########################

  ############################ JWT start #############################
  jwt:
    # token请求头名称
    token-name: token
    # jwt密钥
    secret: 666666
    # 发行人
    issuer: ${spring.application.name}
    # 观众
    audience: web
    # 默认过期时间1小时，单位：秒
    expire-second: 36000
    # 是否刷新token
    refresh-token: true
    # 刷新token的时间间隔，默认10分钟，单位：秒
    refresh-token-countdown: 600
    # redis校验jwt token是否存在,可选
    redis-check: true
    # true: 同一个账号只能是最后一次登录token有效，false：同一个账号可多次登录
    single-login: false
    # 盐值校验，如果不加自定义盐值，则使用secret校验
    salt-check: true
  ############################ JWT end ###############################

############################### spring-boot-plus end ###############################


############################### mybatis-plus start #################################
mybatis-plus:
  # 启动时是否检查MyBatis XML文件是否存在
  check-config-location: true
  # 支持统配符 * 或者 ; 分割
  typeEnumsPackage: io.geekidea.springbootplus.*.enums
  # MyBatis原生配置
  configuration:
    # 字段名称下划线转驼峰命名
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      # 全局默认主键类型
      id-type: auto
      # 逻辑已删除值(默认为 1)
      logic-delete-value: 1
      # 逻辑未删除值(默认为 0)
      logic-not-delete-value: 0
  # mapper xml映射路径
  mapper-locations: classpath*:mapper/**/*Mapper.xml
################################ mybatis-plus end ##################################


############################### druid 数据源配置 start ################################
---
spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      #初始化大小
      initialSize: 5
      #最小值
      minIdle: 5
      #最大值
      maxActive: 20
      #最大等待时间，配置获取连接等待超时，时间单位都是毫秒ms
      maxWait: 60000
      #配置间隔多久才进行一次检测，检测需要关闭的空闲连接
      timeBetweenEvictionRunsMillis: 60000
      #配置一个连接在池中最小生存的时间
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，
      #'wall'用于防火墙，SpringBoot中没有log4j，我改成了log4j2
      filters: stat,wall,log4j2
      #最大PSCache连接
      maxPoolPreparedStatementPerConnectionSize: 20
      useGlobalDataSourceStat: true
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
      # 配置StatFilter
      web-stat-filter:
        #默认为false，设置为true启动
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      #配置StatViewServlet
      stat-view-servlet:
        url-pattern: "/druid/*"
        #是否可以重置
        reset-enable: true
        #启用
        enabled: true
  autoconfigure:
      #自动化配置 例外处理
      exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
############################### druid 数据源配置 end ################################


############################### Redis 公共配置 start ###############################
---
spring:
  redis:
    timeout: 3000s
    lettuce:
      pool:
        max-active: 200
        max-idle: 8
        max-wait: 10s
        min-idle: 2
      shutdown-timeout: 3s
############################### Redis 公共配置 end ##################################


#################################### Swagger start #################################
---
spring-boot-plus:
  swagger:
    # 是否启用
    enable: true
    base:
      # 扫描的包，多个包使用逗号隔开
      package: io.geekidea.springbootplus
    contact:
      email: <EMAIL>
      name: www.microteam.cn
      url: https://www.microteam.cn
    description:
    title: ${spring.application.name} API Documents
    url: https://www.microteam.cn
    version: ${project.version}
    # 自定义参数配置，可配置N个
    parameter-config:
      - name: token
        description: Token Request Header
        # header, cookie, body, query
        type: header
        data-type: String
        required: false
        # 测试接口时，自动填充token的值
        default-value:

# knife4j配置
knife4j:
  enable: ${spring-boot-plus.swagger.enable}
  basic:
    enable: true
    username: admin
    password: admin

#################################### Swagger end ###################################


############################## Spring boot admin start ##############################
---
spring:
  boot:
    admin:
      client:
        # Spring Boot Admin服务地址，参照admin模块application.yml配置
        url: http://${spring-boot-plus.server-ip}:8887
        # Spring Boot Admin账号
        username: admin
        # Spring Boot Admin密码
        password: admin
        # 当前项目实例名称
        instance:
          # client名称
          name: ${spring.application.name}
          # client ip端口
          service-base-url: http://${spring-boot-plus.server-ip}:${server.port}
      monitor:
        period: 100000
        status-lifetime: 100000
        connect-timeout: 100000
        read-timeout: 100000

# 开启和暴露端点
management:
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      #可在线查看日志
      enabled: true
  endpoints:
    web:
      exposure:
        include: '*'

# 自定义项目信息，Spring Boot Admin展示使用
info:
  project-groupId: '@project.parent.groupId@'
  project-name: ${spring.application.name}
  project-finalName: '@boot.artifact.name@'
  project-author: geekidea
  project-description: ${spring.application.name} project
  project-sourceEncoding: '@project.build.sourceEncoding@'
  project-spring-boot-version: '@spring-boot.version@'
  project-mybatis-plus-version: '@mybatis-plus-boot-starter.version@'
  project-version: '@project.version@'
  project-website: 'https://www.microteam.cn'
  project-home: http://${spring-boot-plus.server-ip}:${server.port}${server.servlet.context-path}
  project-swagger: http://${spring-boot-plus.server-ip}:${server.port}${server.servlet.context-path}/swagger-ui.html
  project-knife4j: http://${spring-boot-plus.server-ip}:${server.port}${server.servlet.context-path}/doc.html
############################## Spring boot admin end ###############################

