2025-08-04 11:45:47,580  INFO [main] [] i.g.s.SpringBootPlusApplication          55 : Starting SpringBootPlusApplication on DESKTOP-37JJ82G with PID 5492 (C:\Users\<USER>\Desktop\microteam\back\work\teambox\bootstrap\target\classes started by WD in C:\Users\<USER>\Desktop\microteam\back\work\teambox)
2025-08-04 11:45:47,583  INFO [main] [] i.g.s.SpringBootPlusApplication          655 : The following profiles are active: dev
2025-08-04 11:45:50,804  INFO [main] [] .s.d.r.c.RepositoryConfigurationDelegate 249 : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 11:45:50,816  INFO [main] [] .s.d.r.c.RepositoryConfigurationDelegate 127 : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 11:45:51,040  INFO [main] [] .s.d.r.c.RepositoryConfigurationDelegate 187 : Finished Spring Data repository scanning in 203ms. Found 0 Redis repository interfaces.
2025-08-04 11:45:51,953  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 11:45:52,088  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$3cbeceaf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 11:45:52,095  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 11:45:52,107  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 11:45:52,155  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'springDaoMethodAspect' of type [io.geekidea.springbootplus.config.SpringDaoMethodAspect$$EnhancerBySpringCGLIB$$c7be4cff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 11:45:52,191  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'druidStatInterceptor' of type [com.alibaba.druid.support.spring.stat.DruidStatInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 11:45:52,194  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'druidStatPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 11:45:52,196  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'druidStatAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 11:45:52,218  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'redisCacheConfig' of type [io.geekidea.springbootplus.config.RedisCacheConfig$$EnhancerBySpringCGLIB$$aecd10e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 11:45:52,730  INFO [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer  92 : Tomcat initialized with port(s): 8889 (http)
2025-08-04 11:45:52,742  INFO [main] [] o.a.coyote.http11.Http11NioProtocol      173 : Initializing ProtocolHandler ["http-nio-8889"]
2025-08-04 11:45:52,742  INFO [main] [] o.apache.catalina.core.StandardService   173 : Starting service [Tomcat]
2025-08-04 11:45:52,742  INFO [main] [] org.apache.catalina.core.StandardEngine  173 : Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-08-04 11:45:52,872  INFO [main] [] o.a.c.c.C.[.[localhost].[/teambox]       173 : Initializing Spring embedded WebApplicationContext
2025-08-04 11:45:52,872  INFO [main] [] o.s.web.context.ContextLoader            284 : Root WebApplicationContext: initialization completed in 5188 ms
2025-08-04 11:45:53,918  INFO [main] [] com.alibaba.druid.pool.DruidDataSource   1010 : {dataSource-1,teambox} inited
2025-08-04 11:45:53,920  INFO [main] [] com.alibaba.druid.pool.DruidDataSource   1010 : {dataSource-2,vsteam} inited
2025-08-04 11:45:53,920  INFO [main] [] c.b.d.d.DynamicRoutingDataSource         128 : dynamic-datasource - load a datasource named [teambox] success
2025-08-04 11:45:53,920  INFO [main] [] c.b.d.d.DynamicRoutingDataSource         128 : dynamic-datasource - load a datasource named [vsteam] success
2025-08-04 11:45:53,921  INFO [main] [] c.b.d.d.DynamicRoutingDataSource         263 : dynamic-datasource initial loaded [2] datasource,primary datasource named [teambox]
2025-08-04 11:45:54,097  INFO [main] [] o.s.boot.web.servlet.RegistrationBean    110 : Filter webStatFilter was not registered (possibly already registered?)
2025-08-04 11:45:54,098  INFO [main] [] o.s.boot.web.servlet.RegistrationBean    110 : Servlet statViewServlet was not registered (possibly already registered?)
2025-08-04 11:45:54,099  INFO [main] [] o.s.b.a.e.web.ServletEndpointRegistrar   75 : Registered '/actuator/jolokia' to jolokia-actuator-endpoint
2025-08-04 11:45:54,145  INFO [main] [] i.g.s.framework.core.xss.XssFilter       36 : XssFilter init
2025-08-04 11:45:54,147  INFO [main] [] i.g.s.f.core.filter.RequestDetailFilter  39 : RequestDetailFilter init
2025-08-04 11:45:56,411  INFO [main] [] org.reflections.Reflections              228 : Reflections took 75 ms to scan 2 urls, producing 2 keys and 4 values 
2025-08-04 11:45:56,660  INFO [main] [] i.g.s.framework.shiro.util.JwtUtil       51 : {"audience":"web","expireSecond":36000,"issuer":"spring-boot-plus","redisCheck":true,"refreshToken":true,"refreshTokenCountdown":600,"saltCheck":true,"secret":"666666","singleLogin":false,"tokenName":"token"}
2025-08-04 11:45:59,539  INFO [main] [] i.g.s.u.s.impl.DelayQueueServiceImpl     55 : 开始监控延迟队列：DrillPastDelayQueue
2025-08-04 11:45:59,540  INFO [main] [] i.g.s.u.s.impl.DelayQueueServiceImpl     55 : 开始监控延迟队列：GamePastDelayQueue
2025-08-04 11:45:59,540  INFO [main] [] i.g.s.u.s.impl.DelayQueueServiceImpl     55 : 开始监控延迟队列：TeamPastDelayQueue
2025-08-04 11:45:59,541  INFO [main] [] i.g.s.u.s.impl.DelayQueueServiceImpl     80 : 开始监控学校延迟队列：kindergartenMonitorDelayQueue
2025-08-04 11:46:01,937  INFO [main] [] o.s.b.a.e.web.EndpointLinksResolver      58 : Exposing 15 endpoint(s) beneath base path '/actuator'
2025-08-04 11:46:02,053  INFO [main] [] pertySourcedRequestMappingHandlerMapping 69 : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-08-04 11:46:02,085  INFO [main] [] i.g.s.config.Swagger2Config              166 : swagger scan basePackages:null
2025-08-04 11:46:02,298  INFO [main] [] o.s.s.concurrent.ThreadPoolTaskExecutor  181 : Initializing ExecutorService 'applicationTaskExecutor'
2025-08-04 11:46:02,857  INFO [main] [] o.s.s.c.ThreadPoolTaskScheduler          181 : Initializing ExecutorService
2025-08-04 11:46:03,203  INFO [main] [] o.s.s.c.ThreadPoolTaskScheduler          181 : Initializing ExecutorService 'taskScheduler'
2025-08-04 11:46:03,297  INFO [main] [] d.s.w.p.DocumentationPluginsBootstrapper 160 : Context refreshed
2025-08-04 11:46:03,318  INFO [main] [] d.s.w.p.DocumentationPluginsBootstrapper 163 : Found 1 custom documentation plugin(s)
2025-08-04 11:46:03,382  INFO [main] [] s.d.s.w.s.ApiListingReferenceScanner     41 : Scanning for api listing references
2025-08-04 11:46:03,879  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: fieldRankUsingGET_1
2025-08-04 11:46:03,881  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findListByIdsUsingGET_1
2025-08-04 11:46:03,909  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getGroupFieldUsingPOST_1
2025-08-04 11:46:03,910  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getGrowUpDataUsingPOST_1
2025-08-04 11:46:03,911  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPersonalUsingGET_1
2025-08-04 11:46:03,912  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPhysicalAnalysisUsingGET_1
2025-08-04 11:46:03,913  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPlayerDataUsingGET_1
2025-08-04 11:46:03,916  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getStuByGroupIdAndFieldIdUsingGET_1
2025-08-04 11:46:03,917  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getStuFieldUsingPOST_1
2025-08-04 11:46:03,920  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getTeamUsingGET_1
2025-08-04 11:46:03,922  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: isSetStatusUsingGET_1
2025-08-04 11:46:03,923  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: moveDistanceRankingUsingGET_1
2025-08-04 11:46:03,928  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: teamRankUsingGET_1
2025-08-04 11:46:03,931  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: unstartCountUsingGET_1
2025-08-04 11:46:03,934  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: uploadDataUsingPOST_1
2025-08-04 11:46:03,973  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findByIdUsingGET_1
2025-08-04 11:46:03,974  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findByMacUsingGET_1
2025-08-04 11:46:03,976  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findMacUsingGET_1
2025-08-04 11:46:03,984  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: startHardwaresUsingPOST_1
2025-08-04 11:46:03,987  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: updateHardwareUsingPOST_1
2025-08-04 11:46:04,021  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getKindergartenMonitorPageListUsingPOST_1
2025-08-04 11:46:04,022  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPhysicalAnalysisUsingGET_2
2025-08-04 11:46:04,023  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPlayerDataUsingGET_2
2025-08-04 11:46:04,024  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: moveDistanceRankingUsingGET_2
2025-08-04 11:46:04,035  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: recentlyUsingGET_1
2025-08-04 11:46:04,048  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: uploadDataUsingPOST_2
2025-08-04 11:46:04,069  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: addDemoMacQrcodeUsingPOST_1
2025-08-04 11:46:04,071  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findMacByboxUsingGET_1
2025-08-04 11:46:04,100  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: deleteStudentInfoUsingPOST_1
2025-08-04 11:46:04,111  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: uploadFileUsingPOST_1
2025-08-04 11:46:04,117  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: updateVersionUsingPOST_1
2025-08-04 11:46:04,153  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: addTacticsBoardUsingPOST_1
2025-08-04 11:46:04,154  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: deleteTacticsBoardUsingPOST_1
2025-08-04 11:46:04,168  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getTacticsBoardPageListUsingPOST_1
2025-08-04 11:46:04,172  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: updateTacticsBoardUsingPOST_1
2025-08-04 11:46:04,214  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getTeamUsingGET_2
2025-08-04 11:46:04,220  WARN [main] [] s.d.s.w.r.p.ParameterDataTypeReader      92 : Trying to infer dataType com.alibaba.fastjson.JSONObject
2025-08-04 11:46:04,220  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: startedCountUsingGET_1
2025-08-04 11:46:04,236  WARN [main] [] s.d.s.w.r.p.ParameterDataTypeReader      92 : Trying to infer dataType java.util.List<org.springframework.web.multipart.MultipartFile>
2025-08-04 11:46:04,266  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: loginStuUsingPOST_1
2025-08-04 11:46:04,273  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: checkPhoneExistUsingPOST_1
2025-08-04 11:46:04,274  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: checkVerificationCodeUsingPOST_1
2025-08-04 11:46:04,275  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: sendPhoneVerificationCodeUsingPOST_1
2025-08-04 11:46:04,277  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: writeOffUsingPOST_1
2025-08-04 11:46:04,303  INFO [main] [] o.a.coyote.http11.Http11NioProtocol      173 : Starting ProtocolHandler ["http-nio-8889"]
2025-08-04 11:46:04,334  INFO [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer  204 : Tomcat started on port(s): 8889 (http) with context path '/teambox'
2025-08-04 11:46:04,338  INFO [main] [] i.g.s.SpringBootPlusApplication          61 : Started SpringBootPlusApplication in 17.797 seconds (JVM running for 28.502)
2025-08-04 11:46:04,346  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        83 : projectFinalName : bootstrap-2.0
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        84 : projectVersion : 2.0
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        85 : profileActive : dev
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        86 : contextPath : /teambox
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        87 : serverIp : localhost
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        88 : port : 8889
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        102 : Admin:   http://localhost:8887
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        103 : Home:    http://localhost:8889/teambox
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        104 : druid:   http://localhost:8889/teambox/druid
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        105 : Knife4j: http://localhost:8889/teambox/doc.html
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        106 : Swagger: http://localhost:8889/teambox/swagger-ui.html
2025-08-04 11:46:04,347  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        107 : spring-boot-plus project start success...........
2025-08-04 11:46:04,355  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        109 : 
           _                _                       
          (_)              | |                      
 _ __ ___  _  ___ _ __ ___ | |_ ___  __ _ _ __ ___  
| '_ ` _ \| |/ __| '__/ _ \| __/ _ \/ _` | '_ ` _ \ 
| | | | | | | (__| | | (_) | ||  __/ (_| | | | | | |
|_| |_| |_|_|\___|_|  \___/ \__\___|\__,_|_| |_| |_|
2025-08-04 11:46:04,659  INFO [RMI TCP Connection(2)-*************] [] o.a.c.c.C.[.[localhost].[/teambox]       173 : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 11:46:04,660  INFO [RMI TCP Connection(2)-*************] [] o.s.web.servlet.DispatcherServlet        525 : Initializing Servlet 'dispatcherServlet'
2025-08-04 11:46:04,673  INFO [RMI TCP Connection(2)-*************] [] o.s.web.servlet.DispatcherServlet        547 : Completed initialization in 13 ms
2025-08-04 11:46:05,454  INFO [boundedElastic-1] [] io.lettuce.core.EpollProvider            68 : Starting without optional epoll library
2025-08-04 11:46:05,456  INFO [boundedElastic-1] [] io.lettuce.core.KqueueProvider           70 : Starting without optional kqueue library
2025-08-04 11:46:08,612  WARN [registrationTask1] [] d.c.b.a.c.r.ApplicationRegistrator       93 : Failed to register application as Application(name=spring-boot-plus, managementUrl=http://localhost:8889/teambox/actuator, healthUrl=http://localhost:8889/teambox/actuator/health, serviceUrl=http://localhost:8889/teambox) at spring-boot-admin ([http://localhost:8887/instances]): I/O error on POST request for "http://localhost:8887/instances": Connect to localhost:8887 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:8887 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-04 16:25:09,433  WARN [http-nio-8889-exec-1] [] org.apache.catalina.connector.Request    173 : Creating the temporary upload location [C:\Users\<USER>\AppData\Local\Temp\tomcat.3435469567585216462.8889\work\Tomcat\localhost\teambox\mnt\opt\upload\tmp] as it is required by the servlet [dispatcherServlet]
2025-08-04 16:25:11,904  INFO [http-nio-8889-exec-1] [] i.g.s.u.interceptor.LoginInterceptor     35 : path: /using/uploadUsing/uploadFiles
2025-08-04 16:25:12,063  INFO [http-nio-8889-exec-1] [] i.g.s.framework.log.aop.BaseLogAop       565 : requestInfo:{"path":"/teambox/using/uploadUsing/uploadFiles","ip":"************","requestMethod":"POST","contentType":"multipart/form-data; boundary=--------------------------125728513951509054058884","requestBody":false,"param":{"type":"file-microteam"},"time":"2025-08-04 16:25:12","token":"733871CC5FFA4115B3E3545AA8701E53"}
2025-08-04 16:25:14,779  INFO [http-nio-8889-exec-1] [] i.g.s.framework.log.aop.BaseLogAop       602 : responseResult:{"code":200,"success":true,"msg":"操作成功","data":["https://file-microteam.oss-cn-shenzhen.aliyuncs.com/MauritiusFootballBigData.apk"],"timestamp":1754295914776}
2025-08-04 16:25:14,822  INFO [http-nio-8889-exec-1] [] i.g.s.u.interceptor.LoginInterceptor     71 : responseSpeed: 2917ms
2025-08-04 17:02:13,469  INFO [SpringContextShutdownHook] [] o.s.s.c.ThreadPoolTaskScheduler          218 : Shutting down ExecutorService 'taskScheduler'
2025-08-04 17:02:13,471  INFO [SpringContextShutdownHook] [] o.s.s.c.ThreadPoolTaskScheduler          218 : Shutting down ExecutorService
2025-08-04 17:02:13,472  INFO [SpringContextShutdownHook] [] o.s.s.concurrent.ThreadPoolTaskExecutor  218 : Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-04 17:02:13,482  INFO [SpringContextShutdownHook] [] c.b.d.d.DynamicRoutingDataSource         225 : dynamic-datasource start closing ....
2025-08-04 17:02:13,485  INFO [SpringContextShutdownHook] [] com.alibaba.druid.pool.DruidDataSource   2003 : {dataSource-1} closing ...
2025-08-04 17:02:13,486  INFO [SpringContextShutdownHook] [] com.alibaba.druid.pool.DruidDataSource   2075 : {dataSource-1} closed
2025-08-04 17:02:13,487  INFO [SpringContextShutdownHook] [] com.alibaba.druid.pool.DruidDataSource   2003 : {dataSource-2} closing ...
2025-08-04 17:02:13,487  INFO [SpringContextShutdownHook] [] com.alibaba.druid.pool.DruidDataSource   2075 : {dataSource-2} closed
2025-08-04 17:02:13,487  INFO [SpringContextShutdownHook] [] c.b.d.d.DynamicRoutingDataSource         229 : dynamic-datasource all closed success,bye
2025-08-04 17:02:15,064 ERROR [pool-2-thread-3] [] i.g.s.framework.util.RedisUtils          200 : TeamPastDelayQueue

org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1197)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1178)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:942)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:353)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:134)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:97)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:84)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:215)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScore(DefaultZSetOperations.java:197)
	at org.springframework.data.redis.core.DefaultBoundZSetOperations.rangeByScore(DefaultBoundZSetOperations.java:140)
	at io.geekidea.springbootplus.framework.util.RedisUtils.zSGet(RedisUtils.java:198)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$FastClassBySpringCGLIB$$29751b48.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:73)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$EnhancerBySpringCGLIB$$14636201.zSGet(<generated>)
	at io.geekidea.springbootplus.using.service.impl.DelayQueueServiceImpl.lambda$0(DelayQueueServiceImpl.java:65)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:207)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$null$0(LettucePoolingConnectionProvider.java:97)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:209)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:199)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:58)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:899)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:429)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:354)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:103)
	... 28 common frames omitted
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:59)
	at io.netty.bootstrap.Bootstrap.doResolveAndConnect0(Bootstrap.java:192)
	at io.netty.bootstrap.Bootstrap.access$000(Bootstrap.java:46)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:180)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:166)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:551)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:604)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetSuccess(AbstractChannel.java:984)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:504)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:417)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:474)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted

2025-08-04 17:02:15,137 ERROR [pool-2-thread-1] [] i.g.s.framework.util.RedisUtils          200 : DrillPastDelayQueue

org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1197)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1178)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:942)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:353)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:134)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:97)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:84)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:215)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScore(DefaultZSetOperations.java:197)
	at org.springframework.data.redis.core.DefaultBoundZSetOperations.rangeByScore(DefaultBoundZSetOperations.java:140)
	at io.geekidea.springbootplus.framework.util.RedisUtils.zSGet(RedisUtils.java:198)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$FastClassBySpringCGLIB$$29751b48.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:73)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$EnhancerBySpringCGLIB$$14636201.zSGet(<generated>)
	at io.geekidea.springbootplus.using.service.impl.DelayQueueServiceImpl.lambda$0(DelayQueueServiceImpl.java:65)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:207)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$null$0(LettucePoolingConnectionProvider.java:97)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:209)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:199)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:58)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:899)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:429)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:354)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:103)
	... 28 common frames omitted
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:59)
	at io.netty.bootstrap.Bootstrap.doResolveAndConnect0(Bootstrap.java:192)
	at io.netty.bootstrap.Bootstrap.access$000(Bootstrap.java:46)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:180)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:166)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:551)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:604)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetSuccess(AbstractChannel.java:984)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:504)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:417)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:474)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted

2025-08-04 17:02:15,461 ERROR [pool-2-thread-2] [] i.g.s.framework.util.RedisUtils          200 : GamePastDelayQueue

org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1197)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1178)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:942)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:353)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:134)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:97)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:84)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:215)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScore(DefaultZSetOperations.java:197)
	at org.springframework.data.redis.core.DefaultBoundZSetOperations.rangeByScore(DefaultBoundZSetOperations.java:140)
	at io.geekidea.springbootplus.framework.util.RedisUtils.zSGet(RedisUtils.java:198)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$FastClassBySpringCGLIB$$29751b48.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:73)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$EnhancerBySpringCGLIB$$14636201.zSGet(<generated>)
	at io.geekidea.springbootplus.using.service.impl.DelayQueueServiceImpl.lambda$0(DelayQueueServiceImpl.java:65)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:207)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$null$0(LettucePoolingConnectionProvider.java:97)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:209)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:199)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:58)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:899)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:429)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:354)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:103)
	... 28 common frames omitted
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:59)
	at io.netty.bootstrap.Bootstrap.doResolveAndConnect0(Bootstrap.java:192)
	at io.netty.bootstrap.Bootstrap.access$000(Bootstrap.java:46)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:180)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:166)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:551)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:604)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetSuccess(AbstractChannel.java:984)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:504)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:417)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:474)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted

2025-08-04 17:02:16,739  INFO [SpringContextShutdownHook] [] i.g.s.framework.core.xss.XssFilter       48 : XssFilter destroy
2025-08-04 17:02:16,741  INFO [SpringContextShutdownHook] [] i.g.s.f.core.filter.RequestDetailFilter  62 : RequestDetailFilter destroy
2025-08-04 23:28:45,789  INFO [main] [] i.g.s.SpringBootPlusApplication          55 : Starting SpringBootPlusApplication on DESKTOP-37JJ82G with PID 4968 (C:\Users\<USER>\Desktop\microteam\back\work\teambox\bootstrap\target\classes started by WD in C:\Users\<USER>\Desktop\microteam\back\work\teambox)
2025-08-04 23:28:45,794  INFO [main] [] i.g.s.SpringBootPlusApplication          655 : The following profiles are active: dev
2025-08-04 23:28:47,496  INFO [main] [] .s.d.r.c.RepositoryConfigurationDelegate 249 : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 23:28:47,497  INFO [main] [] .s.d.r.c.RepositoryConfigurationDelegate 127 : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 23:28:47,626  INFO [main] [] .s.d.r.c.RepositoryConfigurationDelegate 187 : Finished Spring Data repository scanning in 118ms. Found 0 Redis repository interfaces.
2025-08-04 23:28:48,208  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 23:28:48,290  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c92f20e1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 23:28:48,298  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 23:28:48,302  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 23:28:48,336  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'springDaoMethodAspect' of type [io.geekidea.springbootplus.config.SpringDaoMethodAspect$$EnhancerBySpringCGLIB$$542e9f31] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 23:28:48,367  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'druidStatInterceptor' of type [com.alibaba.druid.support.spring.stat.DruidStatInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 23:28:48,373  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'druidStatPointcut' of type [org.springframework.aop.support.JdkRegexpMethodPointcut] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 23:28:48,375  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'druidStatAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 23:28:48,386  INFO [main] [] trationDelegate$BeanPostProcessorChecker 330 : Bean 'redisCacheConfig' of type [io.geekidea.springbootplus.config.RedisCacheConfig$$EnhancerBySpringCGLIB$$975d2340] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 23:28:48,878  INFO [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer  92 : Tomcat initialized with port(s): 8889 (http)
2025-08-04 23:28:48,887  INFO [main] [] o.a.coyote.http11.Http11NioProtocol      173 : Initializing ProtocolHandler ["http-nio-8889"]
2025-08-04 23:28:48,888  INFO [main] [] o.apache.catalina.core.StandardService   173 : Starting service [Tomcat]
2025-08-04 23:28:48,888  INFO [main] [] org.apache.catalina.core.StandardEngine  173 : Starting Servlet engine: [Apache Tomcat/9.0.31]
2025-08-04 23:28:49,005  INFO [main] [] o.a.c.c.C.[.[localhost].[/teambox]       173 : Initializing Spring embedded WebApplicationContext
2025-08-04 23:28:49,005  INFO [main] [] o.s.web.context.ContextLoader            284 : Root WebApplicationContext: initialization completed in 3138 ms
2025-08-04 23:28:49,664  INFO [main] [] com.alibaba.druid.pool.DruidDataSource   1010 : {dataSource-1,teambox} inited
2025-08-04 23:28:49,665  INFO [main] [] com.alibaba.druid.pool.DruidDataSource   1010 : {dataSource-2,vsteam} inited
2025-08-04 23:28:49,665  INFO [main] [] c.b.d.d.DynamicRoutingDataSource         128 : dynamic-datasource - load a datasource named [teambox] success
2025-08-04 23:28:49,665  INFO [main] [] c.b.d.d.DynamicRoutingDataSource         128 : dynamic-datasource - load a datasource named [vsteam] success
2025-08-04 23:28:49,666  INFO [main] [] c.b.d.d.DynamicRoutingDataSource         263 : dynamic-datasource initial loaded [2] datasource,primary datasource named [teambox]
2025-08-04 23:28:49,831  INFO [main] [] o.s.boot.web.servlet.RegistrationBean    110 : Filter webStatFilter was not registered (possibly already registered?)
2025-08-04 23:28:49,832  INFO [main] [] o.s.boot.web.servlet.RegistrationBean    110 : Servlet statViewServlet was not registered (possibly already registered?)
2025-08-04 23:28:49,833  INFO [main] [] o.s.b.a.e.web.ServletEndpointRegistrar   75 : Registered '/actuator/jolokia' to jolokia-actuator-endpoint
2025-08-04 23:28:49,849  INFO [main] [] i.g.s.framework.core.xss.XssFilter       36 : XssFilter init
2025-08-04 23:28:49,850  INFO [main] [] i.g.s.f.core.filter.RequestDetailFilter  39 : RequestDetailFilter init
2025-08-04 23:28:52,034  INFO [main] [] org.reflections.Reflections              228 : Reflections took 58 ms to scan 2 urls, producing 2 keys and 4 values 
2025-08-04 23:28:52,155  INFO [main] [] i.g.s.framework.shiro.util.JwtUtil       51 : {"audience":"web","expireSecond":36000,"issuer":"spring-boot-plus","redisCheck":true,"refreshToken":true,"refreshTokenCountdown":600,"saltCheck":true,"secret":"666666","singleLogin":false,"tokenName":"token"}
2025-08-04 23:28:55,299  INFO [main] [] i.g.s.u.s.impl.DelayQueueServiceImpl     55 : 开始监控延迟队列：DrillPastDelayQueue
2025-08-04 23:28:55,300  INFO [main] [] i.g.s.u.s.impl.DelayQueueServiceImpl     55 : 开始监控延迟队列：GamePastDelayQueue
2025-08-04 23:28:55,300  INFO [main] [] i.g.s.u.s.impl.DelayQueueServiceImpl     55 : 开始监控延迟队列：TeamPastDelayQueue
2025-08-04 23:28:55,301  INFO [main] [] i.g.s.u.s.impl.DelayQueueServiceImpl     80 : 开始监控学校延迟队列：kindergartenMonitorDelayQueue
2025-08-04 23:28:58,057  INFO [main] [] o.s.b.a.e.web.EndpointLinksResolver      58 : Exposing 15 endpoint(s) beneath base path '/actuator'
2025-08-04 23:28:58,159  INFO [main] [] pertySourcedRequestMappingHandlerMapping 69 : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-08-04 23:28:58,185  INFO [main] [] i.g.s.config.Swagger2Config              166 : swagger scan basePackages:null
2025-08-04 23:28:58,385  INFO [main] [] o.s.s.concurrent.ThreadPoolTaskExecutor  181 : Initializing ExecutorService 'applicationTaskExecutor'
2025-08-04 23:28:58,801  INFO [main] [] o.s.s.c.ThreadPoolTaskScheduler          181 : Initializing ExecutorService
2025-08-04 23:28:58,978  INFO [main] [] o.s.s.c.ThreadPoolTaskScheduler          181 : Initializing ExecutorService 'taskScheduler'
2025-08-04 23:28:59,079  INFO [main] [] d.s.w.p.DocumentationPluginsBootstrapper 160 : Context refreshed
2025-08-04 23:28:59,091  INFO [main] [] d.s.w.p.DocumentationPluginsBootstrapper 163 : Found 1 custom documentation plugin(s)
2025-08-04 23:28:59,134  INFO [main] [] s.d.s.w.s.ApiListingReferenceScanner     41 : Scanning for api listing references
2025-08-04 23:28:59,622  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: fieldRankUsingGET_1
2025-08-04 23:28:59,624  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findListByIdsUsingGET_1
2025-08-04 23:28:59,653  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getGroupFieldUsingPOST_1
2025-08-04 23:28:59,654  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getGrowUpDataUsingPOST_1
2025-08-04 23:28:59,654  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPersonalUsingGET_1
2025-08-04 23:28:59,656  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPhysicalAnalysisUsingGET_1
2025-08-04 23:28:59,658  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPlayerDataUsingGET_1
2025-08-04 23:28:59,659  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getStuByGroupIdAndFieldIdUsingGET_1
2025-08-04 23:28:59,660  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getStuFieldUsingPOST_1
2025-08-04 23:28:59,665  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getTeamUsingGET_1
2025-08-04 23:28:59,667  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: isSetStatusUsingGET_1
2025-08-04 23:28:59,668  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: moveDistanceRankingUsingGET_1
2025-08-04 23:28:59,672  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: teamRankUsingGET_1
2025-08-04 23:28:59,676  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: unstartCountUsingGET_1
2025-08-04 23:28:59,678  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: uploadDataUsingPOST_1
2025-08-04 23:28:59,716  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findByIdUsingGET_1
2025-08-04 23:28:59,716  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findByMacUsingGET_1
2025-08-04 23:28:59,717  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findMacUsingGET_1
2025-08-04 23:28:59,727  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: startHardwaresUsingPOST_1
2025-08-04 23:28:59,730  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: updateHardwareUsingPOST_1
2025-08-04 23:28:59,764  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getKindergartenMonitorPageListUsingPOST_1
2025-08-04 23:28:59,765  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPhysicalAnalysisUsingGET_2
2025-08-04 23:28:59,767  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getPlayerDataUsingGET_2
2025-08-04 23:28:59,769  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: moveDistanceRankingUsingGET_2
2025-08-04 23:28:59,778  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: recentlyUsingGET_1
2025-08-04 23:28:59,792  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: uploadDataUsingPOST_2
2025-08-04 23:28:59,817  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: addDemoMacQrcodeUsingPOST_1
2025-08-04 23:28:59,819  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: findMacByboxUsingGET_1
2025-08-04 23:28:59,850  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: deleteStudentInfoUsingPOST_1
2025-08-04 23:28:59,866  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: uploadFileUsingPOST_1
2025-08-04 23:28:59,880  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: updateVersionUsingPOST_1
2025-08-04 23:28:59,928  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: addTacticsBoardUsingPOST_1
2025-08-04 23:28:59,929  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: deleteTacticsBoardUsingPOST_1
2025-08-04 23:28:59,941  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getTacticsBoardPageListUsingPOST_1
2025-08-04 23:28:59,944  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: updateTacticsBoardUsingPOST_1
2025-08-04 23:28:59,984  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: getTeamUsingGET_2
2025-08-04 23:28:59,991  WARN [main] [] s.d.s.w.r.p.ParameterDataTypeReader      92 : Trying to infer dataType com.alibaba.fastjson.JSONObject
2025-08-04 23:28:59,993  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: startedCountUsingGET_1
2025-08-04 23:29:00,021  WARN [main] [] s.d.s.w.r.p.ParameterDataTypeReader      92 : Trying to infer dataType java.util.List<org.springframework.web.multipart.MultipartFile>
2025-08-04 23:29:00,055  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: loginStuUsingPOST_1
2025-08-04 23:29:00,067  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: checkPhoneExistUsingPOST_1
2025-08-04 23:29:00,068  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: checkVerificationCodeUsingPOST_1
2025-08-04 23:29:00,070  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: sendPhoneVerificationCodeUsingPOST_1
2025-08-04 23:29:00,078  INFO [main] [] .d.s.w.r.o.CachingOperationNameGenerator 40 : Generating unique operation named: writeOffUsingPOST_1
2025-08-04 23:29:00,139  INFO [main] [] o.a.coyote.http11.Http11NioProtocol      173 : Starting ProtocolHandler ["http-nio-8889"]
2025-08-04 23:29:00,183  INFO [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer  204 : Tomcat started on port(s): 8889 (http) with context path '/teambox'
2025-08-04 23:29:00,187  INFO [main] [] i.g.s.SpringBootPlusApplication          61 : Started SpringBootPlusApplication in 15.059 seconds (JVM running for 16.687)
2025-08-04 23:29:00,194  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        83 : projectFinalName : bootstrap-2.0
2025-08-04 23:29:00,194  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        84 : projectVersion : 2.0
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        85 : profileActive : dev
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        86 : contextPath : /teambox
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        87 : serverIp : localhost
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        88 : port : 8889
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        102 : Admin:   http://localhost:8887
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        103 : Home:    http://localhost:8889/teambox
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        104 : druid:   http://localhost:8889/teambox/druid
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        105 : Knife4j: http://localhost:8889/teambox/doc.html
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        106 : Swagger: http://localhost:8889/teambox/swagger-ui.html
2025-08-04 23:29:00,196  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        107 : spring-boot-plus project start success...........
2025-08-04 23:29:00,198  INFO [main] [] i.g.s.f.util.PrintApplicationInfo        109 : 
           _                _                       
          (_)              | |                      
 _ __ ___  _  ___ _ __ ___ | |_ ___  __ _ _ __ ___  
| '_ ` _ \| |/ __| '__/ _ \| __/ _ \/ _` | '_ ` _ \ 
| | | | | | | (__| | | (_) | ||  __/ (_| | | | | | |
|_| |_| |_|_|\___|_|  \___/ \__\___|\__,_|_| |_| |_|
2025-08-04 23:29:00,956  INFO [RMI TCP Connection(3)-*************] [] o.a.c.c.C.[.[localhost].[/teambox]       173 : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 23:29:00,957  INFO [RMI TCP Connection(3)-*************] [] o.s.web.servlet.DispatcherServlet        525 : Initializing Servlet 'dispatcherServlet'
2025-08-04 23:29:00,974  INFO [RMI TCP Connection(3)-*************] [] o.s.web.servlet.DispatcherServlet        547 : Completed initialization in 16 ms
2025-08-04 23:29:01,420  INFO [boundedElastic-1] [] io.lettuce.core.EpollProvider            68 : Starting without optional epoll library
2025-08-04 23:29:01,423  INFO [boundedElastic-1] [] io.lettuce.core.KqueueProvider           70 : Starting without optional kqueue library
2025-08-04 23:29:04,413  WARN [registrationTask1] [] d.c.b.a.c.r.ApplicationRegistrator       93 : Failed to register application as Application(name=spring-boot-plus, managementUrl=http://localhost:8889/teambox/actuator, healthUrl=http://localhost:8889/teambox/actuator/health, serviceUrl=http://localhost:8889/teambox) at spring-boot-admin ([http://localhost:8887/instances]): I/O error on POST request for "http://localhost:8887/instances": Connect to localhost:8887 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:8887 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-04 23:29:45,221  INFO [SpringContextShutdownHook] [] o.s.s.c.ThreadPoolTaskScheduler          218 : Shutting down ExecutorService 'taskScheduler'
2025-08-04 23:29:45,224  INFO [SpringContextShutdownHook] [] o.s.s.c.ThreadPoolTaskScheduler          218 : Shutting down ExecutorService
2025-08-04 23:29:45,225  INFO [SpringContextShutdownHook] [] o.s.s.concurrent.ThreadPoolTaskExecutor  218 : Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-04 23:29:45,232  INFO [SpringContextShutdownHook] [] c.b.d.d.DynamicRoutingDataSource         225 : dynamic-datasource start closing ....
2025-08-04 23:29:45,235  INFO [SpringContextShutdownHook] [] com.alibaba.druid.pool.DruidDataSource   2003 : {dataSource-1} closing ...
2025-08-04 23:29:45,238  INFO [SpringContextShutdownHook] [] com.alibaba.druid.pool.DruidDataSource   2075 : {dataSource-1} closed
2025-08-04 23:29:45,238  INFO [SpringContextShutdownHook] [] com.alibaba.druid.pool.DruidDataSource   2003 : {dataSource-2} closing ...
2025-08-04 23:29:45,239  INFO [SpringContextShutdownHook] [] com.alibaba.druid.pool.DruidDataSource   2075 : {dataSource-2} closed
2025-08-04 23:29:45,239  INFO [SpringContextShutdownHook] [] c.b.d.d.DynamicRoutingDataSource         229 : dynamic-datasource all closed success,bye
2025-08-04 23:29:45,585 ERROR [pool-1-thread-2] [] i.g.s.framework.util.RedisUtils          200 : GamePastDelayQueue

org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1197)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1178)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:942)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:353)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:134)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:97)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:84)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:215)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScore(DefaultZSetOperations.java:197)
	at org.springframework.data.redis.core.DefaultBoundZSetOperations.rangeByScore(DefaultBoundZSetOperations.java:140)
	at io.geekidea.springbootplus.framework.util.RedisUtils.zSGet(RedisUtils.java:198)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$FastClassBySpringCGLIB$$29751b48.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:73)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$EnhancerBySpringCGLIB$$636883fd.zSGet(<generated>)
	at io.geekidea.springbootplus.using.service.impl.DelayQueueServiceImpl.lambda$0(DelayQueueServiceImpl.java:65)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:207)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$null$0(LettucePoolingConnectionProvider.java:97)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:209)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:199)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:58)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:899)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:429)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:354)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:103)
	... 28 common frames omitted
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:59)
	at io.netty.bootstrap.Bootstrap.doResolveAndConnect0(Bootstrap.java:192)
	at io.netty.bootstrap.Bootstrap.access$000(Bootstrap.java:46)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:180)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:166)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:551)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:604)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetSuccess(AbstractChannel.java:984)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:504)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:417)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:474)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted

2025-08-04 23:29:45,683 ERROR [pool-1-thread-1] [] i.g.s.framework.util.RedisUtils          200 : DrillPastDelayQueue

org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1197)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1178)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:942)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:353)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:134)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:97)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:84)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:215)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScore(DefaultZSetOperations.java:197)
	at org.springframework.data.redis.core.DefaultBoundZSetOperations.rangeByScore(DefaultBoundZSetOperations.java:140)
	at io.geekidea.springbootplus.framework.util.RedisUtils.zSGet(RedisUtils.java:198)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$FastClassBySpringCGLIB$$29751b48.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:73)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$EnhancerBySpringCGLIB$$636883fd.zSGet(<generated>)
	at io.geekidea.springbootplus.using.service.impl.DelayQueueServiceImpl.lambda$0(DelayQueueServiceImpl.java:65)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:207)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$null$0(LettucePoolingConnectionProvider.java:97)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:209)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:199)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:58)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:899)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:429)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:354)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:103)
	... 28 common frames omitted
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:59)
	at io.netty.bootstrap.Bootstrap.doResolveAndConnect0(Bootstrap.java:192)
	at io.netty.bootstrap.Bootstrap.access$000(Bootstrap.java:46)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:180)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:166)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:551)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:604)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetSuccess(AbstractChannel.java:984)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:504)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:417)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:474)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted

2025-08-04 23:29:45,793 ERROR [pool-1-thread-3] [] i.g.s.framework.util.RedisUtils          200 : TeamPastDelayQueue

org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1197)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1178)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:942)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:353)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:134)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:97)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:84)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:215)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScore(DefaultZSetOperations.java:197)
	at org.springframework.data.redis.core.DefaultBoundZSetOperations.rangeByScore(DefaultBoundZSetOperations.java:140)
	at io.geekidea.springbootplus.framework.util.RedisUtils.zSGet(RedisUtils.java:198)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$FastClassBySpringCGLIB$$29751b48.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:73)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$EnhancerBySpringCGLIB$$636883fd.zSGet(<generated>)
	at io.geekidea.springbootplus.using.service.impl.DelayQueueServiceImpl.lambda$0(DelayQueueServiceImpl.java:65)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:207)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$null$0(LettucePoolingConnectionProvider.java:97)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:209)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:199)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:58)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:899)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:429)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:354)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:103)
	... 28 common frames omitted
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:59)
	at io.netty.bootstrap.Bootstrap.doResolveAndConnect0(Bootstrap.java:192)
	at io.netty.bootstrap.Bootstrap.access$000(Bootstrap.java:46)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:180)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:166)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:551)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:604)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetSuccess(AbstractChannel.java:984)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:504)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:417)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:474)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted

2025-08-04 23:29:47,648 ERROR [pool-1-thread-2] [] i.g.s.framework.util.RedisUtils          200 : GamePastDelayQueue

org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1197)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1178)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:942)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:353)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:134)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:97)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:84)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:215)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScore(DefaultZSetOperations.java:197)
	at org.springframework.data.redis.core.DefaultBoundZSetOperations.rangeByScore(DefaultBoundZSetOperations.java:140)
	at io.geekidea.springbootplus.framework.util.RedisUtils.zSGet(RedisUtils.java:198)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$FastClassBySpringCGLIB$$29751b48.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:73)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$EnhancerBySpringCGLIB$$636883fd.zSGet(<generated>)
	at io.geekidea.springbootplus.using.service.impl.DelayQueueServiceImpl.lambda$0(DelayQueueServiceImpl.java:65)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:207)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$null$0(LettucePoolingConnectionProvider.java:97)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:209)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:199)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:58)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:899)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:429)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:354)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:103)
	... 28 common frames omitted
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:59)
	at io.netty.bootstrap.Bootstrap.doResolveAndConnect0(Bootstrap.java:192)
	at io.netty.bootstrap.Bootstrap.access$000(Bootstrap.java:46)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:180)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:166)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:551)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:604)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetSuccess(AbstractChannel.java:984)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:504)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:417)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:474)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted

2025-08-04 23:29:47,756 ERROR [pool-1-thread-1] [] i.g.s.framework.util.RedisUtils          200 : DrillPastDelayQueue

org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1197)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1178)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:942)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:353)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:134)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:97)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:84)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:215)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScore(DefaultZSetOperations.java:197)
	at org.springframework.data.redis.core.DefaultBoundZSetOperations.rangeByScore(DefaultBoundZSetOperations.java:140)
	at io.geekidea.springbootplus.framework.util.RedisUtils.zSGet(RedisUtils.java:198)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$FastClassBySpringCGLIB$$29751b48.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:73)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$EnhancerBySpringCGLIB$$636883fd.zSGet(<generated>)
	at io.geekidea.springbootplus.using.service.impl.DelayQueueServiceImpl.lambda$0(DelayQueueServiceImpl.java:65)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:207)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$null$0(LettucePoolingConnectionProvider.java:97)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:209)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:199)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:58)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:899)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:429)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:354)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:103)
	... 28 common frames omitted
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:59)
	at io.netty.bootstrap.Bootstrap.doResolveAndConnect0(Bootstrap.java:192)
	at io.netty.bootstrap.Bootstrap.access$000(Bootstrap.java:46)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:180)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:166)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:551)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:604)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetSuccess(AbstractChannel.java:984)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:504)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:417)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:474)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted

2025-08-04 23:29:47,867 ERROR [pool-1-thread-3] [] i.g.s.framework.util.RedisUtils          200 : TeamPastDelayQueue

org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:109)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1197)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1178)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:942)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:353)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:134)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:97)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:84)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:215)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultZSetOperations.rangeByScore(DefaultZSetOperations.java:197)
	at org.springframework.data.redis.core.DefaultBoundZSetOperations.rangeByScore(DefaultBoundZSetOperations.java:140)
	at io.geekidea.springbootplus.framework.util.RedisUtils.zSGet(RedisUtils.java:198)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$FastClassBySpringCGLIB$$29751b48.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:73)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at io.geekidea.springbootplus.framework.util.RedisUtils$$EnhancerBySpringCGLIB$$636883fd.zSGet(<generated>)
	at io.geekidea.springbootplus.using.service.impl.DelayQueueServiceImpl.lambda$0(DelayQueueServiceImpl.java:65)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 192.168.2.253:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:234)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:207)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$null$0(LettucePoolingConnectionProvider.java:97)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:209)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:199)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:58)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:899)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:429)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:354)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:103)
	... 28 common frames omitted
Caused by: java.lang.IllegalStateException: executor not accepting a task
	at io.netty.resolver.AddressResolverGroup.getResolver(AddressResolverGroup.java:59)
	at io.netty.bootstrap.Bootstrap.doResolveAndConnect0(Bootstrap.java:192)
	at io.netty.bootstrap.Bootstrap.access$000(Bootstrap.java:46)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:180)
	at io.netty.bootstrap.Bootstrap$1.operationComplete(Bootstrap.java:166)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:577)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:551)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:490)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:615)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:604)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:104)
	at io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetSuccess(AbstractChannel.java:984)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.register0(AbstractChannel.java:504)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.access$200(AbstractChannel.java:417)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$1.run(AbstractChannel.java:474)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted

2025-08-04 23:29:48,460  INFO [SpringContextShutdownHook] [] i.g.s.framework.core.xss.XssFilter       48 : XssFilter destroy
2025-08-04 23:29:48,460  INFO [SpringContextShutdownHook] [] i.g.s.f.core.filter.RequestDetailFilter  62 : RequestDetailFilter destroy
