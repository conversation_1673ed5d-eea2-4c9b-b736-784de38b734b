<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright 2019-2029 geekidea(https://github.com/geekidea)
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.geekidea.springbootplus</groupId>
        <artifactId>parent</artifactId>
        <version>2.0</version>
    </parent>

    <artifactId>distribution</artifactId>
    <name>distribution</name>
    <description>项目打包模块</description>

    <profiles>
        <profile>
            <id>release</id>
            <dependencies>
                <dependency>
                    <groupId>io.geekidea.springbootplus</groupId>
                    <artifactId>bootstrap</artifactId>
                </dependency>
            </dependencies>

            <build>
                <resources>
                    <resource>
                        <directory>bin</directory>
                        <filtering>true</filtering>
                        <targetPath>bin</targetPath>
                    </resource>
                </resources>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <version>${maven-assembly-plugin.version}</version>
                        <configuration>
                            <finalName>${assembly.name}</finalName>
                            <descriptors>
                                <descriptor>release.xml</descriptor>
                            </descriptors>
                            <appendAssemblyId>false</appendAssemblyId>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>release-admin</id>
            <build>
                <resources>
                    <resource>
                        <directory>admin</directory>
                        <filtering>true</filtering>
                        <targetPath>admin</targetPath>
                    </resource>
                </resources>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <version>${maven-assembly-plugin.version}</version>
                        <configuration>
                            <finalName>${assembly.name}-admin</finalName>
                            <descriptors>
                                <descriptor>release-admin.xml</descriptor>
                            </descriptors>
                            <appendAssemblyId>false</appendAssemblyId>
                        </configuration>
                        <executions>
                            <execution>
                                <id>make-assembly</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
